{% extends "base.html" %}

{% block title %}إدارة الأنسولين الشاملة - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0 fw-bold">
                            <i class="mdi mdi-needle me-3"></i>إدارة الأنسولين الشاملة
                        </h3>
                        <div>
                            <a href="/insulin/dispense" class="btn btn-light me-2">
                                <i class="mdi mdi-cart me-1"></i>منصرف الأنسولين
                            </a>
                            <a href="/" class="btn btn-light">
                                <i class="mdi mdi-home me-1"></i>الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-barcode display-4"></i>
                                    <h4>{{ insulin_codes_count }}</h4>
                                    <p class="mb-0">تكويدات الأنسولين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-format-list-bulleted display-4"></i>
                                    <h4>{{ insulin_types_count }}</h4>
                                    <p class="mb-0">أنواع الأنسولين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-tag display-4"></i>
                                    <h4>{{ insulin_categories_count }}</h4>
                                    <p class="mb-0">فئات الأنسولين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-needle display-4"></i>
                                    <h4>{{ total_dispensed }}</h4>
                                    <p class="mb-0">إجمالي المنصرف</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويبات الإدارة -->
                    <ul class="nav nav-tabs nav-justified mb-4" id="managementTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="codes-tab" data-bs-toggle="tab" data-bs-target="#codes" type="button" role="tab">
                                <i class="mdi mdi-barcode me-2"></i>تكويد الأنسولين
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="types-tab" data-bs-toggle="tab" data-bs-target="#types" type="button" role="tab">
                                <i class="mdi mdi-format-list-bulleted me-2"></i>أنواع الأنسولين
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab">
                                <i class="mdi mdi-tag me-2"></i>فئات الأنسولين
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="managementTabsContent">
                        
                        <!-- تبويب تكويد الأنسولين -->
                        <div class="tab-pane fade show active" id="codes" role="tabpanel">
                            <div class="row">
                                <!-- نموذج إضافة تكويد جديد -->
                                <div class="col-md-4">
                                    <div class="card shadow-sm">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0">
                                                <i class="mdi mdi-plus-circle me-2"></i>إضافة تكويد جديد
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" action="/manage/insulin_codes" id="addCodeForm">
                                                <input type="hidden" name="action" value="add_code">
                                                <div class="mb-3">
                                                    <label for="code" class="form-label">الكود</label>
                                                    <input type="number" class="form-control" id="code" name="code" value="{{ next_code }}" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="name" class="form-label">اسم التكويد</label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="unit" class="form-label">الوحدة</label>
                                                    <select class="form-select" id="unit" name="unit" required>
                                                        <option value="">-- اختر الوحدة --</option>
                                                        <option value="فيال">فيال</option>
                                                        <option value="قلم">قلم</option>
                                                        <option value="خرطوشة">خرطوشة</option>
                                                        <option value="علبة">علبة</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="type" class="form-label">النوع</label>
                                                    <select class="form-select" id="type" name="type" required>
                                                        <option value="">-- اختر النوع --</option>
                                                        <option value="سريع المفعول">سريع المفعول</option>
                                                        <option value="متوسط المفعول">متوسط المفعول</option>
                                                        <option value="طويل المفعول">طويل المفعول</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="description" class="form-label">الوصف (اختياري)</label>
                                                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                                </div>
                                                <button type="submit" class="btn btn-primary w-100">
                                                    <i class="mdi mdi-content-save me-1"></i>حفظ التكويد
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- قائمة التكويدات -->
                                <div class="col-md-8">
                                    <div class="card shadow-sm">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">
                                                    <i class="mdi mdi-format-list-bulleted me-2"></i>قائمة التكويدات
                                                </h5>
                                                <div class="input-group" style="width: 250px;">
                                                    <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                                                    <input type="text" id="searchCodes" class="form-control" placeholder="بحث في التكويدات...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            {% if insulin_codes %}
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover" id="codesTable">
                                                    <thead class="table-primary">
                                                        <tr class="text-center">
                                                            <th>#</th>
                                                            <th>الكود</th>
                                                            <th>الاسم</th>
                                                            <th>الوحدة</th>
                                                            <th>النوع</th>
                                                            <th>الوصف</th>
                                                            <th>الإجراءات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for code in insulin_codes %}
                                                        <tr>
                                                            <td class="text-center">{{ loop.index }}</td>
                                                            <td class="text-center"><span class="badge bg-primary">{{ code.code }}</span></td>
                                                            <td><strong>{{ code.name }}</strong></td>
                                                            <td class="text-center">
                                                                <span class="badge bg-info">{{ code.unit or '-' }}</span>
                                                            </td>
                                                            <td class="text-center">
                                                                <span class="badge bg-success">{{ code.type or '-' }}</span>
                                                            </td>
                                                            <td>{{ code.description or '-' }}</td>
                                                            <td class="text-center">
                                                                <div class="btn-group">
                                                                    <button type="button" class="btn btn-sm btn-warning edit-code"
                                                                            data-id="{{ code.id }}"
                                                                            data-code="{{ code.code }}"
                                                                            data-name="{{ code.name }}"
                                                                            data-unit="{{ code.unit or '' }}"
                                                                            data-type="{{ code.type or '' }}"
                                                                            data-description="{{ code.description or '' }}">
                                                                        <i class="mdi mdi-pencil"></i>
                                                                    </button>
                                                                    <form method="POST" action="/manage/insulin_codes/{{ code.id }}/delete" class="d-inline"
                                                                          onsubmit="return confirm('هل أنت متأكد من حذف هذا التكويد؟');">
                                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                                            <i class="mdi mdi-delete"></i>
                                                                        </button>
                                                                    </form>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            {% else %}
                                            <div class="alert alert-info text-center">
                                                <i class="mdi mdi-information me-2"></i>لا توجد تكويدات أنسولين مسجلة حالياً
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب أنواع الأنسولين -->
                        <div class="tab-pane fade" id="types" role="tabpanel">
                            <div class="row">
                                <!-- نموذج إضافة نوع جديد -->
                                <div class="col-md-4">
                                    <div class="card shadow-sm">
                                        <div class="card-header bg-info text-white">
                                            <h5 class="mb-0">
                                                <i class="mdi mdi-plus-circle me-2"></i>إضافة نوع جديد
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" action="/manage/insulin_types" id="addTypeForm">
                                                <input type="hidden" name="action" value="add_type">
                                                <div class="mb-3">
                                                    <label for="type_name" class="form-label">اسم النوع</label>
                                                    <input type="text" class="form-control" id="type_name" name="type_name" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="type_description" class="form-label">الوصف (اختياري)</label>
                                                    <textarea class="form-control" id="type_description" name="type_description" rows="3"></textarea>
                                                </div>
                                                <button type="submit" class="btn btn-info w-100">
                                                    <i class="mdi mdi-content-save me-1"></i>حفظ النوع
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- قائمة الأنواع -->
                                <div class="col-md-8">
                                    <div class="card shadow-sm">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">
                                                    <i class="mdi mdi-format-list-bulleted me-2"></i>قائمة الأنواع
                                                </h5>
                                                <div class="input-group" style="width: 250px;">
                                                    <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                                                    <input type="text" id="searchTypes" class="form-control" placeholder="بحث في الأنواع...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            {% if insulin_types %}
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover" id="typesTable">
                                                    <thead class="table-info">
                                                        <tr class="text-center">
                                                            <th>#</th>
                                                            <th>اسم النوع</th>
                                                            <th>الوصف</th>
                                                            <th>الإجراءات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for type in insulin_types %}
                                                        <tr>
                                                            <td class="text-center">{{ loop.index }}</td>
                                                            <td><strong>{{ type.name }}</strong></td>
                                                            <td>{{ type.description or '-' }}</td>
                                                            <td class="text-center">
                                                                <div class="btn-group">
                                                                    <button type="button" class="btn btn-sm btn-warning edit-type"
                                                                            data-id="{{ type.id }}"
                                                                            data-name="{{ type.name }}"
                                                                            data-description="{{ type.description or '' }}">
                                                                        <i class="mdi mdi-pencil"></i>
                                                                    </button>
                                                                    <form method="POST" action="/manage/insulin_types/{{ type.id }}/delete" class="d-inline" 
                                                                          onsubmit="return confirm('هل أنت متأكد من حذف هذا النوع؟');">
                                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                                            <i class="mdi mdi-delete"></i>
                                                                        </button>
                                                                    </form>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            {% else %}
                                            <div class="alert alert-info text-center">
                                                <i class="mdi mdi-information me-2"></i>لا توجد أنواع أنسولين مسجلة حالياً
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب فئات الأنسولين -->
                        <div class="tab-pane fade" id="categories" role="tabpanel">
                            <div class="row">
                                <!-- نموذج إضافة فئة جديدة -->
                                <div class="col-md-4">
                                    <div class="card shadow-sm">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0">
                                                <i class="mdi mdi-plus-circle me-2"></i>إضافة فئة جديدة
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" action="/manage/insulin_categories" id="addCategoryForm">
                                                <input type="hidden" name="action" value="add_category">
                                                <div class="mb-3">
                                                    <label for="category_name" class="form-label">اسم الفئة</label>
                                                    <input type="text" class="form-control" id="category_name" name="category_name" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="category_description" class="form-label">الوصف (اختياري)</label>
                                                    <textarea class="form-control" id="category_description" name="category_description" rows="3"></textarea>
                                                </div>
                                                <button type="submit" class="btn btn-success w-100">
                                                    <i class="mdi mdi-content-save me-1"></i>حفظ الفئة
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- قائمة الفئات -->
                                <div class="col-md-8">
                                    <div class="card shadow-sm">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">
                                                    <i class="mdi mdi-format-list-bulleted me-2"></i>قائمة الفئات
                                                </h5>
                                                <div class="input-group" style="width: 250px;">
                                                    <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                                                    <input type="text" id="searchCategories" class="form-control" placeholder="بحث في الفئات...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            {% if insulin_categories %}
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover" id="categoriesTable">
                                                    <thead class="table-success">
                                                        <tr class="text-center">
                                                            <th>#</th>
                                                            <th>اسم الفئة</th>
                                                            <th>الوصف</th>
                                                            <th>الإجراءات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for category in insulin_categories %}
                                                        <tr>
                                                            <td class="text-center">{{ loop.index }}</td>
                                                            <td><strong>{{ category.name }}</strong></td>
                                                            <td>{{ category.description or '-' }}</td>
                                                            <td class="text-center">
                                                                <div class="btn-group">
                                                                    <button type="button" class="btn btn-sm btn-warning edit-category"
                                                                            data-id="{{ category.id }}"
                                                                            data-name="{{ category.name }}"
                                                                            data-description="{{ category.description or '' }}">
                                                                        <i class="mdi mdi-pencil"></i>
                                                                    </button>
                                                                    <form method="POST" action="/manage/insulin_categories/{{ category.id }}/delete" class="d-inline" 
                                                                          onsubmit="return confirm('هل أنت متأكد من حذف هذه الفئة؟');">
                                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                                            <i class="mdi mdi-delete"></i>
                                                                        </button>
                                                                    </form>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            {% else %}
                                            <div class="alert alert-info text-center">
                                                <i class="mdi mdi-information me-2"></i>لا توجد فئات أنسولين مسجلة حالياً
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نماذج التعديل المنبثقة -->

<!-- نموذج تعديل التكويد -->
<div class="modal fade" id="editCodeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="mdi mdi-pencil me-2"></i>تعديل تكويد الأنسولين
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/manage/insulin_codes/update">
                <div class="modal-body">
                    <input type="hidden" id="edit_code_id" name="code_id">
                    <div class="mb-3">
                        <label for="edit_code" class="form-label">الكود</label>
                        <input type="number" class="form-control" id="edit_code" name="code" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم التكويد</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_unit" class="form-label">الوحدة</label>
                        <select class="form-select" id="edit_unit" name="unit" required>
                            <option value="">-- اختر الوحدة --</option>
                            <option value="فيال">فيال</option>
                            <option value="قلم">قلم</option>
                            <option value="خرطوشة">خرطوشة</option>
                            <option value="علبة">علبة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_type" class="form-label">النوع</label>
                        <select class="form-select" id="edit_type" name="type" required>
                            <option value="">-- اختر النوع --</option>
                            <option value="سريع المفعول">سريع المفعول</option>
                            <option value="متوسط المفعول">متوسط المفعول</option>
                            <option value="طويل المفعول">طويل المفعول</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل النوع -->
<div class="modal fade" id="editTypeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="mdi mdi-pencil me-2"></i>تعديل نوع الأنسولين
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/manage/insulin_types/update">
                <div class="modal-body">
                    <input type="hidden" id="edit_type_id" name="type_id">
                    <div class="mb-3">
                        <label for="edit_type_name" class="form-label">اسم النوع</label>
                        <input type="text" class="form-control" id="edit_type_name" name="type_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_type_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_type_description" name="type_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل الفئة -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="mdi mdi-pencil me-2"></i>تعديل فئة الأنسولين
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/manage/insulin_categories/update">
                <div class="modal-body">
                    <input type="hidden" id="edit_category_id" name="category_id">
                    <div class="mb-3">
                        <label for="edit_category_name" class="form-label">اسم الفئة</label>
                        <input type="text" class="form-control" id="edit_category_name" name="category_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_category_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_category_description" name="category_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // وظائف البحث
    $('#searchCodes').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#codesTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    $('#searchTypes').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#typesTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    $('#searchCategories').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#categoriesTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // أزرار التعديل
    $('.edit-code').on('click', function() {
        var id = $(this).data('id');
        var code = $(this).data('code');
        var name = $(this).data('name');
        var unit = $(this).data('unit');
        var type = $(this).data('type');
        var description = $(this).data('description');

        $('#edit_code_id').val(id);
        $('#edit_code').val(code);
        $('#edit_name').val(name);
        $('#edit_unit').val(unit);
        $('#edit_type').val(type);
        $('#edit_description').val(description);

        $('#editCodeModal').modal('show');
    });

    $('.edit-type').on('click', function() {
        var id = $(this).data('id');
        var name = $(this).data('name');
        var description = $(this).data('description');

        $('#edit_type_id').val(id);
        $('#edit_type_name').val(name);
        $('#edit_type_description').val(description);

        $('#editTypeModal').modal('show');
    });

    $('.edit-category').on('click', function() {
        var id = $(this).data('id');
        var name = $(this).data('name');
        var description = $(this).data('description');

        $('#edit_category_id').val(id);
        $('#edit_category_name').val(name);
        $('#edit_category_description').val(description);

        $('#editCategoryModal').modal('show');
    });

    // تأثيرات بصرية للنماذج
    $('form').on('submit', function() {
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.html('<i class="mdi mdi-loading mdi-spin me-1"></i>جاري الحفظ...');
        submitBtn.prop('disabled', true);

        // إعادة تفعيل الزر بعد ثانيتين في حالة عدم إعادة تحميل الصفحة
        setTimeout(function() {
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
        }, 2000);
    });

    // تحديث الكود التالي تلقائياً
    $('#addCodeForm').on('submit', function() {
        var currentCode = parseInt($('#code').val());
        $('#code').val(currentCode + 1);
    });
});
</script>
{% endblock %}
