{% extends "base.html" %}

{% block title %}إدارة الأنسولين - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="mdi mdi-needle me-2"></i>إدارة الأنسولين
                    </h4>
                    <a href="/manage/insulin_codes" class="btn btn-light btn-add">
                        <i class="mdi mdi-barcode me-1"></i>إدارة تكويد الأنسولين
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="POST" action="/manage/insulin">
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="mdi mdi-plus-circle me-2"></i>إضافة أنسولين جديد</h5>
                                        <span class="badge bg-primary">منصرف الأنسولين</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">اسم الأنسولين</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="type" class="form-label">النوع</label>
                                        <select class="form-select" id="type" name="type" required>
                                            <option value="">-- اختر النوع --</option>
                                            <option value="سريع المفعول">سريع المفعول</option>
                                            <option value="متوسط المفعول">متوسط المفعول</option>
                                            <option value="طويل المفعول">طويل المفعول</option>
                                            <option value="مختلط">مختلط</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="cases_count" class="form-label">عدد الحالات</label>
                                        <input type="number" min="1" class="form-control" id="cases_count" name="cases_count" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="quantity" class="form-label">الكمية</label>
                                        <input type="number" step="0.01" min="0.01" class="form-control" id="quantity" name="quantity" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="price" class="form-label">السعر</label>
                                        <input type="number" step="0.01" min="0.01" class="form-control" id="price" name="price" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="clinic_id" class="form-label">العيادة</label>
                                        <select class="form-select" id="clinic_id" name="clinic_id" required>
                                            <option value="">-- اختر العيادة --</option>
                                            {% for clinic in clinics %}
                                            <option value="{{ clinic.id }}">{{ clinic.name }} ({{ clinic.area_name }} - {{ clinic.branch_name }})</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="dispense_month" class="form-label">شهر الصرف</label>
                                        <input type="month" class="form-control" id="dispense_month" name="dispense_month" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="insulin_code_id" class="form-label">تكويد الأنسولين (اختياري)</label>
                                        <select class="form-select" id="insulin_code_id" name="insulin_code_id">
                                            <option value="">-- بدون تكويد --</option>
                                            {% for code in insulin_codes %}
                                            <option value="{{ code.id }}">{{ code.code }} - {{ code.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="mdi mdi-plus-circle me-1"></i>إضافة
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <div class="card shadow-sm h-100">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="mdi mdi-link-variant me-2"></i>روابط سريعة</h5>
                                    <span class="badge bg-info">الإدارة</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body p-3">
                                                <h5 class="card-title"><i class="mdi mdi-needle me-2"></i>الأنسولين</h5>
                                                <div class="list-group list-group-flush mt-3">
                                                    <a href="/manage/insulin_codes" class="list-group-item list-group-item-action">
                                                        <i class="mdi mdi-barcode me-2"></i>تكويد الأنسولين
                                                    </a>
                                                    <a href="/manage/insulin" class="list-group-item list-group-item-action bg-light text-primary fw-bold">
                                                        <i class="mdi mdi-needle me-2"></i>منصرف الأنسولين
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-success text-white">
                                            <div class="card-body p-3">
                                                <h5 class="card-title"><i class="mdi mdi-pill me-2"></i>الأدوية</h5>
                                                <div class="list-group list-group-flush mt-3">
                                                    <a href="/manage/drugs" class="list-group-item list-group-item-action">
                                                        <i class="mdi mdi-pill me-2"></i>إدارة الأدوية
                                                    </a>
                                                    <a href="/dispense" class="list-group-item list-group-item-action">
                                                        <i class="mdi mdi-cart me-2"></i>صرف الأدوية
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 text-center mt-3">
                                        <a href="/" class="btn btn-outline-primary btn-lg w-100">
                                            <i class="mdi mdi-home me-2"></i>العودة للصفحة الرئيسية
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0"><i class="mdi mdi-format-list-bulleted me-2"></i>قائمة منصرف الأنسولين</h5>
                            </div>
                            <div class="col-auto">
                                <div class="input-group">
                                    <span class="input-group-text bg-light"><i class="mdi mdi-magnify"></i></span>
                                    <input type="text" id="searchInsulin" class="form-control" placeholder="بحث...">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if insulin_items %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-bordered" id="insulinTable">
                                <thead class="table-primary">
                                    <tr class="text-center">
                                        <th>#</th>
                                        <th>اسم الأنسولين</th>
                                        <th>النوع</th>
                                        <th>عدد الحالات</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>التكلفة</th>
                                        <th>العيادة</th>
                                        <th>شهر الصرف</th>
                                        <th>التكويد</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in insulin_items %}
                                    <tr>
                                        <td class="text-center">{{ loop.index }}</td>
                                        <td class="fw-bold">{{ item.name }}</td>
                                        <td class="text-center">
                                            {% if item.type == 'سريع المفعول' %}
                                                <span class="badge bg-danger">{{ item.type }}</span>
                                            {% elif item.type == 'متوسط المفعول' %}
                                                <span class="badge bg-warning text-dark">{{ item.type }}</span>
                                            {% elif item.type == 'طويل المفعول' %}
                                                <span class="badge bg-success">{{ item.type }}</span>
                                            {% else %}
                                                <span class="badge bg-info">{{ item.type }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center fw-bold">{{ item.cases_count }}</td>
                                        <td class="text-center">{{ item.quantity }}</td>
                                        <td class="text-center">{{ item.price }}</td>
                                        <td class="text-center fw-bold text-success">{{ (item.quantity * item.price)|round(2) }}</td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span>{{ item.clinic_name }}</span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-primary">{{ item.dispense_month }}</span>
                                        </td>
                                        <td class="text-center">
                                            {% if item.code_value %}
                                            <span class="badge bg-info">{{ item.code_value }} - {{ item.code_name }}</span>
                                            {% else %}
                                            <span class="badge bg-secondary">بدون تكويد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-primary rounded-circle me-1 edit-insulin"
                                                        data-id="{{ item.id }}"
                                                        data-name="{{ item.name }}"
                                                        data-type="{{ item.type }}"
                                                        data-cases-count="{{ item.cases_count }}"
                                                        data-quantity="{{ item.quantity }}"
                                                        data-price="{{ item.price }}"
                                                        data-clinic-id="{{ item.clinic_id }}"
                                                        data-dispense-month="{{ item.dispense_month }}"
                                                        data-insulin-code-id="{{ item.insulin_code_id or '' }}">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>
                                                <form method="POST" action="/manage/insulin/{{ item.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الأنسولين؟');">
                                                    <button type="submit" class="btn btn-sm btn-danger rounded-circle">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="mdi mdi-information me-2"></i>لا توجد أنسولين مسجل حالياً.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل الأنسولين -->
<div class="modal fade" id="editInsulinModal" tabindex="-1" aria-labelledby="editInsulinModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editInsulinModalLabel">
                    <i class="mdi mdi-pencil me-1"></i>تعديل بيانات الأنسولين
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/manage/insulin/update">
                <div class="modal-body">
                    <input type="hidden" id="edit_insulin_id" name="insulin_id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                                <label for="edit_name">اسم الأنسولين</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="edit_type" name="type" required>
                                    <option value="">-- اختر النوع --</option>
                                    <option value="سريع المفعول">سريع المفعول</option>
                                    <option value="متوسط المفعول">متوسط المفعول</option>
                                    <option value="طويل المفعول">طويل المفعول</option>
                                    <option value="مختلط">مختلط</option>
                                </select>
                                <label for="edit_type">النوع</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="number" min="1" class="form-control" id="edit_cases_count" name="cases_count" required>
                                <label for="edit_cases_count">عدد الحالات</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="number" step="0.01" min="0.01" class="form-control" id="edit_quantity" name="quantity" required>
                                <label for="edit_quantity">الكمية</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="number" step="0.01" min="0.01" class="form-control" id="edit_price" name="price" required>
                                <label for="edit_price">السعر</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="edit_clinic_id" name="clinic_id" required>
                                    <option value="">-- اختر العيادة --</option>
                                    {% for clinic in clinics %}
                                    <option value="{{ clinic.id }}">{{ clinic.name }} ({{ clinic.area_name }} - {{ clinic.branch_name }})</option>
                                    {% endfor %}
                                </select>
                                <label for="edit_clinic_id">العيادة</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="month" class="form-control" id="edit_dispense_month" name="dispense_month" required>
                                <label for="edit_dispense_month">شهر الصرف</label>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-floating">
                                <select class="form-select" id="edit_insulin_code_id" name="insulin_code_id">
                                    <option value="">-- بدون تكويد --</option>
                                    {% for code in insulin_codes %}
                                    <option value="{{ code.id }}">{{ code.code }} - {{ code.name }}</option>
                                    {% endfor %}
                                </select>
                                <label for="edit_insulin_code_id">تكويد الأنسولين (اختياري)</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-content-save me-1"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // البحث في جدول الأنسولين
        $("#searchInsulin").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $("#insulinTable tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });

        // تعيين التاريخ الحالي كقيمة افتراضية لشهر الصرف
        var today = new Date();
        var year = today.getFullYear();
        var month = (today.getMonth() + 1).toString().padStart(2, '0');
        $('#dispense_month').val(year + '-' + month);

        // تفعيل نموذج تعديل الأنسولين
        $('.edit-insulin').on('click', function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var type = $(this).data('type');
            var casesCount = $(this).data('cases-count');
            var quantity = $(this).data('quantity');
            var price = $(this).data('price');
            var clinicId = $(this).data('clinic-id');
            var dispenseMonth = $(this).data('dispense-month');
            var insulinCodeId = $(this).data('insulin-code-id');

            $('#edit_insulin_id').val(id);
            $('#edit_name').val(name);
            $('#edit_type').val(type);
            $('#edit_cases_count').val(casesCount);
            $('#edit_quantity').val(quantity);
            $('#edit_price').val(price);
            $('#edit_clinic_id').val(clinicId);
            $('#edit_dispense_month').val(dispenseMonth);
            $('#edit_insulin_code_id').val(insulinCodeId);

            $('#editInsulinModal').modal('show');
        });

        // حساب التكلفة عند تغيير الكمية أو السعر
        $('#quantity, #price, #edit_quantity, #edit_price').on('input', function() {
            var formId = $(this).attr('id').startsWith('edit_') ? 'edit_' : '';
            var quantity = parseFloat($('#' + formId + 'quantity').val()) || 0;
            var price = parseFloat($('#' + formId + 'price').val()) || 0;
            var cost = quantity * price;

            if (formId === 'edit_') {
                $('#edit_cost_display').text(cost.toFixed(2));
            } else {
                $('#item_cost_display').text(cost.toFixed(2));
            }
        });
    });
</script>
{% endblock %}
