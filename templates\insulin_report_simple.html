{% extends "base.html" %}

{% block title %}تقرير الأنسولين - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="mdi mdi-needle me-2"></i>تقرير الأنسولين
                        </h4>
                        <div>
                            <button onclick="window.print()" class="btn btn-light me-2">
                                <i class="mdi mdi-printer me-1"></i>طباعة
                            </button>
                            <a href="{{ url_for('reports') }}" class="btn btn-light">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><strong>تاريخ التقرير:</strong> {{ now.strftime('%Y-%m-%d %H:%M') if now else 'غير محدد' }}</h6>
                            <h6><strong>الفترة:</strong> {{ period }}</h6>
                            <h6><strong>النطاق:</strong> {{ scope_name }}</h6>
                        </div>
                        <div class="col-md-6 text-end">
                            <h6><strong>إجمالي التكلفة:</strong> {{ "{:,.2f}".format(total_cost) }} ج.م</h6>
                            <h6><strong>إجمالي الكمية:</strong> {{ "{:,.0f}".format(total_quantity) }} وحدة</h6>
                            <h6><strong>إجمالي الحالات:</strong> {{ total_cases }}</h6>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-cash-multiple" style="font-size: 2rem;"></i>
                                    <h5 class="mt-2">إجمالي التكلفة</h5>
                                    <h3>{{ "{:,.2f}".format(total_cost) }} ج.م</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-package-variant" style="font-size: 2rem;"></i>
                                    <h5 class="mt-2">إجمالي الكمية</h5>
                                    <h3>{{ "{:,.0f}".format(total_quantity) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-archive" style="font-size: 2rem;"></i>
                                    <h5 class="mt-2">عدد الحالات</h5>
                                    <h3>{{ total_cases }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="mdi mdi-medical-bag" style="font-size: 2rem;"></i>
                                    <h5 class="mt-2">أنواع الأنسولين</h5>
                                    <h3>{{ unique_types }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الأنسولين -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="mdi mdi-table me-2"></i>تفاصيل منصرف الأنسولين
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if insulin_items %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-info">
                                        <tr>
                                            <th>#</th>
                                            <th>اسم الأنسولين</th>
                                            <th>النوع</th>
                                            <th>الفئة</th>
                                            <th>الوحدة</th>
                                            <th>السعر</th>
                                            <th>الكمية</th>
                                            <th>عدد الحالات</th>
                                            <th>التكلفة</th>
                                            <th>الموقع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in insulin_items %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td><strong>{{ item.name }}</strong></td>
                                            <td>
                                                <span class="badge bg-primary">{{ item.type }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ item.category }}</span>
                                            </td>
                                            <td>{{ item.unit }}</td>
                                            <td>{{ "{:,.2f}".format(item.price) }} ج.م</td>
                                            <td>{{ "{:,.0f}".format(item.quantity) }}</td>
                                            <td>{{ item.cases_count }}</td>
                                            <td><strong>{{ "{:,.2f}".format(item.cost) }} ج.م</strong></td>
                                            <td>{{ item.location_name }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-secondary">
                                        <tr>
                                            <th colspan="6">الإجمالي</th>
                                            <th>{{ "{:,.0f}".format(total_quantity) }}</th>
                                            <th>{{ total_cases }}</th>
                                            <th><strong>{{ "{:,.2f}".format(total_cost) }} ج.م</strong></th>
                                            <th>-</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-3" style="font-size: 2rem;"></i>
                                <strong>لا توجد بيانات أنسولين في الفترة المحددة.</strong>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    {% if insulin_items and insulin_items|length > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">متوسط التكلفة</h6>
                                    <h4 class="text-primary">{{ "{:,.2f}".format(total_cost / insulin_items|length) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">متوسط الكمية</h6>
                                    <h4 class="text-success">{{ "{:,.0f}".format(total_quantity / insulin_items|length) }} وحدة</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">متوسط الحالات</h6>
                                    <h4 class="text-warning">{{ "{:,.1f}".format(total_cases / insulin_items|length) }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إضافة وظائف التفاعل إذا لزم الأمر
$(document).ready(function() {
    // يمكن إضافة المزيد من الوظائف هنا
});
</script>
{% endblock %}
