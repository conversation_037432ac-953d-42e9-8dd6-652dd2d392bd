{% extends "base.html" %}

{% block title %}تقرير الأنسولين - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
    <div class="row justify-content-center">
        <div class="col-12">
            <!-- Header Card -->
            <div class="card shadow-lg mb-4" style="border: none; border-radius: 20px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
                <div class="card-header text-white text-center py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px 20px 0 0; border: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div></div>
                        <div class="text-center">
                            <i class="mdi mdi-needle" style="font-size: 3rem; margin-bottom: 10px;"></i>
                            <h2 class="mb-0 fw-bold">تقرير الأنسولين</h2>
                            <p class="mb-0 opacity-75">تقرير شامل لمنصرف الأنسولين</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button onclick="window.print()" class="btn btn-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-printer me-1"></i>طباعة
                            </button>
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- معلومات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card border-0 bg-light rounded-3 p-3">
                                <h5 class="text-primary mb-3">
                                    <i class="mdi mdi-information-outline me-2"></i>معلومات التقرير
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-2"><i class="mdi mdi-calendar text-success me-2"></i><strong>تاريخ التقرير:</strong> {{ now.strftime('%Y-%m-%d %H:%M') if now else 'غير محدد' }}</p>
                                        <p class="mb-2"><i class="mdi mdi-clock text-info me-2"></i><strong>الفترة:</strong> {{ period }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-2"><i class="mdi mdi-map-marker text-warning me-2"></i><strong>النطاق:</strong> {{ scope_name }}</p>
                                        <p class="mb-0"><i class="mdi mdi-medical-bag text-danger me-2"></i><strong>أنواع الأنسولين:</strong> {{ unique_types }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 bg-gradient text-white rounded-3 p-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <h6 class="text-white-50 mb-2">الملخص المالي</h6>
                                <h4 class="mb-1">{{ "{:,.2f}".format(total_cost) }} ج.م</h4>
                                <small class="text-white-75">إجمالي التكلفة</small>
                                <hr class="my-2 border-white-50">
                                <div class="d-flex justify-content-between">
                                    <span>الكمية:</span>
                                    <span>{{ "{:,.0f}".format(total_quantity) }}</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>الحالات:</span>
                                    <span>{{ total_cases }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="card-body text-center text-white p-4">
                                    <div class="rounded-circle bg-white bg-opacity-20 d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="mdi mdi-cash-multiple" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <h6 class="text-white-75 mb-1">إجمالي التكلفة</h6>
                                    <h4 class="mb-0 fw-bold">{{ "{:,.2f}".format(total_cost) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px; background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                                <div class="card-body text-center text-white p-4">
                                    <div class="rounded-circle bg-white bg-opacity-20 d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="mdi mdi-package-variant" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <h6 class="text-white-75 mb-1">إجمالي الكمية</h6>
                                    <h4 class="mb-0 fw-bold">{{ "{:,.0f}".format(total_quantity) }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                                <div class="card-body text-center text-white p-4">
                                    <div class="rounded-circle bg-white bg-opacity-20 d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="mdi mdi-archive" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <h6 class="text-white-75 mb-1">عدد الحالات</h6>
                                    <h4 class="mb-0 fw-bold">{{ total_cases }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                                <div class="card-body text-center text-white p-4">
                                    <div class="rounded-circle bg-white bg-opacity-20 d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="mdi mdi-medical-bag" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <h6 class="text-white-75 mb-1">أنواع الأنسولين</h6>
                                    <h4 class="mb-0 fw-bold">{{ unique_types }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الأنسولين -->
                    <div class="card border-0 shadow-sm" style="border-radius: 15px;">
                        <div class="card-header bg-white border-0 py-3" style="border-radius: 15px 15px 0 0;">
                            <h5 class="mb-0 text-primary">
                                <i class="mdi mdi-table me-2"></i>تفاصيل منصرف الأنسولين
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            {% if insulin_items %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" style="border-radius: 0 0 15px 15px;">
                                    <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                        <tr class="text-white">
                                            <th class="border-0 py-3">#</th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-needle me-1"></i>اسم الأنسولين
                                            </th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-tag me-1"></i>النوع
                                            </th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-folder me-1"></i>الفئة
                                            </th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-scale me-1"></i>الوحدة
                                            </th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-currency-usd me-1"></i>السعر
                                            </th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-package me-1"></i>الكمية
                                            </th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-account-group me-1"></i>الحالات
                                            </th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-cash me-1"></i>التكلفة
                                            </th>
                                            <th class="border-0 py-3">
                                                <i class="mdi mdi-map-marker me-1"></i>الموقع
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in insulin_items %}
                                        <tr class="border-0" style="border-bottom: 1px solid #f0f0f0 !important;">
                                            <td class="py-3">
                                                <span class="badge bg-light text-dark rounded-pill">{{ loop.index }}</span>
                                            </td>
                                            <td class="py-3">
                                                <strong class="text-primary">{{ item.name }}</strong>
                                            </td>
                                            <td class="py-3">
                                                <span class="badge rounded-pill" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">{{ item.type }}</span>
                                            </td>
                                            <td class="py-3">
                                                <span class="badge bg-success rounded-pill">{{ item.category }}</span>
                                            </td>
                                            <td class="py-3 text-muted">{{ item.unit }}</td>
                                            <td class="py-3">
                                                <span class="fw-bold text-success">{{ "{:,.2f}".format(item.price) }} ج.م</span>
                                            </td>
                                            <td class="py-3">
                                                <span class="fw-bold">{{ "{:,.0f}".format(item.quantity) }}</span>
                                            </td>
                                            <td class="py-3">
                                                <span class="badge bg-warning text-dark rounded-pill">{{ item.cases_count }}</span>
                                            </td>
                                            <td class="py-3">
                                                <strong class="text-primary">{{ "{:,.2f}".format(item.cost) }} ج.م</strong>
                                            </td>
                                            <td class="py-3">
                                                <small class="text-muted">{{ item.location_name }}</small>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                        <tr class="border-0">
                                            <th colspan="6" class="py-3 text-primary">
                                                <i class="mdi mdi-calculator me-2"></i>الإجمالي
                                            </th>
                                            <th class="py-3 text-primary">{{ "{:,.0f}".format(total_quantity) }}</th>
                                            <th class="py-3 text-primary">{{ total_cases }}</th>
                                            <th class="py-3">
                                                <strong class="text-success fs-5">{{ "{:,.2f}".format(total_cost) }} ج.م</strong>
                                            </th>
                                            <th class="py-3">-</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <div class="mb-4">
                                    <i class="mdi mdi-information-outline" style="font-size: 4rem; color: #6c757d;"></i>
                                </div>
                                <h5 class="text-muted mb-2">لا توجد بيانات أنسولين</h5>
                                <p class="text-muted">لا توجد بيانات أنسولين في الفترة والنطاق المحدد</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    {% if insulin_items and insulin_items|length > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-4 mb-3">
                            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="card-body text-center text-white p-4">
                                    <div class="rounded-circle bg-white bg-opacity-20 d-inline-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                        <i class="mdi mdi-calculator" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <h6 class="text-white-75 mb-1">متوسط التكلفة</h6>
                                    <h4 class="mb-0 fw-bold">{{ "{:,.2f}".format(total_cost / insulin_items|length) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px; background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                                <div class="card-body text-center text-white p-4">
                                    <div class="rounded-circle bg-white bg-opacity-20 d-inline-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                        <i class="mdi mdi-chart-line" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <h6 class="text-white-75 mb-1">متوسط الكمية</h6>
                                    <h4 class="mb-0 fw-bold">{{ "{:,.0f}".format(total_quantity / insulin_items|length) }} وحدة</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-0 shadow-sm h-100" style="border-radius: 15px; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                                <div class="card-body text-center text-white p-4">
                                    <div class="rounded-circle bg-white bg-opacity-20 d-inline-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                        <i class="mdi mdi-account-multiple" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <h6 class="text-white-75 mb-1">متوسط الحالات</h6>
                                    <h4 class="mb-0 fw-bold">{{ "{:,.1f}".format(total_cases / insulin_items|length) }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إضافة وظائف التفاعل إذا لزم الأمر
$(document).ready(function() {
    // يمكن إضافة المزيد من الوظائف هنا
});
</script>
{% endblock %}
