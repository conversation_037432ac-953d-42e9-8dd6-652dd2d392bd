import sqlite3
import os

def get_db_connection():
    # التأكد من وجود مجلد instance
    if not os.path.exists('instance'):
        os.makedirs('instance')

    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

def fix_insulin_table():
    conn = get_db_connection()
    
    # التحقق من وجود جدول insulin_dispensed
    table_exists = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='insulin_dispensed'").fetchone()
    
    if not table_exists:
        print("جدول insulin_dispensed غير موجود!")
        conn.close()
        return
    
    # التحقق من وجود الأعمدة
    columns = conn.execute("PRAGMA table_info(insulin_dispensed)").fetchall()
    column_names = [column['name'] for column in columns]
    
    print("الأعمدة الموجودة:", column_names)
    
    # إضافة الأعمدة المفقودة
    if 'unit' not in column_names:
        print("إضافة عمود unit...")
        conn.execute("ALTER TABLE insulin_dispensed ADD COLUMN unit TEXT DEFAULT 'فيال'")
    
    if 'cost' not in column_names:
        print("إضافة عمود cost...")
        conn.execute("ALTER TABLE insulin_dispensed ADD COLUMN cost REAL DEFAULT 0")
    
    if 'rate' not in column_names:
        print("إضافة عمود rate...")
        conn.execute("ALTER TABLE insulin_dispensed ADD COLUMN rate REAL DEFAULT 0")
    
    if 'balance' not in column_names:
        print("إضافة عمود balance...")
        conn.execute("ALTER TABLE insulin_dispensed ADD COLUMN balance REAL DEFAULT 0")
    
    if 'category' not in column_names:
        print("إضافة عمود category...")
        conn.execute("ALTER TABLE insulin_dispensed ADD COLUMN category TEXT DEFAULT 'عام'")
    
    if 'insulin_code_id' not in column_names:
        print("إضافة عمود insulin_code_id...")
        conn.execute("ALTER TABLE insulin_dispensed ADD COLUMN insulin_code_id INTEGER")
    
    # إعادة إنشاء الجدول إذا لزم الأمر
    if 'unit' not in column_names:
        print("إعادة إنشاء الجدول بالكامل...")
        # حفظ البيانات الموجودة
        old_data = conn.execute("SELECT * FROM insulin_dispensed").fetchall()
        
        # إنشاء جدول مؤقت
        conn.execute('''
        CREATE TABLE insulin_dispensed_temp (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            unit TEXT DEFAULT 'فيال',
            cases_count INTEGER NOT NULL,
            quantity REAL NOT NULL,
            price REAL NOT NULL,
            cost REAL DEFAULT 0,
            rate REAL DEFAULT 0,
            balance REAL DEFAULT 0,
            category TEXT DEFAULT 'عام',
            clinic_id INTEGER NOT NULL,
            area_id INTEGER NOT NULL,
            dispense_month DATE NOT NULL,
            insulin_code_id INTEGER,
            FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
            FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE,
            FOREIGN KEY (insulin_code_id) REFERENCES insulin_codes (id) ON DELETE SET NULL
        )
        ''')
        
        # نقل البيانات القديمة
        for row in old_data:
            conn.execute('''
            INSERT INTO insulin_dispensed_temp (id, name, type, cases_count, quantity, price, clinic_id, area_id, dispense_month)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (row['id'], row['name'], row['type'], row['cases_count'], row['quantity'], row['price'], row['clinic_id'], row['area_id'], row['dispense_month']))
        
        # حذف الجدول القديم
        conn.execute("DROP TABLE insulin_dispensed")
        
        # إعادة تسمية الجدول المؤقت
        conn.execute("ALTER TABLE insulin_dispensed_temp RENAME TO insulin_dispensed")
    
    conn.commit()
    
    # التحقق من الأعمدة بعد التعديل
    columns = conn.execute("PRAGMA table_info(insulin_dispensed)").fetchall()
    column_names = [column['name'] for column in columns]
    
    print("الأعمدة بعد التعديل:", column_names)
    
    conn.close()
    print("تم إصلاح جدول insulin_dispensed بنجاح!")

if __name__ == "__main__":
    fix_insulin_table()
