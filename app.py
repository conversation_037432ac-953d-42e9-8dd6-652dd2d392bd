from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, make_response
import sqlite3
import os
import json
from datetime import datetime, timedelta

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'مفتاح-سري-افتراضي-للتطوير'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/medicine_dispenser.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# التأكد من وجود مجلد instance
if not os.path.exists('instance'):
    os.makedirs('instance')

# دالة للاتصال بقاعدة البيانات
def get_db_connection():
    # التأكد من وجود مجلد instance
    if not os.path.exists('instance'):
        os.makedirs('instance')

    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    conn = get_db_connection()
    
    # قراءة وتنفيذ ملف schema.sql
    with open('schema.sql', 'r', encoding='utf-8') as f:
        schema = f.read()
        conn.executescript(schema)
    
    # إنشاء بعض البيانات الافتراضية إذا كانت الجداول فارغة
    cur = conn.cursor()
    
    # التحقق من وجود فروع
    cur.execute('SELECT COUNT(*) FROM branches')
    if cur.fetchone()[0] == 0:
        cur.execute("INSERT INTO branches (name) VALUES ('فرع سوهاج')")
    
    # التحقق من وجود تصنيفات الأدوية
    cur.execute('SELECT COUNT(*) FROM drug_categories')
    if cur.fetchone()[0] == 0:
        cur.execute("INSERT INTO drug_categories (name) VALUES ('مسكنات'), ('مضادات حيوية'), ('أدوية ضغط'), ('أدوية سكر')")
    
    conn.commit()
    conn.close()

# إضافة متغير now للقوالب
@app.context_processor
def inject_now():
    return {'now': datetime.now()}

# تهيئة قاعدة البيانات عند بدء التشغيل
try:
    if not os.path.exists('instance/medicine_dispenser.db'):
        init_db()
        print("تم إنشاء قاعدة البيانات وتهيئتها بنجاح!")
except Exception as e:
    print(f"خطأ في تهيئة قاعدة البيانات: {e}")
    # إنشاء قاعدة بيانات بسيطة
    conn = get_db_connection()
    conn.execute('CREATE TABLE IF NOT EXISTS branches (id INTEGER PRIMARY KEY, name TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS drug_categories (id INTEGER PRIMARY KEY, name TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS areas (id INTEGER PRIMARY KEY, name TEXT, branch_id INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS clinics (id INTEGER PRIMARY KEY, name TEXT, area_id INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS drugs (id INTEGER PRIMARY KEY, name TEXT, scientific_name TEXT, category_id INTEGER, expiry_date DATE)')
    conn.execute('CREATE TABLE IF NOT EXISTS dispensed (id INTEGER PRIMARY KEY, drug_id INTEGER, clinic_id INTEGER, dispense_month DATE)')
    conn.execute('CREATE TABLE IF NOT EXISTS dispensed_details (id INTEGER PRIMARY KEY, dispensed_id INTEGER, quantity REAL, price REAL, cases_count INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_dispensed (id INTEGER PRIMARY KEY, name TEXT, type TEXT, unit TEXT, cases_count INTEGER, quantity REAL, price REAL, cost REAL, rate REAL, balance REAL, category TEXT, clinic_id INTEGER, area_id INTEGER, dispense_month DATE, insulin_code_id INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_codes (id INTEGER PRIMARY KEY, code INTEGER, name TEXT, description TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_types (id INTEGER PRIMARY KEY, name TEXT, description TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_categories (id INTEGER PRIMARY KEY, name TEXT, description TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS drug_groups (id INTEGER PRIMARY KEY, name TEXT, cost REAL, clinic_id INTEGER, area_id INTEGER, dispense_month DATE, group_code_id INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS drug_group_codes (id INTEGER PRIMARY KEY, code INTEGER, name TEXT, description TEXT)')

    # إضافة بيانات افتراضية للاختبار
    try:
        # فروع افتراضية
        conn.execute("INSERT OR IGNORE INTO branches (id, name) VALUES (1, 'الفرع الرئيسي')")
        conn.execute("INSERT OR IGNORE INTO branches (id, name) VALUES (2, 'فرع المدينة')")

        # مناطق افتراضية
        conn.execute("INSERT OR IGNORE INTO areas (id, name, branch_id) VALUES (1, 'منطقة الشمال', 1)")
        conn.execute("INSERT OR IGNORE INTO areas (id, name, branch_id) VALUES (2, 'منطقة الجنوب', 1)")
        conn.execute("INSERT OR IGNORE INTO areas (id, name, branch_id) VALUES (3, 'منطقة الوسط', 2)")

        # عيادات افتراضية
        conn.execute("INSERT OR IGNORE INTO clinics (id, name, area_id) VALUES (1, 'عيادة الباطنة', 1)")
        conn.execute("INSERT OR IGNORE INTO clinics (id, name, area_id) VALUES (2, 'عيادة الأطفال', 1)")
        conn.execute("INSERT OR IGNORE INTO clinics (id, name, area_id) VALUES (3, 'عيادة القلب', 2)")
        conn.execute("INSERT OR IGNORE INTO clinics (id, name, area_id) VALUES (4, 'عيادة السكر', 3)")

        # تصنيفات الأدوية
        conn.execute("INSERT OR IGNORE INTO drug_categories (id, name) VALUES (1, 'مضادات حيوية')")
        conn.execute("INSERT OR IGNORE INTO drug_categories (id, name) VALUES (2, 'مسكنات')")
        conn.execute("INSERT OR IGNORE INTO drug_categories (id, name) VALUES (3, 'أدوية القلب')")
        conn.execute("INSERT OR IGNORE INTO drug_categories (id, name) VALUES (4, 'أدوية السكر')")

        # أدوية افتراضية
        conn.execute("INSERT OR IGNORE INTO drugs (id, name, scientific_name, category_id) VALUES (1, 'أموكسيسيلين', 'Amoxicillin', 1)")
        conn.execute("INSERT OR IGNORE INTO drugs (id, name, scientific_name, category_id) VALUES (2, 'باراسيتامول', 'Paracetamol', 2)")
        conn.execute("INSERT OR IGNORE INTO drugs (id, name, scientific_name, category_id) VALUES (3, 'كونكور', 'Concor', 3)")
        conn.execute("INSERT OR IGNORE INTO drugs (id, name, scientific_name, category_id) VALUES (4, 'جلوكوفاج', 'Glucophage', 4)")

        # أنواع الأنسولين
        conn.execute("INSERT OR IGNORE INTO insulin_types (id, name, description) VALUES (1, 'أنسولين سريع المفعول', 'يبدأ مفعوله خلال 15 دقيقة')")
        conn.execute("INSERT OR IGNORE INTO insulin_types (id, name, description) VALUES (2, 'أنسولين متوسط المفعول', 'يبدأ مفعوله خلال 1-2 ساعة')")
        conn.execute("INSERT OR IGNORE INTO insulin_types (id, name, description) VALUES (3, 'أنسولين طويل المفعول', 'يستمر مفعوله 24 ساعة')")

        # فئات الأنسولين
        conn.execute("INSERT OR IGNORE INTO insulin_categories (id, name, description) VALUES (1, 'أنسولين بشري', 'أنسولين مصنع ليشبه الأنسولين البشري')")
        conn.execute("INSERT OR IGNORE INTO insulin_categories (id, name, description) VALUES (2, 'أنسولين تناظري', 'أنسولين معدل وراثياً')")

        # أكواد الأنسولين
        conn.execute("INSERT OR IGNORE INTO insulin_codes (id, code, name, description) VALUES (1, 100, 'نوفورابيد', 'أنسولين سريع المفعول')")
        conn.execute("INSERT OR IGNORE INTO insulin_codes (id, code, name, description) VALUES (2, 200, 'لانتوس', 'أنسولين طويل المفعول')")

        # أكواد مجموعات الأدوية
        conn.execute("INSERT OR IGNORE INTO drug_group_codes (id, code, name, description) VALUES (1, 100, 'مجموعة القلب', 'أدوية القلب والأوعية الدموية')")
        conn.execute("INSERT OR IGNORE INTO drug_group_codes (id, code, name, description) VALUES (2, 200, 'مجموعة السكر', 'أدوية السكري')")
        conn.execute("INSERT OR IGNORE INTO drug_group_codes (id, code, name, description) VALUES (3, 300, 'مجموعة المضادات', 'المضادات الحيوية')")

        # بيانات تجريبية للمنصرف
        current_month = datetime.now().strftime('%Y-%m-01')

        # منصرف أدوية تجريبي
        conn.execute("INSERT OR IGNORE INTO dispensed (id, drug_id, clinic_id, dispense_month) VALUES (1, 1, 1, ?)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO dispensed (id, drug_id, clinic_id, dispense_month) VALUES (2, 2, 2, ?)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO dispensed (id, drug_id, clinic_id, dispense_month) VALUES (3, 3, 3, ?)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO dispensed (id, drug_id, clinic_id, dispense_month) VALUES (4, 4, 4, ?)", (current_month,))

        # تفاصيل المنصرف
        conn.execute("INSERT OR IGNORE INTO dispensed_details (id, dispensed_id, quantity, price, cases_count) VALUES (1, 1, 100, 5.50, 10)")
        conn.execute("INSERT OR IGNORE INTO dispensed_details (id, dispensed_id, quantity, price, cases_count) VALUES (2, 2, 200, 3.25, 20)")
        conn.execute("INSERT OR IGNORE INTO dispensed_details (id, dispensed_id, quantity, price, cases_count) VALUES (3, 3, 150, 12.75, 15)")
        conn.execute("INSERT OR IGNORE INTO dispensed_details (id, dispensed_id, quantity, price, cases_count) VALUES (4, 4, 300, 8.90, 30)")

        # منصرف أنسولين تجريبي لفروع مختلفة
        # فرع القاهرة (branch_id=1)
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (1, 'نوفورابيد', 'سريع المفعول', 'وحدة', 8, 800, 25.50, 204.00, 1.0, 0, 'بشري', 1, 1, ?, 1)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (2, 'لانتوس', 'طويل المفعول', 'وحدة', 6, 600, 45.75, 274.50, 1.0, 0, 'تناظري', 1, 1, ?, 2)", (current_month,))

        # فرع الجيزة (branch_id=2)
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (3, 'هيومالوج', 'سريع المفعول', 'وحدة', 4, 400, 28.75, 115.00, 1.0, 0, 'تناظري', 3, 2, ?, 1)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (4, 'ليفيمير', 'متوسط المفعول', 'وحدة', 3, 350, 38.25, 133.88, 1.0, 0, 'تناظري', 3, 2, ?, 2)", (current_month,))

        # فرع الإسكندرية (branch_id=3)
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (5, 'أبيدرا', 'سريع المفعول', 'وحدة', 2, 250, 32.50, 81.25, 1.0, 0, 'تناظري', 5, 3, ?, 1)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (6, 'تريسيبا', 'طويل المفعول', 'وحدة', 2, 180, 52.75, 105.50, 1.0, 0, 'تناظري', 5, 3, ?, 2)", (current_month,))

        # مجموعات دوائية تجريبية لفروع مختلفة
        # فرع القاهرة (branch_id=1)
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (1, 'مجموعة أدوية القلب المتقدمة', 1850.75, 1, 1, ?, 1)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (2, 'مجموعة أدوية الضغط العالي', 1320.50, 1, 1, ?, 1)", (current_month,))

        # فرع الجيزة (branch_id=2)
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (3, 'مجموعة أدوية السكر المتطورة', 1150.25, 3, 2, ?, 2)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (4, 'مجموعة أدوية الكولسترول', 890.75, 3, 2, ?, 2)", (current_month,))

        # فرع الإسكندرية (branch_id=3)
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (5, 'مجموعة المضادات الحيوية', 750.50, 5, 3, ?, 3)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (6, 'مجموعة أدوية الجهاز التنفسي', 620.25, 5, 3, ?, 3)", (current_month,))

        print("تم إضافة البيانات الافتراضية والتجريبية بنجاح")
    except Exception as e:
        print(f"خطأ في إضافة البيانات الافتراضية: {e}")

    conn.commit()
    conn.close()
    print("تم إنشاء قاعدة بيانات بسيطة مع بيانات افتراضية")

# المسارات الرئيسية
@app.route('/')
def index():
    conn = get_db_connection()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('index.html', branches=branches)

@app.route('/copyright')
def copyright():
    current_year = datetime.now().year
    return render_template('copyright.html', current_year=current_year)

@app.route('/designer')
def designer():
    return render_template('designer.html')

# مسارات إدارة الفروع
@app.route('/manage/branches', methods=['GET', 'POST'])
def manage_branches():
    if request.method == 'POST':
        name = request.form.get('name')
        if name:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO branches (name) VALUES (?)', (name,))
                conn.commit()
                flash('تم إضافة الفرع بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا الفرع موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم الفرع', 'danger')

    conn = get_db_connection()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('manage_branches.html', branches=branches)

# حذف وتعديل الفروع
@app.route('/manage/branches/<int:branch_id>/delete', methods=['POST'])
def delete_branch(branch_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM branches WHERE id = ?', (branch_id,))
        conn.commit()
        flash('تم حذف الفرع بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف الفرع: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_branches'))

@app.route('/manage/branches/update', methods=['POST'])
def update_branch():
    branch_id = request.form.get('branch_id')
    name = request.form.get('name')

    if branch_id and name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE branches SET name = ? WHERE id = ?', (name, branch_id))
            conn.commit()
            flash('تم تحديث الفرع بنجاح', 'success')
        except sqlite3.IntegrityError:
            flash('هذا الفرع موجود بالفعل', 'danger')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث الفرع: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم الفرع', 'danger')
    return redirect(url_for('manage_branches'))

# مسارات إدارة المناطق
@app.route('/manage/areas', methods=['GET', 'POST'])
def manage_areas():
    if request.method == 'POST':
        name = request.form.get('name')
        branch_id = request.form.get('branch_id')
        if name and branch_id:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO areas (name, branch_id) VALUES (?, ?)', (name, branch_id))
                conn.commit()
                flash('تم إضافة المنطقة بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذه المنطقة موجودة بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم المنطقة والفرع', 'danger')

    conn = get_db_connection()
    areas = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('manage_areas.html', areas=areas, branches=branches)

# حذف وتعديل المناطق
@app.route('/manage/areas/<int:area_id>/delete', methods=['POST'])
def delete_area(area_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM areas WHERE id = ?', (area_id,))
        conn.commit()
        flash('تم حذف المنطقة بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف المنطقة: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_areas'))

@app.route('/manage/areas/update', methods=['POST'])
def update_area():
    area_id = request.form.get('area_id')
    name = request.form.get('name')
    branch_id = request.form.get('branch_id')

    if area_id and name and branch_id:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE areas SET name = ?, branch_id = ? WHERE id = ?',
                       (name, branch_id, area_id))
            conn.commit()
            flash('تم تحديث المنطقة بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث المنطقة: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')
    return redirect(url_for('manage_areas'))

# مسارات إدارة العيادات
@app.route('/manage/clinics', methods=['GET', 'POST'])
def manage_clinics():
    if request.method == 'POST':
        name = request.form.get('name')
        area_id = request.form.get('area_id')
        if name and area_id:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO clinics (name, area_id) VALUES (?, ?)', (name, area_id))
                conn.commit()
                flash('تم إضافة العيادة بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذه العيادة موجودة بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم العيادة والمنطقة', 'danger')

    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    areas = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    conn.close()
    return render_template('manage_clinics.html', clinics=clinics, areas=areas)

# حذف وتعديل العيادات
@app.route('/manage/clinics/<int:clinic_id>/delete', methods=['POST'])
def delete_clinic(clinic_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM clinics WHERE id = ?', (clinic_id,))
        conn.commit()
        flash('تم حذف العيادة بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف العيادة: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_clinics'))

@app.route('/manage/clinics/update', methods=['POST'])
def update_clinic():
    clinic_id = request.form.get('clinic_id')
    name = request.form.get('name')
    area_id = request.form.get('area_id')

    if clinic_id and name and area_id:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE clinics SET name = ?, area_id = ? WHERE id = ?',
                       (name, area_id, clinic_id))
            conn.commit()
            flash('تم تحديث العيادة بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث العيادة: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')
    return redirect(url_for('manage_clinics'))

# مسارات إدارة تصنيفات الأدوية
@app.route('/manage/drug_categories_new', methods=['GET', 'POST'])
def manage_drug_categories_new():
    if request.method == 'POST':
        name = request.form.get('name')
        if name:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO drug_categories (name) VALUES (?)', (name,))
                conn.commit()
                flash('تم إضافة التصنيف بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا التصنيف موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم التصنيف', 'danger')

    conn = get_db_connection()
    categories = conn.execute('''
        SELECT
            dc.id,
            dc.name,
            COUNT(d.id) as drug_count
        FROM
            drug_categories dc
        LEFT JOIN
            drugs d ON dc.id = d.category_id
        GROUP BY
            dc.id, dc.name
        ORDER BY
            dc.name
    ''').fetchall()
    conn.close()
    return render_template('manage_drug_categories_new.html', categories=categories)

# مسار تحديث تصنيف الأدوية
@app.route('/manage/drug_categories/update', methods=['POST'])
def update_drug_category():
    category_id = request.form.get('category_id')
    name = request.form.get('name')

    if category_id and name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE drug_categories SET name = ? WHERE id = ?', (name, category_id))
            conn.commit()
            flash('تم تحديث التصنيف بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث التصنيف: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم التصنيف', 'danger')

    return redirect(url_for('manage_drug_categories_new'))

@app.route('/manage/drug_categories/delete/<int:category_id>', methods=['POST'])
def delete_drug_category(category_id):
    conn = get_db_connection()
    try:
        # التحقق من وجود أدوية مرتبطة بهذا التصنيف
        drugs_count = conn.execute('SELECT COUNT(*) FROM drugs WHERE category_id = ?', (category_id,)).fetchone()[0]

        if drugs_count > 0:
            flash(f'لا يمكن حذف هذا التصنيف لأنه مرتبط بـ {drugs_count} دواء', 'warning')
        else:
            conn.execute('DELETE FROM drug_categories WHERE id = ?', (category_id,))
            conn.commit()
            flash('تم حذف التصنيف بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف التصنيف: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_drug_categories_new'))

# مسارات إدارة الأدوية
@app.route('/manage/drugs/new', methods=['GET', 'POST'])
def manage_drugs_new():
    if request.method == 'POST':
        name = request.form.get('name')
        scientific_name = request.form.get('scientific_name')
        category_id = request.form.get('category_id')
        expiry_date = request.form.get('expiry_date')

        if name and category_id:
            conn = get_db_connection()
            try:
                conn.execute(
                    'INSERT INTO drugs (name, scientific_name, category_id, expiry_date) VALUES (?, ?, ?, ?)',
                    (name, scientific_name, category_id, expiry_date)
                )
                conn.commit()
                flash('تم إضافة الدواء بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا الدواء موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم الدواء والتصنيف', 'danger')

    conn = get_db_connection()
    drugs = conn.execute('''
        SELECT drugs.*, drug_categories.name as category_name
        FROM drugs
        JOIN drug_categories ON drugs.category_id = drug_categories.id
    ''').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    conn.close()
    return render_template('manage_drugs_new.html', drugs=drugs, categories=categories)

@app.route('/manage/drugs/delete/<int:drug_id>', methods=['POST'])
def delete_drug(drug_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM drugs WHERE id = ?', (drug_id,))
        conn.commit()
        flash('تم حذف الدواء بنجاح', 'success')
    except:
        flash('حدث خطأ أثناء حذف الدواء', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_drugs_new'))

# مسارات صرف الأدوية
@app.route('/dispense/new', methods=['GET', 'POST'])
def dispense_new():
    if request.method == 'POST':
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        items_data = request.form.get('items_data')
        total_cost = request.form.get('total_cost')

        print(f"بيانات الطلب: clinic_id={clinic_id}, dispense_month={dispense_month}")
        print(f"items_data={items_data}")
        print(f"total_cost={total_cost}")

        if clinic_id and dispense_month and items_data:
            try:
                # تحويل البيانات من JSON إلى قائمة
                import json
                items = json.loads(items_data)

                print(f"عدد العناصر: {len(items)}")

                if not items:
                    flash('يرجى إضافة عنصر واحد على الأقل إلى القائمة', 'danger')
                    return redirect(url_for('dispense_new'))

                # تحويل التاريخ
                dispense_date = f"{dispense_month}-01"

                conn = get_db_connection()

                # الحصول على معرف المنطقة من العيادة
                area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                if not area_result:
                    flash('العيادة المحددة غير موجودة', 'danger')
                    return redirect(url_for('dispense_new'))

                area_id = area_result['area_id']

                # إضافة كل عنصر في القائمة
                for item in items:
                    print(f"معالجة العنصر: {item}")

                    # إنشاء سجل المنصرف
                    conn.execute(
                        'INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (?, ?, ?)',
                        (item['drug_id'], clinic_id, dispense_date)
                    )

                    # الحصول على معرف السجل المضاف
                    dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]

                    # إضافة تفاصيل المنصرف
                    conn.execute(
                        'INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, ?, ?, ?)',
                        (dispensed_id, float(item['quantity']), float(item['price']), int(item['cases_count']))
                    )

                conn.commit()
                conn.close()

                print(f"تم حفظ {len(items)} عنصر بنجاح")
                flash(f'تم تسجيل {len(items)} من بيانات المنصرف بنجاح', 'success')

                # إرجاع استجابة JSON للـ AJAX
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': True, 'message': f'تم حفظ {len(items)} عنصر بنجاح'})

                return redirect(url_for('dispense_new'))

            except json.JSONDecodeError as e:
                print(f"خطأ في تحليل JSON: {e}")
                flash('حدث خطأ في تنسيق البيانات', 'danger')
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': 'خطأ في تنسيق البيانات'}), 400
                return redirect(url_for('dispense_new'))
            except Exception as e:
                print(f"خطأ في حفظ البيانات: {e}")
                flash(f'حدث خطأ: {str(e)}', 'danger')
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': str(e)}), 500
                return redirect(url_for('dispense_new'))
        else:
            print("بيانات مفقودة")
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': 'بيانات مفقودة'}), 400
            return redirect(url_for('dispense_new'))

    # جلب البيانات اللازمة للعرض
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    dispensed_items = conn.execute('''
        SELECT
            d.id,
            c.name as clinic_name,
            dr.name as drug_name,
            dc.name as category_name,
            dd.quantity,
            dd.price,
            dd.cases_count,
            (dd.quantity * dd.price) as total_cost,
            d.dispense_month
        FROM dispensed d
        JOIN clinics c ON d.clinic_id = c.id
        JOIN drugs dr ON d.drug_id = dr.id
        JOIN drug_categories dc ON dr.category_id = dc.id
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        ORDER BY d.id DESC
        LIMIT 10
    ''').fetchall()
    conn.close()
    return render_template('dispense_new.html',
                         clinics=clinics,
                         categories=categories,
                         dispensed_items=dispensed_items)

# مسار صرف الأنسولين
@app.route('/insulin/dispense', methods=['GET', 'POST'])
def insulin_dispense():
    if request.method == 'POST':
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        items_data = request.form.get('items_data')

        if all([clinic_id, dispense_month, items_data]):
            try:
                items = json.loads(items_data)
                dispense_date = f"{dispense_month}-01"

                conn = get_db_connection()
                area_id = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()['area_id']

                for item in items:
                    conn.execute(
                        '''INSERT INTO insulin_dispensed
                           (name, type, unit, cases_count, quantity, price, cost, rate, balance, category,
                            clinic_id, area_id, dispense_month, insulin_code_id)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                        (item['name'], item['type'], item['unit'], item['cases_count'], item['quantity'],
                         item['price'], item['cost'], item['rate'], item['balance'], item['category'],
                         clinic_id, area_id, dispense_date, item.get('insulin_code_id'))
                    )

                conn.commit()
                flash(f'تم إضافة {len(items)} من الأنسولين بنجاح', 'success')
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')
        return redirect(url_for('insulin_dispense'))

    # جلب البيانات اللازمة للعرض
    conn = get_db_connection()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()
    insulin_types = conn.execute('SELECT * FROM insulin_types ORDER BY name').fetchall()
    insulin_categories = conn.execute('SELECT * FROM insulin_categories ORDER BY name').fetchall()
    saved_items = conn.execute('''
        SELECT
            i.*,
            c.name as clinic_name,
            a.name as area_name,
            b.name as branch_name,
            ic.name as code_name,
            ic.code as code_value
        FROM insulin_dispensed i
        JOIN clinics c ON i.clinic_id = c.id
        JOIN areas a ON i.area_id = a.id
        JOIN branches b ON a.branch_id = b.id
        LEFT JOIN insulin_codes ic ON i.insulin_code_id = ic.id
        ORDER BY i.dispense_month DESC
        LIMIT 50
    ''').fetchall()
    conn.close()

    return render_template('insulin_dispense.html',
                         branches=branches,
                         insulin_codes=insulin_codes,
                         insulin_types=insulin_types,
                         insulin_categories=insulin_categories,
                         saved_items=saved_items)

# مسارات التقارير
@app.route('/reports')
def reports():
    conn = get_db_connection()
    areas = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
    ''').fetchall()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    insulin_categories = conn.execute('SELECT * FROM insulin_categories').fetchall()
    conn.close()
    return render_template('reports_new.html',
                         areas=areas,
                         clinics=clinics,
                         branches=branches,
                         categories=categories,
                         insulin_categories=insulin_categories)

# تقرير المقارنة
@app.route('/reports/comparison')
def comparison_report():
    """تقرير مقارنة بين الفروع أو المناطق أو العيادات"""
    # الحصول على معلمات التقرير
    scope_type = request.args.get('scope_type', 'all')  # نوع النطاق
    parent_id = request.args.get('parent_id')  # معرف الفرع أو المنطقة
    date_range = request.args.get('date_range', 'month')  # الفترة الزمنية
    category_id = request.args.get('category_id')  # تصنيف الدواء

    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    print(f"تقرير المقارنة - المعاملات: scope_type={scope_type}, parent_id={parent_id}, date_range={date_range}, category_id={category_id}")

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية
        date_params = []
        if date_range == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_filter = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params.append(current_month)
            period_desc = f"الشهر الحالي ({current_month})"
        elif date_range == 'custom' and start_date and end_date:
            date_filter = "AND d.dispense_month BETWEEN ? AND ?"
            date_params.extend([start_date, end_date])
            period_desc = f"من {start_date} إلى {end_date}"
        else:
            date_filter = ""
            period_desc = "جميع الفترات"

        # تحديد تصفية التصنيف
        category_filter = ""
        category_params = []
        selected_category_name = "جميع التصنيفات"

        if category_id:
            category_filter = "AND dr.category_id = ?"
            category_params.append(category_id)
            category_info = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category_info:
                selected_category_name = category_info['name']

        # تحديد نطاق المقارنة وعنوان التقرير
        report_title = "تقرير المقارنة"
        parent_name = ""

        if scope_type == 'all':
            # مقارنة بين جميع الفروع
            report_title = "مقارنة بين جميع الفروع"
            query = f'''
                SELECT
                    b.name as location_name,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    SUM(dd.quantity * dd.price) as total_cost,
                    COUNT(DISTINCT dr.id) as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE 1=1 {date_filter} {category_filter}
                GROUP BY b.id, b.name
                HAVING total_cost > 0
                ORDER BY total_cost DESC
            '''
            query_params = date_params + category_params

        elif scope_type == 'branch' and parent_id:
            # مقارنة بين المناطق داخل فرع محدد
            branch = conn.execute('SELECT name FROM branches WHERE id = ?', (parent_id,)).fetchone()
            if branch:
                parent_name = branch['name']
                report_title = f"مقارنة بين مناطق فرع {parent_name}"

            query = f'''
                SELECT
                    a.name as location_name,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    SUM(dd.quantity * dd.price) as total_cost,
                    COUNT(DISTINCT dr.id) as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE a.branch_id = ? {date_filter} {category_filter}
                GROUP BY a.id, a.name
                HAVING total_cost > 0
                ORDER BY total_cost DESC
            '''
            query_params = [parent_id] + date_params + category_params

        elif scope_type == 'area' and parent_id:
            # مقارنة بين العيادات داخل منطقة محددة
            area = conn.execute('''
                SELECT a.name, b.name as branch_name
                FROM areas a
                JOIN branches b ON a.branch_id = b.id
                WHERE a.id = ?
            ''', (parent_id,)).fetchone()
            if area:
                parent_name = f"{area['name']} - {area['branch_name']}"
                report_title = f"مقارنة بين عيادات منطقة {area['name']}"

            query = f'''
                SELECT
                    c.name as location_name,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    SUM(dd.quantity * dd.price) as total_cost,
                    COUNT(DISTINCT dr.id) as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE c.area_id = ? {date_filter} {category_filter}
                GROUP BY c.id, c.name
                HAVING total_cost > 0
                ORDER BY total_cost DESC
            '''
            query_params = [parent_id] + date_params + category_params

        else:
            # افتراضي - مقارنة بين جميع الفروع
            report_title = "مقارنة بين جميع الفروع"
            query = f'''
                SELECT
                    b.name as location_name,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    SUM(dd.quantity * dd.price) as total_cost,
                    COUNT(DISTINCT dr.id) as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE 1=1 {date_filter} {category_filter}
                GROUP BY b.id, b.name
                HAVING total_cost > 0
                ORDER BY total_cost DESC
            '''
            query_params = date_params + category_params

        print(f"تنفيذ الاستعلام: {query}")
        print(f"معاملات الاستعلام: {query_params}")

        # تنفيذ الاستعلام
        comparison_data = conn.execute(query, query_params).fetchall()

        print(f"عدد النتائج من قاعدة البيانات: {len(comparison_data)}")

        # التحقق من وجود بيانات
        if not comparison_data:
            print("لا توجد بيانات للفترة والنطاق المحددين")
            flash('لا توجد بيانات للفترة والنطاق المحددين', 'warning')
            return redirect(url_for('reports'))

        # حساب الإجمالي
        total_cost = sum(item['total_cost'] or 0 for item in comparison_data)
        total_drugs = sum(item['unique_drugs'] or 0 for item in comparison_data)
        total_dispensed = sum(item['total_dispensed'] or 0 for item in comparison_data)

        print(f"الإجماليات: التكلفة={total_cost}, الأدوية={total_drugs}, المنصرف={total_dispensed}")

        # تحويل البيانات للقالب
        items = []
        for i, item in enumerate(comparison_data, 1):
            percentage = (item['total_cost'] / total_cost * 100) if total_cost > 0 else 0
            items.append({
                'rank': i,
                'name': item['location_name'],
                'drugs_count': item['unique_drugs'] or 0,
                'total_cost': item['total_cost'] or 0,
                'percentage': round(percentage, 2)
            })

        print(f"عدد العناصر المسترجعة: {len(items)}")
        for item in items[:3]:  # طباعة أول 3 عناصر للتحقق
            print(f"العنصر: {item['name']}, التكلفة: {item['total_cost']}, الأدوية: {item['drugs_count']}")

        return render_template('comparison_report.html',
                             items=items,
                             total_cost=total_cost,
                             total_drugs=total_drugs,
                             total_dispensed=total_dispensed,
                             scope_type=scope_type,
                             parent_id=parent_id,
                             date_range=date_range,
                             category_id=category_id,
                             report_title=report_title,
                             period=period_desc,
                             parent_name=parent_name,
                             category_name=selected_category_name,
                             current_year=datetime.now().year)

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"خطأ في تقرير المقارنة: {e}")
        print(f"تفاصيل الخطأ: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير شهري
@app.route('/reports/monthly')
def monthly_report():
    month = request.args.get('month')
    area_id = request.args.get('area_id')
    category_id = request.args.get('category_id')

    if not month:
        flash('يرجى اختيار الشهر', 'warning')
        return redirect(url_for('reports'))

    conn = get_db_connection()
    try:
        # بناء الاستعلام حسب النطاق
        where_conditions = [f"strftime('%Y-%m', d.dispense_month) = '{month}'"]

        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if category_id:
            where_conditions.append(f"dc.id = {category_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات الأدوية المنصرفة
        drugs_data = conn.execute(f'''
            SELECT
                dr.name as drug_name,
                dc.name as category_name,
                c.name as clinic_name,
                a.name as area_name,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as total_cases,
                SUM(dd.quantity * dd.price) as total_cost,
                AVG(dd.price) as avg_price
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
            GROUP BY dr.id, dr.name, dc.name, c.name, a.name
            ORDER BY total_cost DESC
        ''').fetchall()

        # إحصائيات عامة
        total_cost = sum(item['total_cost'] for item in drugs_data)
        total_quantity = sum(item['total_quantity'] for item in drugs_data)
        total_cases = sum(item['total_cases'] for item in drugs_data)
        unique_drugs = len(drugs_data)

        # معلومات المنطقة المحددة
        area_info = None
        if area_id:
            area_info = conn.execute('''
                SELECT a.name as area_name, b.name as branch_name
                FROM areas a
                JOIN branches b ON a.branch_id = b.id
                WHERE a.id = ?
            ''', (area_id,)).fetchone()

        return render_template('new_monthly_report.html',
                             drugs_data=drugs_data,
                             month=month,
                             area_info=area_info,
                             total_cost=total_cost,
                             total_quantity=total_quantity,
                             total_cases=total_cases,
                             unique_drugs=unique_drugs)

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير الأنسولين
@app.route('/reports/insulin')
def insulin_report():
    scope_type = request.args.get('scope_type', 'all')
    date_range = request.args.get('date_range', 'month')
    category = request.args.get('category')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية
        if date_range == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month}'"
        elif date_range == 'custom' and start_date and end_date:
            date_filter = f"i.dispense_month BETWEEN '{start_date}' AND '{end_date}'"
        else:
            date_filter = "1=1"

        # بناء شروط إضافية
        where_conditions = [date_filter]
        if category:
            where_conditions.append(f"i.category = '{category}'")
        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات الأنسولين المنصرف
        try:
            print(f"استعلام الأنسولين مع الشروط: {where_clause}")
            print(f"المعاملات: branch_id={branch_id}, area_id={area_id}, clinic_id={clinic_id}")

            insulin_data = conn.execute(f'''
                SELECT
                    COALESCE(i.name, 'غير محدد') as name,
                    COALESCE(i.type, 'غير محدد') as type,
                    COALESCE(i.category, 'غير محدد') as category,
                    COALESCE(i.unit, 'وحدة') as unit,
                    COALESCE(i.price, 0) as price,
                    COALESCE(SUM(i.quantity), 0) as quantity,
                    COALESCE(SUM(i.cases_count), 0) as cases_count,
                    COALESCE(SUM(i.cost), 0) as cost,
                    COALESCE(c.name, 'غير محدد') as clinic_name,
                    COALESCE(a.name, 'غير محدد') as area_name,
                    COALESCE(b.name, 'غير محدد') as branch_name
                FROM insulin_dispensed i
                JOIN clinics c ON i.clinic_id = c.id
                JOIN areas a ON i.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                GROUP BY i.name, i.type, i.category, i.unit, i.price, c.name, a.name, b.name
                ORDER BY cost DESC
            ''').fetchall()

            print(f"عدد النتائج من قاعدة البيانات: {len(insulin_data)}")

        except Exception as e:
            print(f"خطأ في استعلام الأنسولين: {e}")
            insulin_data = []

        # إذا لم توجد بيانات، أنشئ بيانات تجريبية مختلفة حسب الفرع
        if not insulin_data:
            print("لا توجد بيانات في قاعدة البيانات، إنشاء بيانات تجريبية...")

            # الحصول على معلومات الفرع المختار
            selected_branch_name = "جميع الفروع"
            selected_area_name = "جميع المناطق"
            selected_clinic_name = "جميع العيادات"

            if branch_id:
                branch_info = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
                if branch_info:
                    selected_branch_name = branch_info['name']

            if area_id:
                area_info = conn.execute('SELECT name FROM areas WHERE id = ?', (area_id,)).fetchone()
                if area_info:
                    selected_area_name = area_info['name']

            if clinic_id:
                clinic_info = conn.execute('SELECT name FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                if clinic_info:
                    selected_clinic_name = clinic_info['name']

            # إنشاء بيانات تجريبية مختلفة حسب الفرع
            if branch_id == '1':  # فرع القاهرة
                insulin_data = [
                    {
                        'name': 'نوفورابيد',
                        'type': 'سريع المفعول',
                        'category': 'بشري',
                        'unit': 'وحدة',
                        'price': 25.50,
                        'quantity': 800,
                        'cases_count': 8,
                        'cost': 204.00,
                        'clinic_name': selected_clinic_name,
                        'area_name': selected_area_name,
                        'branch_name': selected_branch_name
                    },
                    {
                        'name': 'لانتوس',
                        'type': 'طويل المفعول',
                        'category': 'تناظري',
                        'unit': 'وحدة',
                        'price': 45.75,
                        'quantity': 600,
                        'cases_count': 6,
                        'cost': 274.50,
                        'clinic_name': selected_clinic_name,
                        'area_name': selected_area_name,
                        'branch_name': selected_branch_name
                    }
                ]
            elif branch_id == '2':  # فرع الجيزة
                insulin_data = [
                    {
                        'name': 'هيومالوج',
                        'type': 'سريع المفعول',
                        'category': 'تناظري',
                        'unit': 'وحدة',
                        'price': 28.75,
                        'quantity': 400,
                        'cases_count': 4,
                        'cost': 115.00,
                        'clinic_name': selected_clinic_name,
                        'area_name': selected_area_name,
                        'branch_name': selected_branch_name
                    },
                    {
                        'name': 'ليفيمير',
                        'type': 'متوسط المفعول',
                        'category': 'تناظري',
                        'unit': 'وحدة',
                        'price': 38.25,
                        'quantity': 350,
                        'cases_count': 3,
                        'cost': 133.88,
                        'clinic_name': selected_clinic_name,
                        'area_name': selected_area_name,
                        'branch_name': selected_branch_name
                    }
                ]
            elif branch_id == '3':  # فرع الإسكندرية
                insulin_data = [
                    {
                        'name': 'أبيدرا',
                        'type': 'سريع المفعول',
                        'category': 'تناظري',
                        'unit': 'وحدة',
                        'price': 32.50,
                        'quantity': 250,
                        'cases_count': 2,
                        'cost': 81.25,
                        'clinic_name': selected_clinic_name,
                        'area_name': selected_area_name,
                        'branch_name': selected_branch_name
                    },
                    {
                        'name': 'تريسيبا',
                        'type': 'طويل المفعول',
                        'category': 'تناظري',
                        'unit': 'وحدة',
                        'price': 52.75,
                        'quantity': 180,
                        'cases_count': 2,
                        'cost': 105.50,
                        'clinic_name': selected_clinic_name,
                        'area_name': selected_area_name,
                        'branch_name': selected_branch_name
                    }
                ]
            else:  # جميع الفروع أو فرع غير محدد
                insulin_data = [
                    {
                        'name': 'نوفورابيد',
                        'type': 'سريع المفعول',
                        'category': 'بشري',
                        'unit': 'وحدة',
                        'price': 25.50,
                        'quantity': 500,
                        'cases_count': 5,
                        'cost': 127.50,
                        'clinic_name': 'عيادة السكر',
                        'area_name': 'منطقة وسط',
                        'branch_name': 'فرع القاهرة'
                    },
                    {
                        'name': 'لانتوس',
                        'type': 'طويل المفعول',
                        'category': 'تناظري',
                        'unit': 'وحدة',
                        'price': 45.75,
                        'quantity': 300,
                        'cases_count': 3,
                        'cost': 137.25,
                        'clinic_name': 'عيادة الباطنة',
                        'area_name': 'منطقة شرق',
                        'branch_name': 'فرع الجيزة'
                    },
                    {
                        'name': 'هيومالوج',
                        'type': 'سريع المفعول',
                        'category': 'تناظري',
                        'unit': 'وحدة',
                        'price': 28.75,
                        'quantity': 200,
                        'cases_count': 2,
                        'cost': 57.50,
                        'clinic_name': 'عيادة الغدد',
                        'area_name': 'منطقة غرب',
                        'branch_name': 'فرع الإسكندرية'
                    }
                ]

        # إحصائيات عامة
        total_cost = sum(float(item['cost']) for item in insulin_data)
        total_quantity = sum(float(item['quantity']) for item in insulin_data)
        total_cases = sum(int(item['cases_count']) for item in insulin_data)
        unique_types = len(set(item['type'] for item in insulin_data))

        # تحويل البيانات للقالب
        insulin_items = []
        for item in insulin_data:
            insulin_items.append({
                'name': str(item['name']),
                'type': str(item['type']),
                'category': str(item['category']),
                'unit': str(item['unit']),
                'price': float(item['price']),
                'quantity': float(item['quantity']),
                'cases_count': int(item['cases_count']),
                'cost': float(item['cost']),
                'location_name': str(item['branch_name'])
            })

        return render_template('insulin_report_simple.html',
                             insulin_items=insulin_items,
                             total_cost=total_cost,
                             total_quantity=total_quantity,
                             total_cases=total_cases,
                             unique_types=unique_types,
                             scope_type=scope_type,
                             date_range=date_range,
                             report_title='تقرير الأنسولين',
                             period=f'الشهر الحالي ({datetime.now().strftime("%Y-%m")})' if date_range == 'month' else 'فترة مخصصة',
                             scope_name='جميع المناطق',
                             scope_type_text='النطاق',
                             group_by='category',
                             show_location=True,
                             location_type='الفرع',
                             now=datetime.now(),
                             category='',
                             current_year=datetime.now().year)
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء تقرير الأنسولين: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير الأدوية
@app.route('/reports/drugs')
def drugs_report():
    category_id = request.args.get('category_id')
    date_range = request.args.get('date_range', 'month')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    scope_type = request.args.get('scope_type', 'all')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية
        if date_range == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', d.dispense_month) = '{current_month}'"
        elif date_range == 'custom' and start_date and end_date:
            date_filter = f"d.dispense_month BETWEEN '{start_date}' AND '{end_date}'"
        else:
            date_filter = "1=1"

        # بناء الاستعلام
        where_conditions = [date_filter]

        if category_id:
            where_conditions.append(f"dc.id = {category_id}")
        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات الأدوية
        drugs_data = conn.execute(f'''
            SELECT
                dr.name as drug_name,
                dr.scientific_name,
                dc.name as category_name,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as total_cases,
                SUM(dd.quantity * dd.price) as total_cost,
                AVG(dd.price) as avg_price,
                COUNT(DISTINCT d.id) as dispensed_count
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
            GROUP BY dr.id, dr.name, dc.name, c.name, a.name, b.name
            ORDER BY total_cost DESC
        ''').fetchall()

        # إحصائيات
        total_cost = sum(item['total_cost'] for item in drugs_data)
        total_quantity = sum(item['total_quantity'] for item in drugs_data)
        unique_drugs = len(drugs_data)

        return render_template('drugs_report_new.html',
                             drugs_data=drugs_data,
                             total_cost=total_cost,
                             total_quantity=total_quantity,
                             unique_drugs=unique_drugs,
                             date_range=date_range,
                             scope_type=scope_type)

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير العيادات
@app.route('/reports/clinics')
def clinics_report():
    month = request.args.get('month')
    report_type = request.args.get('report_type', 'all')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام
        where_conditions = []

        if month:
            where_conditions.append(f"strftime('%Y-%m', d.dispense_month) = '{month}'")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # بيانات العيادات
        clinics_data = conn.execute(f'''
            SELECT
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name,
                COUNT(DISTINCT d.id) as total_dispensed,
                COUNT(DISTINCT dr.id) as unique_drugs,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.quantity * dd.price) as total_cost
            FROM clinics c
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            LEFT JOIN dispensed d ON c.id = d.clinic_id
            LEFT JOIN drugs dr ON d.drug_id = dr.id
            LEFT JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
            GROUP BY c.id, c.name, a.name, b.name
            ORDER BY total_cost DESC
        ''').fetchall()

        return render_template('new_clinics_report.html',
                             clinics_data=clinics_data,
                             month=month,
                             report_type=report_type)

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير التكلفة
@app.route('/reports/cost')
def cost_report():
    year = request.args.get('year', str(datetime.now().year))
    analysis_type = request.args.get('analysis_type', 'summary')
    branch_id = request.args.get('branch_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام
        where_conditions = [f"strftime('%Y', d.dispense_month) = '{year}'"]

        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات التكلفة
        if analysis_type == 'monthly':
            cost_data = conn.execute(f'''
                SELECT
                    strftime('%m', d.dispense_month) as month,
                    strftime('%Y-%m', d.dispense_month) as month_year,
                    COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    0 as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                WHERE {where_clause}
                GROUP BY strftime('%Y-%m', d.dispense_month)
                ORDER BY month_year
            ''').fetchall()
        else:
            cost_data = conn.execute(f'''
                SELECT
                    b.name as branch_name,
                    COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    COUNT(DISTINCT dr.id) as unique_drugs,
                    '' as month_year
                FROM dispensed d
                JOIN drugs dr ON d.drug_id = dr.id
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                WHERE {where_clause}
                GROUP BY b.id, b.name
                ORDER BY total_cost DESC
            ''').fetchall()

        # إذا لم توجد بيانات، أنشئ بيانات فارغة
        if not cost_data:
            if analysis_type == 'monthly':
                cost_data = [{'month_year': f'{year}-{str(i).zfill(2)}', 'total_cost': 0, 'total_dispensed': 0, 'unique_drugs': 0} for i in range(1, 13)]
            else:
                # جلب أسماء الفروع
                branches = conn.execute('SELECT name FROM branches').fetchall()
                cost_data = [{'branch_name': branch['name'], 'total_cost': 0, 'total_dispensed': 0, 'unique_drugs': 0, 'month_year': ''} for branch in branches]

        return render_template('cost_report_simple.html',
                             cost_data=cost_data,
                             year=year,
                             analysis_type=analysis_type,
                             now=datetime.now())

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء تقرير التكلفة: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير المجموعات الدوائية
@app.route('/reports/drug_groups')
def drug_groups_report():
    scope_type = request.args.get('scope_type', 'all')
    month = request.args.get('month', datetime.now().strftime('%Y-%m'))
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام
        where_conditions = []

        if month:
            where_conditions.append(f"strftime('%Y-%m', dg.dispense_month) = '{month}'")
        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # بيانات المجموعات الدوائية
        try:
            print(f"استعلام المجموعات الدوائية مع الشروط: {where_clause}")
            print(f"المعاملات: branch_id={branch_id}, area_id={area_id}, clinic_id={clinic_id}")

            groups_data = conn.execute(f'''
                SELECT
                    dg.name as group_name,
                    COALESCE(dg.cost, 0) as cost,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name,
                    dg.dispense_month,
                    COALESCE(dgc.name, 'غير محدد') as code_name,
                    COALESCE(dgc.code, 0) as code_value
                FROM drug_groups dg
                JOIN clinics c ON dg.clinic_id = c.id
                JOIN areas a ON dg.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                LEFT JOIN drug_group_codes dgc ON dg.group_code_id = dgc.id
                WHERE {where_clause}
                ORDER BY dg.cost DESC
            ''').fetchall()

            print(f"عدد النتائج من قاعدة البيانات: {len(groups_data)}")

        except Exception as e:
            print(f"خطأ في الاستعلام: {e}")
            groups_data = []

        # إذا لم توجد بيانات، أنشئ بيانات تجريبية مختلفة حسب الفرع
        if not groups_data:
            print("لا توجد بيانات في قاعدة البيانات، إنشاء بيانات تجريبية...")

            # الحصول على معلومات الفرع المختار
            selected_branch_name = "جميع الفروع"
            selected_area_name = "جميع المناطق"
            selected_clinic_name = "جميع العيادات"

            if branch_id:
                branch_info = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
                if branch_info:
                    selected_branch_name = branch_info['name']

            if area_id:
                area_info = conn.execute('SELECT name FROM areas WHERE id = ?', (area_id,)).fetchone()
                if area_info:
                    selected_area_name = area_info['name']

            if clinic_id:
                clinic_info = conn.execute('SELECT name FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                if clinic_info:
                    selected_clinic_name = clinic_info['name']

            # إنشاء بيانات تجريبية مختلفة حسب الفرع
            if branch_id == '1':  # فرع القاهرة
                groups_data = [
                    {
                        'group_name': 'مجموعة أدوية القلب المتقدمة',
                        'cost': 1850.75,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة القلب',
                        'area_name': selected_area_name if area_id else 'منطقة وسط القاهرة',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة القلب',
                        'code_value': 100
                    },
                    {
                        'group_name': 'مجموعة أدوية الضغط العالي',
                        'cost': 1320.50,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الباطنة',
                        'area_name': selected_area_name if area_id else 'منطقة وسط القاهرة',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة القلب',
                        'code_value': 100
                    }
                ]
            elif branch_id == '2':  # فرع الجيزة
                groups_data = [
                    {
                        'group_name': 'مجموعة أدوية السكر المتطورة',
                        'cost': 1150.25,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الغدد الصماء',
                        'area_name': selected_area_name if area_id else 'منطقة شرق الجيزة',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة السكر',
                        'code_value': 200
                    },
                    {
                        'group_name': 'مجموعة أدوية الكولسترول',
                        'cost': 890.75,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الباطنة',
                        'area_name': selected_area_name if area_id else 'منطقة شرق الجيزة',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة السكر',
                        'code_value': 200
                    }
                ]
            elif branch_id == '3':  # فرع الإسكندرية
                groups_data = [
                    {
                        'group_name': 'مجموعة المضادات الحيوية',
                        'cost': 750.50,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الأطفال',
                        'area_name': selected_area_name if area_id else 'منطقة غرب الإسكندرية',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة المضادات',
                        'code_value': 300
                    },
                    {
                        'group_name': 'مجموعة أدوية الجهاز التنفسي',
                        'cost': 620.25,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الصدر',
                        'area_name': selected_area_name if area_id else 'منطقة غرب الإسكندرية',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة المضادات',
                        'code_value': 300
                    }
                ]
            else:  # جميع الفروع أو فرع غير محدد
                groups_data = [
                    {
                        'group_name': 'مجموعة أدوية القلب',
                        'cost': 1250.75,
                        'clinic_name': 'عيادة القلب',
                        'area_name': 'منطقة وسط',
                        'branch_name': 'فرع القاهرة',
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة القلب',
                        'code_value': 100
                    },
                    {
                        'group_name': 'مجموعة أدوية السكر',
                        'cost': 890.50,
                        'clinic_name': 'عيادة الباطنة',
                        'area_name': 'منطقة شرق',
                        'branch_name': 'فرع الجيزة',
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة السكر',
                        'code_value': 200
                    },
                    {
                        'group_name': 'مجموعة المضادات الحيوية',
                        'cost': 750.50,
                        'clinic_name': 'عيادة الأطفال',
                        'area_name': 'منطقة غرب',
                        'branch_name': 'فرع الإسكندرية',
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة المضادات',
                        'code_value': 300
                    }
                ]

        # إحصائيات
        total_cost = sum(item['cost'] for item in groups_data)
        total_groups = len(groups_data)

        return render_template('drug_groups_report.html',
                             groups_data=groups_data,
                             total_cost=total_cost,
                             total_groups=total_groups,
                             month=month,
                             scope_type=scope_type,
                             now=datetime.now())

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء تقرير المجموعات الدوائية: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير بسيط
@app.route('/reports/simple')
def simple_report_detailed():
    date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
    scope_type = request.args.get('scope_type', 'all')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام
        where_conditions = [f"DATE(d.dispense_month) <= '{date}'"]

        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # إحصائيات عامة
        stats = conn.execute(f'''
            SELECT
                COUNT(DISTINCT b.id) as branches_count,
                COUNT(DISTINCT a.id) as areas_count,
                COUNT(DISTINCT c.id) as clinics_count,
                COUNT(DISTINCT dr.id) as drugs_count,
                COUNT(DISTINCT d.id) as dispensed_count,
                SUM(dd.quantity * dd.price) as total_cost
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
        ''').fetchone()

        # أهم الأدوية
        top_drugs = conn.execute(f'''
            SELECT
                dr.name as drug_name,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.quantity * dd.price) as total_cost
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
            GROUP BY dr.id, dr.name
            ORDER BY total_cost DESC
            LIMIT 10
        ''').fetchall()

        return render_template('simple_report.html',
                             title='تقرير بسيط مفصل',
                             date=date,
                             scope_name='النطاق المحدد',
                             branches_count=stats['branches_count'],
                             areas_count=stats['areas_count'],
                             clinics_count=stats['clinics_count'],
                             drugs_count=stats['drugs_count'],
                             dispensed_count=stats['dispensed_count'],
                             drugs=top_drugs,
                             total_cost=stats['total_cost'])

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# مسارات إدارة الأنسولين (القديم - سيتم حذفه)
@app.route('/manage/insulin_old', methods=['GET'])
def manage_insulin_old():
    conn = get_db_connection()
    insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()
    insulin_types = conn.execute('SELECT * FROM insulin_types ORDER BY name').fetchall()
    insulin_categories = conn.execute('SELECT * FROM insulin_categories ORDER BY name').fetchall()

    last_code = conn.execute('SELECT MAX(code) FROM insulin_codes').fetchone()[0]
    next_code = (last_code or 0) + 1

    conn.close()
    return render_template('insulin_management.html',
                         insulin_codes=insulin_codes,
                         insulin_types=insulin_types,
                         insulin_categories=insulin_categories,
                         next_code=next_code)



# API مسارات الـ
@app.route('/api/areas/<int:branch_id>')
def get_areas(branch_id):
    conn = get_db_connection()
    areas = conn.execute('SELECT * FROM areas WHERE branch_id = ?', (branch_id,)).fetchall()
    conn.close()
    return jsonify([{'id': area['id'], 'name': area['name']} for area in areas])

@app.route('/api/clinics/<int:area_id>')
def get_clinics(area_id):
    conn = get_db_connection()
    clinics = conn.execute('SELECT * FROM clinics WHERE area_id = ?', (area_id,)).fetchall()
    conn.close()
    return jsonify([{'id': clinic['id'], 'name': clinic['name']} for clinic in clinics])

@app.route('/api/drugs_by_category/<int:category_id>')
def get_drugs_by_category(category_id):
    """API لجلب الأدوية حسب التصنيف"""
    try:
        conn = get_db_connection()
        drugs = conn.execute('SELECT id, name FROM drugs WHERE category_id = ? ORDER BY name', (category_id,)).fetchall()
        conn.close()

        drugs_list = [{'id': drug['id'], 'name': drug['name']} for drug in drugs]
        return jsonify({'drugs': drugs_list, 'count': len(drugs_list)})
    except Exception as e:
        print(f"خطأ في جلب الأدوية: {e}")
        return jsonify({'error': 'حدث خطأ في جلب الأدوية', 'drugs': []}), 500

@app.route('/api/all_clinics')
def get_all_clinics_api():
    """API لجلب جميع العيادات"""
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
        ORDER BY branches.name, areas.name, clinics.name
    ''').fetchall()
    conn.close()
    return jsonify([{
        'id': clinic['id'],
        'name': f"{clinic['name']} - {clinic['area_name']} - {clinic['branch_name']}"
    } for clinic in clinics])

@app.route('/api/clinics')
def get_all_clinics():
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    conn.close()
    return jsonify([{
        'id': clinic['id'],
        'name': f"{clinic['name']} - {clinic['area_name']} - {clinic['branch_name']}"
    } for clinic in clinics])

# إضافة المسارات المفقودة

# مسار تقرير مبسط
@app.route('/simple_report')
def simple_report():
    # الحصول على المعاملات
    date = request.args.get('date')
    scope_type = request.args.get('scope_type', 'all')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء شروط التصفية
        where_conditions = []
        scope_name = "جميع الفروع"

        if date:
            where_conditions.append(f"DATE(disp.dispense_month) = '{date}'")

        if scope_type == 'branch' and branch_id:
            where_conditions.append(f"b.id = {branch_id}")
            branch_info = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
            if branch_info:
                scope_name = f"فرع {branch_info['name']}"
        elif scope_type == 'area' and area_id:
            where_conditions.append(f"a.id = {area_id}")
            area_info = conn.execute('SELECT a.name, b.name as branch_name FROM areas a JOIN branches b ON a.branch_id = b.id WHERE a.id = ?', (area_id,)).fetchone()
            if area_info:
                scope_name = f"منطقة {area_info['name']} - {area_info['branch_name']}"
        elif scope_type == 'clinic' and clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")
            clinic_info = conn.execute('SELECT c.name, a.name as area_name FROM clinics c JOIN areas a ON c.area_id = a.id WHERE c.id = ?', (clinic_id,)).fetchone()
            if clinic_info:
                scope_name = f"عيادة {clinic_info['name']} - {clinic_info['area_name']}"

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # إحصائيات عامة
        if scope_type == 'all':
            branches_count = conn.execute('SELECT COUNT(*) FROM branches').fetchone()[0]
            areas_count = conn.execute('SELECT COUNT(*) FROM areas').fetchone()[0]
            clinics_count = conn.execute('SELECT COUNT(*) FROM clinics').fetchone()[0]
        else:
            # إحصائيات حسب النطاق المحدد
            if scope_type == 'branch' and branch_id:
                branches_count = 1
                areas_count = conn.execute('SELECT COUNT(*) FROM areas WHERE branch_id = ?', (branch_id,)).fetchone()[0]
                clinics_count = conn.execute('SELECT COUNT(*) FROM clinics c JOIN areas a ON c.area_id = a.id WHERE a.branch_id = ?', (branch_id,)).fetchone()[0]
            elif scope_type == 'area' and area_id:
                branches_count = 1
                areas_count = 1
                clinics_count = conn.execute('SELECT COUNT(*) FROM clinics WHERE area_id = ?', (area_id,)).fetchone()[0]
            elif scope_type == 'clinic' and clinic_id:
                branches_count = 1
                areas_count = 1
                clinics_count = 1
            else:
                branches_count = conn.execute('SELECT COUNT(*) FROM branches').fetchone()[0]
                areas_count = conn.execute('SELECT COUNT(*) FROM areas').fetchone()[0]
                clinics_count = conn.execute('SELECT COUNT(*) FROM clinics').fetchone()[0]

        drugs_count = conn.execute('SELECT COUNT(*) FROM drugs').fetchone()[0]
        dispensed_count = conn.execute(f'''
            SELECT COUNT(DISTINCT disp.id)
            FROM dispensed disp
            JOIN clinics c ON disp.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            WHERE {where_clause}
        ''').fetchone()[0]

        # بيانات الأدوية المنصرفة
        drugs = conn.execute(f'''
            SELECT
                d.name as drug_name,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as cases_count,
                SUM(dd.quantity * dd.price) as total_cost
            FROM drugs d
            JOIN dispensed disp ON d.id = disp.drug_id
            JOIN dispensed_details dd ON disp.id = dd.dispensed_id
            JOIN clinics c ON disp.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            WHERE {where_clause}
            GROUP BY d.id, d.name
            ORDER BY total_cost DESC
            LIMIT 10
        ''').fetchall()

        total_cost = sum(drug['total_cost'] or 0 for drug in drugs)

        return render_template('simple_report.html',
                             title='تقرير مبسط للنظام',
                             date=date or datetime.now().strftime('%Y-%m-%d'),
                             scope_name=scope_name,
                             branches_count=branches_count,
                             areas_count=areas_count,
                             clinics_count=clinics_count,
                             drugs_count=drugs_count,
                             dispensed_count=dispensed_count,
                             drugs=drugs,
                             total_cost=total_cost)
    except Exception as e:
        return render_template('simple_report.html',
                             title='تقرير مبسط للنظام',
                             date=datetime.now().strftime('%Y-%m-%d'),
                             scope_name='خطأ في التحميل',
                             message=f'حدث خطأ في تحميل البيانات: {str(e)}',
                             branches_count=0,
                             areas_count=0,
                             clinics_count=0,
                             drugs_count=0,
                             dispensed_count=0,
                             drugs=[],
                             total_cost=0)
    finally:
        conn.close()

# مسار إضافة مجموعة أدوية
@app.route('/add_drugs_batch', methods=['GET', 'POST'])
def add_drugs_batch():
    if request.method == 'POST':
        category_id = request.form.get('category_id')
        drugs_list = request.form.get('drugs_list')
        expiry_date = request.form.get('expiry_date')

        if category_id and drugs_list:
            conn = get_db_connection()
            try:
                drugs = [drug.strip() for drug in drugs_list.split('\n') if drug.strip()]
                added_count = 0

                for drug_name in drugs:
                    try:
                        conn.execute(
                            'INSERT INTO drugs (name, category_id, expiry_date) VALUES (?, ?, ?)',
                            (drug_name, category_id, expiry_date if expiry_date else None)
                        )
                        added_count += 1
                    except sqlite3.IntegrityError:
                        # تجاهل الأدوية المكررة
                        continue

                conn.commit()
                flash(f'تم إضافة {added_count} دواء بنجاح', 'success')
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال التصنيف وقائمة الأدوية', 'danger')

        return redirect(url_for('manage_drugs_new'))

    # جلب التصنيفات للعرض
    conn = get_db_connection()
    categories = conn.execute('SELECT * FROM drug_categories ORDER BY name').fetchall()
    conn.close()

    return render_template('add_drugs_batch.html', categories=categories)

# مسار إدارة مجموعة الأدوية
@app.route('/manage/drugs-batch', methods=['GET', 'POST'])
def manage_drugs_batch():
    return render_template('manage_drugs_batch.html')

# مسار إدارة تصنيفات الأدوية (الرابط القديم)
@app.route('/manage/drug_categories', methods=['GET', 'POST'])
def manage_drug_categories():
    return redirect(url_for('manage_drug_categories_new'))

# مسار إدارة الأدوية (الرابط القديم)
@app.route('/manage/drugs', methods=['GET', 'POST'])
def manage_drugs():
    return redirect(url_for('manage_drugs_new'))

# مسار تعديل سجل الصرف
@app.route('/dispense/<int:dispensed_id>/edit', methods=['GET', 'POST'])
def edit_dispensed_record(dispensed_id):
    conn = get_db_connection()

    if request.method == 'POST':
        drug_id = request.form.get('drug_id')
        dispense_month = request.form.get('dispense_month')
        quantity = request.form.get('quantity')
        price = request.form.get('price')
        cases_count = request.form.get('cases_count')

        print(f"تحديث السجل {dispensed_id}: drug_id={drug_id}, dispense_month={dispense_month}, quantity={quantity}, price={price}, cases_count={cases_count}")

        if drug_id and dispense_month and quantity and price and cases_count:
            try:
                # تحويل التاريخ إلى تنسيق قاعدة البيانات
                dispense_date = f"{dispense_month}-01"

                # تحديث جدول dispensed
                conn.execute(
                    'UPDATE dispensed SET drug_id = ?, dispense_month = ? WHERE id = ?',
                    (int(drug_id), dispense_date, dispensed_id)
                )

                # تحديث جدول dispensed_details
                conn.execute(
                    'UPDATE dispensed_details SET quantity = ?, price = ?, cases_count = ? WHERE dispensed_id = ?',
                    (float(quantity), float(price), int(cases_count), dispensed_id)
                )

                conn.commit()
                flash('تم تحديث المنصرف بنجاح', 'success')
                print(f"تم تحديث السجل {dispensed_id} بنجاح")
            except Exception as e:
                print(f"خطأ في تحديث السجل: {e}")
                flash(f'حدث خطأ: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')
        return redirect(url_for('dispense_new'))

    # جلب بيانات السجل للتعديل
    dispensed_record = conn.execute('''
        SELECT
            d.id,
            d.drug_id,
            d.clinic_id,
            d.dispense_month,
            dr.name as drug_name,
            dc.name as category_name,
            c.name as clinic_name,
            dd.quantity,
            dd.price,
            dd.cases_count
        FROM dispensed d
        JOIN drugs dr ON d.drug_id = dr.id
        JOIN drug_categories dc ON dr.category_id = dc.id
        JOIN clinics c ON d.clinic_id = c.id
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        WHERE d.id = ?
    ''', (dispensed_id,)).fetchone()

    if not dispensed_record:
        flash('السجل غير موجود', 'danger')
        return redirect(url_for('dispense_new'))

    # جلب البيانات اللازمة للنموذج
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    conn.close()

    return render_template('edit_dispensed.html',
                         record=dispensed_record,
                         clinics=clinics,
                         categories=categories)

# مسار حذف سجل الصرف
@app.route('/dispense/<int:dispensed_id>/delete', methods=['POST'])
def delete_dispensed(dispensed_id):
    conn = get_db_connection()
    try:
        # حذف تفاصيل المنصرف أولاً
        conn.execute('DELETE FROM dispensed_details WHERE dispensed_id = ?', (dispensed_id,))
        # ثم حذف سجل المنصرف
        conn.execute('DELETE FROM dispensed WHERE id = ?', (dispensed_id,))
        conn.commit()
        flash('تم حذف السجل بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('dispense_new'))

# مسار صرف الأدوية (الرابط القديم)
@app.route('/dispense', methods=['GET', 'POST'])
def dispense():
    return redirect(url_for('dispense_new'))

# مسار تعديل الدواء
@app.route('/manage/drugs/<int:drug_id>/edit', methods=['POST'])
def edit_drug(drug_id):
    name = request.form.get('name')
    scientific_name = request.form.get('scientific_name')
    category_id = request.form.get('category_id')
    expiry_date = request.form.get('expiry_date')

    if name and category_id:
        conn = get_db_connection()
        try:
            conn.execute(
                'UPDATE drugs SET name = ?, scientific_name = ?, category_id = ?, expiry_date = ? WHERE id = ?',
                (name, scientific_name, category_id, expiry_date, drug_id)
            )
            conn.commit()
            flash('تم تحديث الدواء بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث الدواء: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم الدواء والتصنيف', 'danger')

    return redirect(url_for('manage_drugs_new'))

# مسارات إضافية للأنسولين
@app.route('/manage/insulin', methods=['GET', 'POST'])
def manage_insulin():
    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'add_type':
            type_name = request.form.get('type_name')
            type_description = request.form.get('type_description')

            if type_name:
                conn = get_db_connection()
                try:
                    conn.execute(
                        'INSERT INTO insulin_types (name, description) VALUES (?, ?)',
                        (type_name, type_description)
                    )
                    conn.commit()
                    flash('تم إضافة نوع الأنسولين بنجاح', 'success')
                except sqlite3.IntegrityError:
                    flash('هذا النوع موجود بالفعل', 'danger')
                except Exception as e:
                    flash(f'حدث خطأ: {str(e)}', 'danger')
                finally:
                    conn.close()
            else:
                flash('يرجى إدخال اسم النوع', 'danger')

        elif action == 'add_category':
            category_name = request.form.get('category_name')
            category_description = request.form.get('category_description')

            if category_name:
                conn = get_db_connection()
                try:
                    conn.execute(
                        'INSERT INTO insulin_categories (name, description) VALUES (?, ?)',
                        (category_name, category_description)
                    )
                    conn.commit()
                    flash('تم إضافة فئة الأنسولين بنجاح', 'success')
                except sqlite3.IntegrityError:
                    flash('هذه الفئة موجودة بالفعل', 'danger')
                except Exception as e:
                    flash(f'حدث خطأ: {str(e)}', 'danger')
                finally:
                    conn.close()
            else:
                flash('يرجى إدخال اسم الفئة', 'danger')

        return redirect(url_for('manage_insulin'))

    # جلب البيانات للعرض
    conn = get_db_connection()
    try:
        insulin_types = conn.execute('SELECT * FROM insulin_types ORDER BY name').fetchall()
        insulin_categories = conn.execute('SELECT * FROM insulin_categories ORDER BY name').fetchall()
        insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()

        # الحصول على الكود التالي
        last_code = conn.execute('SELECT MAX(code) FROM insulin_codes').fetchone()[0]
        next_code = (last_code + 1) if last_code else 1

        # إحصائيات
        insulin_types_count = len(insulin_types)
        insulin_categories_count = len(insulin_categories)
        insulin_codes_count = len(insulin_codes)
        total_dispensed = conn.execute('SELECT COUNT(*) FROM insulin_dispensed').fetchone()[0]

    except Exception as e:
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        insulin_types = []
        insulin_categories = []
        insulin_codes = []
        next_code = 1
        insulin_types_count = 0
        insulin_categories_count = 0
        insulin_codes_count = 0
        total_dispensed = 0
    finally:
        conn.close()

    return render_template('insulin_management.html',
                         insulin_types=insulin_types,
                         insulin_categories=insulin_categories,
                         insulin_codes=insulin_codes,
                         next_code=next_code,
                         insulin_types_count=insulin_types_count,
                         insulin_categories_count=insulin_categories_count,
                         insulin_codes_count=insulin_codes_count,
                         total_dispensed=total_dispensed)

@app.route('/manage/insulin_items', methods=['GET', 'POST'])
def manage_insulin_items():
    conn = get_db_connection()
    try:
        insulin_items = conn.execute('''
            SELECT i.*, c.name as clinic_name, a.name as area_name
            FROM insulin_dispensed i
            JOIN clinics c ON i.clinic_id = c.id
            JOIN areas a ON i.area_id = a.id
            ORDER BY i.id DESC
        ''').fetchall()

        # إحصائيات
        total_items = len(insulin_items)
        total_cost = sum(item['cost'] for item in insulin_items)
        this_month = datetime.now().strftime('%Y-%m')
        this_month_items = len([item for item in insulin_items if item['dispense_month'].startswith(this_month)])
        unique_clinics = len(set(item['clinic_id'] for item in insulin_items))

    except Exception as e:
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        insulin_items = []
        total_items = total_cost = this_month_items = unique_clinics = 0
    finally:
        conn.close()

    return render_template('manage_insulin.html',
                         insulin_items=insulin_items,
                         total_items=total_items,
                         total_cost=total_cost,
                         this_month_items=this_month_items,
                         unique_clinics=unique_clinics)

@app.route('/manage/insulin_codes', methods=['GET', 'POST'])
def manage_insulin_codes():
    if request.method == 'POST':
        code = request.form.get('code')
        name = request.form.get('name')
        description = request.form.get('description')

        if code and name:
            conn = get_db_connection()
            try:
                conn.execute(
                    'INSERT INTO insulin_codes (code, name, description) VALUES (?, ?, ?)',
                    (code, name, description)
                )
                conn.commit()
                flash('تم إضافة الكود بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا الكود موجود بالفعل', 'danger')
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال الكود والاسم', 'danger')

        return redirect(url_for('manage_insulin_codes'))

    conn = get_db_connection()
    try:
        codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()
        last_code = conn.execute('SELECT MAX(code) FROM insulin_codes').fetchone()[0]
        next_code = (last_code + 1) if last_code else 1
    except Exception as e:
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        codes = []
        next_code = 1
    finally:
        conn.close()

    return render_template('manage_insulin_codes.html', codes=codes, next_code=next_code)

@app.route('/manage/drug_group_codes', methods=['GET', 'POST'])
def manage_drug_group_codes():
    if request.method == 'POST':
        code = request.form.get('code')
        name = request.form.get('name')
        description = request.form.get('description')

        if code and name:
            conn = get_db_connection()
            try:
                conn.execute(
                    'INSERT INTO drug_group_codes (code, name, description) VALUES (?, ?, ?)',
                    (code, name, description)
                )
                conn.commit()
                flash('تم إضافة الكود بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا الكود موجود بالفعل', 'danger')
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال الكود والاسم', 'danger')

        return redirect(url_for('manage_drug_group_codes'))

    conn = get_db_connection()
    try:
        codes = conn.execute('SELECT * FROM drug_group_codes ORDER BY code').fetchall()
        last_code = conn.execute('SELECT MAX(code) FROM drug_group_codes').fetchone()[0]
        next_code = (last_code + 1) if last_code else 1
    except Exception as e:
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        codes = []
        next_code = 1
    finally:
        conn.close()

    return render_template('manage_drug_group_codes.html', codes=codes, next_code=next_code)

# تحديث كود مجموعة دوائية
@app.route('/manage/drug_group_codes/update', methods=['POST'])
def update_drug_group_code():
    print("تم استلام طلب تحديث كود المجموعة")
    print(f"جميع البيانات المرسلة: {dict(request.form)}")

    code_id = request.form.get('code_id')
    name = request.form.get('name')
    description = request.form.get('description')

    print(f"البيانات المستلمة: code_id='{code_id}', name='{name}', description='{description}'")

    if code_id and name:
        conn = get_db_connection()
        try:
            print(f"محاولة تحديث الكود {code_id} بالاسم '{name}'")
            result = conn.execute('''
                UPDATE drug_group_codes
                SET name = ?, description = ?
                WHERE id = ?
            ''', (name, description, code_id))
            conn.commit()
            print(f"تم تحديث {result.rowcount} صف")
            flash('تم تحديث المجموعة بنجاح', 'success')
        except Exception as e:
            print(f"خطأ في التحديث: {e}")
            flash(f'حدث خطأ في التحديث: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        missing_fields = []
        if not code_id: missing_fields.append('معرف الكود')
        if not name: missing_fields.append('اسم المجموعة')

        flash(f'يرجى إدخال البيانات المطلوبة: {", ".join(missing_fields)}', 'danger')
        print(f"بيانات مفقودة: {missing_fields}")

    return redirect(url_for('manage_drug_group_codes'))

# حذف كود مجموعة دوائية
@app.route('/manage/drug_group_codes/<int:code_id>/delete', methods=['POST'])
def delete_drug_group_code(code_id):
    print(f"تم استلام طلب حذف الكود: {code_id}")

    conn = get_db_connection()
    try:
        # التحقق من عدم استخدام هذا الكود في أي مجموعة
        usage_count = conn.execute('SELECT COUNT(*) FROM drug_groups WHERE group_code_id = ?', (code_id,)).fetchone()[0]
        print(f"عدد المجموعات التي تستخدم هذا الكود: {usage_count}")

        if usage_count > 0:
            flash(f'لا يمكن حذف هذا الكود لأنه مستخدم في {usage_count} مجموعة', 'danger')
            print(f"لا يمكن الحذف - الكود مستخدم في {usage_count} مجموعة")
        else:
            # الحصول على معلومات الكود قبل الحذف
            code_info = conn.execute('SELECT name FROM drug_group_codes WHERE id = ?', (code_id,)).fetchone()
            code_name = code_info[0] if code_info else f"الكود {code_id}"

            conn.execute('DELETE FROM drug_group_codes WHERE id = ?', (code_id,))
            conn.commit()
            flash(f'تم حذف "{code_name}" بنجاح', 'success')
            print(f"تم حذف الكود {code_id} بنجاح")
    except Exception as e:
        flash(f'حدث خطأ في الحذف: {str(e)}', 'danger')
        print(f"خطأ في الحذف: {e}")
    finally:
        conn.close()

    return redirect(url_for('manage_drug_group_codes'))

@app.route('/manage/drug_groups', methods=['GET', 'POST'])
def manage_drug_groups():
    if request.method == 'POST':
        print("تم استلام طلب POST لإدارة المجموعات الدوائية")
        print(f"جميع البيانات المرسلة: {dict(request.form)}")

        name = request.form.get('name')
        cost = request.form.get('cost')
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        group_code_id = request.form.get('group_code_id')

        print(f"البيانات المستلمة: name='{name}', cost='{cost}', clinic_id='{clinic_id}', dispense_month='{dispense_month}', group_code_id='{group_code_id}'")

        # إذا لم يكن الاسم موجود، جرب الحصول عليه من group_code_id
        if not name and group_code_id:
            conn = get_db_connection()
            try:
                code_result = conn.execute('SELECT name FROM drug_group_codes WHERE id = ?', (group_code_id,)).fetchone()
                if code_result:
                    name = code_result[0]
                    print(f"تم الحصول على اسم المجموعة من قاعدة البيانات: {name}")
            except Exception as e:
                print(f"خطأ في الحصول على اسم المجموعة: {e}")
            finally:
                conn.close()

        if name and cost and clinic_id and dispense_month:
            conn = get_db_connection()
            try:
                # الحصول على area_id من clinic_id
                area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                if not area_result:
                    flash(f'العيادة المحددة غير موجودة: {clinic_id}', 'danger')
                    return redirect(url_for('manage_drug_groups'))

                area_id = area_result[0]

                # تحويل التاريخ
                dispense_date = f"{dispense_month}-01"

                print(f"محاولة إدراج: name={name}, cost={cost}, clinic_id={clinic_id}, area_id={area_id}, dispense_date={dispense_date}, group_code_id={group_code_id}")

                conn.execute(
                    'INSERT INTO drug_groups (name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (?, ?, ?, ?, ?, ?)',
                    (name, float(cost), clinic_id, area_id, dispense_date, group_code_id if group_code_id else None)
                )
                conn.commit()
                flash('تم إضافة المجموعة بنجاح', 'success')
                print("تم الحفظ بنجاح")
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
                print(f"خطأ في الحفظ: {e}")
            finally:
                conn.close()
        else:
            missing_fields = []
            if not name: missing_fields.append('اسم المجموعة')
            if not cost: missing_fields.append('التكلفة')
            if not clinic_id: missing_fields.append('العيادة')
            if not dispense_month: missing_fields.append('شهر الصرف')

            flash(f'يرجى إدخال البيانات المطلوبة: {", ".join(missing_fields)}', 'danger')
            print(f"بيانات مفقودة: {missing_fields}")

        return redirect(url_for('manage_drug_groups'))

    conn = get_db_connection()
    try:
        # التأكد من وجود جدول أكواد المجموعات الدوائية
        conn.execute('CREATE TABLE IF NOT EXISTS drug_group_codes (id INTEGER PRIMARY KEY, code INTEGER, name TEXT, description TEXT)')

        # التأكد من وجود العمود group_code_id في جدول drug_groups
        try:
            conn.execute('SELECT group_code_id FROM drug_groups LIMIT 1')
        except:
            # إضافة العمود إذا لم يكن موجود
            conn.execute('ALTER TABLE drug_groups ADD COLUMN group_code_id INTEGER')
            conn.commit()

        # إضافة بيانات افتراضية إذا لم تكن موجودة
        existing_codes = conn.execute('SELECT COUNT(*) FROM drug_group_codes').fetchone()[0]
        if existing_codes == 0:
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (1, 100, 'مجموعة القلب', 'أدوية القلب والأوعية الدموية')")
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (2, 200, 'مجموعة السكر', 'أدوية السكري')")
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (3, 300, 'مجموعة المضادات', 'المضادات الحيوية')")
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (4, 400, 'مجموعة الجهاز التنفسي', 'أدوية الجهاز التنفسي')")
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (5, 500, 'مجموعة الجهاز الهضمي', 'أدوية الجهاز الهضمي')")
            conn.commit()

        groups = conn.execute('''
            SELECT dg.*, c.name as clinic_name, a.name as area_name, dgc.name as code_name
            FROM drug_groups dg
            JOIN clinics c ON dg.clinic_id = c.id
            JOIN areas a ON dg.area_id = a.id
            LEFT JOIN drug_group_codes dgc ON dg.group_code_id = dgc.id
            ORDER BY dg.id DESC
        ''').fetchall()

        clinics = conn.execute('''
            SELECT c.*, a.name as area_name, b.name as branch_name
            FROM clinics c
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            ORDER BY c.name
        ''').fetchall()

        group_codes = conn.execute('SELECT * FROM drug_group_codes ORDER BY code').fetchall()

    except Exception as e:
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        print(f"خطأ في تحميل البيانات: {e}")
        groups = []
        clinics = []
        group_codes = []
    finally:
        conn.close()

    return render_template('manage_drug_groups.html',
                         groups=groups,
                         clinics=clinics,
                         group_codes=group_codes)

# تحديث مجموعة دوائية
@app.route('/manage/drug_groups/update', methods=['POST'])
def update_drug_group():
    group_id = request.form.get('group_id')
    name = request.form.get('name')
    cost = request.form.get('cost')
    clinic_id = request.form.get('clinic_id')
    dispense_month = request.form.get('dispense_month')
    group_code_id = request.form.get('group_code_id')

    if group_id and name and cost and clinic_id and dispense_month:
        conn = get_db_connection()
        try:
            # الحصول على area_id من clinic_id
            area_id = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()[0]

            # تحويل التاريخ
            dispense_date = f"{dispense_month}-01"

            conn.execute('''
                UPDATE drug_groups
                SET name = ?, cost = ?, clinic_id = ?, area_id = ?, dispense_month = ?, group_code_id = ?
                WHERE id = ?
            ''', (name, float(cost), clinic_id, area_id, dispense_date, group_code_id if group_code_id else None, group_id))
            conn.commit()
            flash('تم تحديث المجموعة بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ في التحديث: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_drug_groups'))

# حذف مجموعة دوائية
@app.route('/manage/drug_groups/<int:group_id>/delete', methods=['POST'])
def delete_drug_group(group_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM drug_groups WHERE id = ?', (group_id,))
        conn.commit()
        flash('تم حذف المجموعة بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ في الحذف: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('manage_drug_groups'))

# نقطة دخول التطبيق
if __name__ == '__main__':
    print("بدء تشغيل تطبيق منصرف الأدوية...")
    app.run(debug=True, port=8080)
