{% extends "base.html" %}

{% block title %}إدارة تصنيفات الأدوية - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 20px 20px 0 0 !important;
        border: none !important;
        padding: 20px 30px;
    }

    .section-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        overflow: hidden;
        height: 100%;
    }

    .section-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .section-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 15px 20px;
        margin: -1px -1px 20px -1px;
        border-radius: 15px 15px 0 0;
    }

    .links-section .section-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .categories-list-section .section-header {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 12px 15px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .btn-danger {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
    }

    .btn-warning {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
        color: white;
        font-weight: 600;
    }

    .btn-warning:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .table {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
        text-align: center;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .table tbody td {
        padding: 15px;
        text-align: center;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .list-group-item {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 10px !important;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .list-group-item:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: translateX(10px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .alert-info {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border: none;
        border-radius: 15px;
        color: #333;
        padding: 20px;
        text-align: center;
    }

    .icon-input {
        position: relative;
    }

    .icon-input i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        z-index: 10;
    }

    .icon-input .form-control {
        padding-left: 45px;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .section-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .section-card:nth-child(2) {
        animation-delay: 0.2s;
    }

    .section-card:nth-child(3) {
        animation-delay: 0.4s;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="card main-card">
        <div class="card-header">
            <h2 class="text-white mb-0">
                <i class="mdi mdi-tag-multiple me-2"></i>
                إدارة تصنيفات الأدوية
            </h2>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- نموذج إضافة/تعديل التصنيف -->
                <div class="col-md-4 mb-4">
                    <div class="section-card">
                        <div class="section-header">
                            <h4 class="mb-0">
                                <i class="mdi mdi-plus-circle me-2"></i>
                                إضافة تصنيف جديد
                            </h4>
                        </div>
                        <div class="p-4">
                            <form method="POST" action="{{ url_for('manage_drug_categories_new') }}">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم التصنيف</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="mdi mdi-plus-circle me-2"></i>
                                    إضافة التصنيف
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- قائمة التصنيفات -->
                <div class="col-md-8">
                    <div class="section-card">
                        <div class="section-header">
                            <h4 class="mb-0">
                                <i class="mdi mdi-format-list-bulleted me-2"></i>
                                قائمة التصنيفات
                            </h4>
                        </div>
                        <div class="p-4">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>اسم التصنيف</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for category in categories %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ category.name }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-primary" onclick="editCategory(this)" 
                                                        data-id="{{ category.id }}" data-name="{{ category.name }}">
                                                    <i class="mdi mdi-pencil"></i>
                                                    تعديل
                                                </button>
                                                <form method="POST" action="{{ url_for('delete_drug_category', category_id=category.id) }}" 
                                                      style="display: inline;"
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا التصنيف؟')">
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="mdi mdi-delete"></i>
                                                        حذف
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    var isEditing = false;
    var editingCategoryId = null;

    // دالة تعديل التصنيف
    function editCategory(button) {
        var id = $(button).data('id');
        var name = $(button).data('name');

        // ملء النموذج ببيانات التصنيف
        $('#name').val(name);

        // تغيير حالة النموذج للتعديل
        isEditing = true;
        editingCategoryId = id;

        // تغيير نص الزر في نموذج الإضافة/التعديل فقط
        $('.col-md-4 form button[type="submit"]').html('<i class="mdi mdi-check me-2"></i>تحديث التصنيف');

        // إضافة زر إلغاء
        if ($('#cancel-edit-btn').length === 0) {
            $('.col-md-4 form button[type="submit"]').after('<button type="button" id="cancel-edit-btn" class="btn btn-secondary ms-2" onclick="cancelEdit()"><i class="mdi mdi-close me-2"></i>إلغاء</button>');
        }

        // تغيير action النموذج للنموذج الرئيسي (نموذج الإضافة/التحديث)
        $('.col-md-4 form').attr('action', '{{ url_for("update_drug_category") }}');
        
        // إضافة حقل مخفي لمعرف التصنيف
        if ($('#category_id').length === 0) {
            $('.col-md-4 form').append('<input type="hidden" id="category_id" name="category_id">');
        }
        $('#category_id').val(id);

        // التمرير للنموذج
        $('html, body').animate({
            scrollTop: $('form').offset().top - 100
        }, 500);
    }

    // دالة إلغاء التعديل
    function cancelEdit() {
        // إعادة تعيين النموذج
        $('form')[0].reset();

        // إعادة تعيين حالة التعديل
        isEditing = false;
        editingCategoryId = null;

        // إعادة تعيين نص الزر في نموذج الإضافة/التعديل فقط
        $('.col-md-4 form button[type="submit"]').html('<i class="mdi mdi-plus-circle me-2"></i>إضافة التصنيف');

        // إزالة زر الإلغاء
        $('#cancel-edit-btn').remove();

        // إعادة تعيين action النموذج
        $('.col-md-4 form').attr('action', '{{ url_for("manage_drug_categories_new") }}');
    }
</script>
{% endblock %}