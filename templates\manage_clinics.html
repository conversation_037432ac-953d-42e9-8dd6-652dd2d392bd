{% extends "base.html" %}

{% block title %}إدارة العيادات - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    :root {
        --primary-color: #4e73df;
        --secondary-color: #858796;
        --success-color: #1cc88a;
        --info-color: #36b9cc;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --light-color: #f8f9fc;
        --dark-color: #5a5c69;
    }

    .clinic-card {
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
    }

    .clinic-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .clinic-card .card-header {
        padding: 1.25rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .btn-add {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .table-custom {
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .table-custom th {
        background-color: #f8f9fc;
        font-weight: 600;
        border-top: none;
    }

    .table-custom td {
        vertical-align: middle;
    }

    .form-control:focus, .form-select:focus {
        border-color: #bac8f3;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }

    .input-group-text {
        background-color: #f8f9fc;
    }

    .list-group-item {
        border-left: none;
        border-right: none;
        padding: 1rem 1.25rem;
        transition: all 0.2s;
    }

    .list-group-item:first-child {
        border-top: none;
    }

    .list-group-item:last-child {
        border-bottom: none;
    }

    .list-group-item:hover {
        background-color: #f8f9fc;
        transform: translateX(-5px);
    }

    .list-group-item i {
        transition: all 0.2s;
    }

    .list-group-item:hover i {
        transform: scale(1.2);
    }

    .modal-content {
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
    }

    .modal-header {
        background: linear-gradient(45deg, #4e73df, #224abe);
        border-bottom: none;
    }

    .modal-footer {
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .btn-action {
        width: 32px;
        height: 32px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
    }

    .btn-action:hover {
        transform: translateY(-3px);
    }

    .empty-state {
        text-align: center;
        padding: 2rem;
    }

    .empty-state i {
        font-size: 3rem;
        color: var(--secondary-color);
        margin-bottom: 1rem;
    }

    .area-badge {
        background-color: #e8f0fe;
        color: #4285f4;
        border-radius: 50px;
        padding: 0.25rem 0.75rem;
        font-size: 0.85rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
    }

    .area-badge i {
        margin-right: 0.25rem;
        font-size: 1rem;
    }

    .branch-badge {
        background-color: #fce8e6;
        color: #ea4335;
        border-radius: 50px;
        padding: 0.25rem 0.75rem;
        font-size: 0.85rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
    }

    .branch-badge i {
        margin-right: 0.25rem;
        font-size: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Modal تعديل العيادة -->
<div class="modal fade" id="editClinicModal" tabindex="-1" aria-labelledby="editClinicModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header text-white">
                <h5 class="modal-title" id="editClinicModalLabel"><i class="mdi mdi-pencil-box-outline me-2"></i>تعديل العيادة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <form id="editClinicForm" method="POST" action="/manage/clinics/update">
                    <input type="hidden" id="edit_clinic_id" name="clinic_id">
                    <div class="mb-4">
                        <label for="edit_area_id" class="form-label">المنطقة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-map"></i></span>
                            <select class="form-select" id="edit_area_id" name="area_id" required>
                                <option value="">-- اختر المنطقة --</option>
                                {% for area in areas %}
                                <option value="{{ area.id }}">{{ area.name }} ({{ area.branch_name }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-text text-muted">
                            <i class="mdi mdi-information-outline me-1"></i>اختر المنطقة التي تنتمي إليها العيادة
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="edit_name" class="form-label">اسم العيادة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-hospital-building"></i></span>
                            <input type="text" class="form-control" id="edit_name" name="name" required placeholder="أدخل اسم العيادة">
                        </div>
                        <div class="form-text text-muted">
                            <i class="mdi mdi-information-outline me-1"></i>يرجى إدخال اسم فريد للعيادة
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="mdi mdi-close me-1"></i>إلغاء
                </button>
                <button type="button" class="btn btn-primary" id="saveEditClinic">
                    <i class="mdi mdi-content-save me-1"></i>حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="mdi mdi-hospital-building me-2"></i>إدارة العيادات
                </h4>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="POST" action="/manage/clinics">
                            <div class="card clinic-card shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="mdi mdi-plus-box me-2"></i>إضافة عيادة جديدة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <label for="area_id" class="form-label">المنطقة</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="mdi mdi-map"></i></span>
                                            <select class="form-select" id="area_id" name="area_id" required>
                                                <option value="">-- اختر المنطقة --</option>
                                                {% for area in areas %}
                                                <option value="{{ area.id }}">{{ area.name }} ({{ area.branch_name }})</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-text text-muted">
                                            <i class="mdi mdi-information-outline me-1"></i>اختر المنطقة التي تنتمي إليها العيادة
                                        </div>
                                    </div>
                                    <div class="mb-4">
                                        <label for="name" class="form-label">اسم العيادة</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="mdi mdi-hospital-building"></i></span>
                                            <input type="text" class="form-control" id="name" name="name" required placeholder="أدخل اسم العيادة">
                                        </div>
                                        <div class="form-text text-muted">
                                            <i class="mdi mdi-information-outline me-1"></i>يرجى إدخال اسم فريد للعيادة
                                        </div>
                                    </div>
                                    <div class="text-center mt-4">
                                        <button type="submit" class="btn btn-primary btn-add">
                                            <i class="mdi mdi-content-save me-1"></i>حفظ العيادة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <div class="card clinic-card shadow-sm h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="mdi mdi-link-variant me-2"></i>روابط سريعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="list-group">
                                    <a href="/manage/branches" class="list-group-item list-group-item-action">
                                        <i class="mdi mdi-office-building me-2"></i>إدارة الفروع
                                    </a>
                                    <a href="/manage/areas" class="list-group-item list-group-item-action">
                                        <i class="mdi mdi-map me-2"></i>إدارة المناطق
                                    </a>
                                    <a href="/" class="list-group-item list-group-item-action">
                                        <i class="mdi mdi-home me-2"></i>العودة للصفحة الرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card clinic-card shadow-sm mt-4">
                    <div class="card-header bg-light">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0"><i class="mdi mdi-format-list-bulleted me-2"></i>قائمة العيادات</h5>
                            </div>
                            <div class="col-auto">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                                    <input type="text" id="searchClinic" class="form-control" placeholder="بحث...">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if clinics %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-custom" id="clinicsTable">
                                <thead>
                                    <tr class="text-center">
                                        <th>#</th>
                                        <th>اسم العيادة</th>
                                        <th>المنطقة</th>
                                        <th>الفرع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for clinic in clinics %}
                                    <tr>
                                        <td class="text-center">{{ loop.index }}</td>
                                        <td>{{ clinic.name }}</td>
                                        <td><span class="area-badge"><i class="mdi mdi-map"></i>{{ clinic.area_name }}</span></td>
                                        <td><span class="branch-badge"><i class="mdi mdi-office-building"></i>{{ clinic.branch_name }}</span></td>
                                        <td class="text-center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-info rounded-circle me-2 btn-action edit-clinic"
                                                        data-id="{{ clinic.id }}"
                                                        data-name="{{ clinic.name }}"
                                                        data-area="{{ clinic.area_id }}"
                                                        title="تعديل العيادة">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>
                                                <form method="POST" action="/manage/clinics/{{ clinic.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه العيادة؟\nسيتم حذف جميع سجلات الصرف المرتبطة بها.');">
                                                    <button type="submit" class="btn btn-sm btn-danger rounded-circle btn-action" title="حذف العيادة">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="empty-state">
                            <i class="mdi mdi-hospital-building-outline"></i>
                            <h5>لا توجد عيادات مسجلة حالياً</h5>
                            <p class="text-muted">قم بإضافة عيادة جديدة باستخدام النموذج أعلاه</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // البحث في جدول العيادات
        $("#searchClinic").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            if (value.length > 0) {
                $("#clinicsTable").addClass("table-hover");
            } else {
                $("#clinicsTable").removeClass("table-hover");
            }

            var hasResults = false;
            $("#clinicsTable tbody tr").filter(function() {
                var matches = $(this).text().toLowerCase().indexOf(value) > -1;
                $(this).toggle(matches);
                if (matches) hasResults = true;
            });

            // إظهار رسالة عدم وجود نتائج
            if (!hasResults && value.length > 0) {
                if ($("#no-results-row").length === 0) {
                    $("#clinicsTable tbody").append(
                        '<tr id="no-results-row"><td colspan="5" class="text-center py-3">' +
                        '<i class="mdi mdi-alert-circle-outline text-warning" style="font-size: 1.5rem;"></i><br>' +
                        'لا توجد نتائج تطابق "<span class="fw-bold">' + value + '</span>"</td></tr>'
                    );
                } else {
                    $("#no-results-row td").html(
                        '<i class="mdi mdi-alert-circle-outline text-warning" style="font-size: 1.5rem;"></i><br>' +
                        'لا توجد نتائج تطابق "<span class="fw-bold">' + value + '</span>"'
                    );
                }
            } else {
                $("#no-results-row").remove();
            }
        });

        // فتح نافذة التعديل عند النقر على زر التعديل
        $(".edit-clinic").on("click", function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var area = $(this).data('area');

            // ملء النموذج بالبيانات
            $("#edit_clinic_id").val(id);
            $("#edit_name").val(name);
            $("#edit_area_id").val(area);

            // إضافة تأثير بصري
            $(this).addClass("animate__animated animate__pulse");

            // فتح النافذة المنبثقة
            $("#editClinicModal").modal('show');

            // التركيز على حقل الاسم
            setTimeout(function() {
                $("#edit_name").focus().select();
            }, 500);
        });

        // حفظ التغييرات عند النقر على زر الحفظ
        $("#saveEditClinic").on("click", function() {
            // التحقق من صحة النموذج
            var isValid = true;

            if (!$("#edit_name").val()) {
                $("#edit_name").addClass("is-invalid");
                if ($("#edit_name-error").length === 0) {
                    $("#edit_name").after('<div id="edit_name-error" class="invalid-feedback">يرجى إدخال اسم العيادة</div>');
                }
                isValid = false;
            }

            if (!$("#edit_area_id").val()) {
                $("#edit_area_id").addClass("is-invalid");
                if ($("#edit_area_id-error").length === 0) {
                    $("#edit_area_id").after('<div id="edit_area_id-error" class="invalid-feedback">يرجى اختيار المنطقة</div>');
                }
                isValid = false;
            }

            if (isValid) {
                // إضافة تأثير بصري
                $(this).html('<i class="mdi mdi-loading mdi-spin me-1"></i>جاري الحفظ...');
                $(this).attr('disabled', true);

                // إرسال النموذج
                $("#editClinicForm").submit();
            } else {
                // التركيز على أول حقل غير صالح
                if (!$("#edit_name").val()) {
                    $("#edit_name").focus();
                } else {
                    $("#edit_area_id").focus();
                }
            }
        });

        // إزالة رسالة الخطأ عند الكتابة
        $("#edit_name, #edit_area_id").on("input change", function() {
            $(this).removeClass("is-invalid");
        });

        // تأثيرات بصرية للأزرار
        $(".btn-action").hover(
            function() {
                $(this).addClass("shadow-sm");
                // تكبير الأيقونة
                $(this).find("i").css("transform", "scale(1.2)");
            },
            function() {
                $(this).removeClass("shadow-sm");
                // إعادة الأيقونة لحجمها الطبيعي
                $(this).find("i").css("transform", "scale(1)");
            }
        );

        // تأثيرات بصرية للنموذج
        $(".form-control, .form-select").on("focus", function() {
            $(this).closest(".mb-4").addClass("was-validated");
            // تكبير الأيقونة
            $(this).prev(".input-group-text").find("i").css("transform", "scale(1.2)");
        }).on("blur", function() {
            // إعادة الأيقونة لحجمها الطبيعي
            $(this).prev(".input-group-text").find("i").css("transform", "scale(1)");
        });

        // تأثيرات بصرية للبطاقات
        $(".clinic-card").hover(
            function() {
                $(this).find(".card-header h5 i").css("transform", "scale(1.2)");
            },
            function() {
                $(this).find(".card-header h5 i").css("transform", "scale(1)");
            }
        );

        // تأثير عند تحميل الصفحة
        $(".clinic-card").addClass("animate__animated animate__fadeIn");

        // تأثير للشارات
        $(".area-badge, .branch-badge").hover(
            function() {
                $(this).css("transform", "scale(1.05)");
            },
            function() {
                $(this).css("transform", "scale(1)");
            }
        );
    });
</script>
{% endblock %}