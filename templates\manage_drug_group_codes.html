{% extends "base.html" %}

{% block title %}إضافة مجموعة دوائية جديدة - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    .group-card {
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
    }

    .group-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .group-card .card-header {
        padding: 1.25rem;
    }

    .btn-add {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .table-custom th {
        background-color: #f8f9fc;
        font-weight: 600;
    }

    .form-control, .form-select {
        border-radius: 0.5rem;
        padding: 0.6rem 1rem;
    }

    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- أزرار التنقل السريع -->
<div class="container py-2">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="text-white mb-0">
                            <i class="mdi mdi-barcode me-2"></i>إدارة أكواد المجموعات الدوائية
                        </h5>
                        <div class="d-flex gap-2">
                            <a href="/manage/drugs" class="btn btn-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-pill me-1"></i>إدارة الأدوية
                            </a>
                            <a href="/manage/drug_categories" class="btn btn-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-tag-multiple me-1"></i>تصنيفات الأدوية
                            </a>
                            <a href="/manage/drug_groups" class="btn btn-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-package-variant me-1"></i>المجموعات الدوائية
                            </a>
                            <a href="/reports/drug_groups" class="btn btn-outline-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-chart-line me-1"></i>تقرير المجموعات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل المجموعة الدوائية -->
<div class="modal fade" id="editGroupCodeModal" tabindex="-1" aria-labelledby="editGroupCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editGroupCodeModalLabel">تعديل المجموعة الدوائية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editGroupCodeForm" method="POST" action="/manage/drug_group_codes/update">
                    <input type="hidden" id="edit_code_id" name="code_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_code_value" class="form-label">كود المجموعة</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="mdi mdi-barcode"></i></span>
                                    <input type="text" class="form-control" id="edit_code_value" name="code" readonly>
                                </div>
                                <small class="text-muted">لا يمكن تعديل الكود</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_code_name" class="form-label">اسم المجموعة</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="mdi mdi-package-variant"></i></span>
                                    <input type="text" class="form-control" id="edit_code_name" name="name" required placeholder="أدخل اسم المجموعة">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_code_description" class="form-label">وصف المجموعة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="mdi mdi-text-box"></i></span>
                            <textarea class="form-control" id="edit_code_description" name="description" rows="3" placeholder="أدخل وصفاً مختصراً للمجموعة (اختياري)"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveEditGroupCode">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="mdi mdi-package-variant me-2"></i>إضافة مجموعة دوائية جديدة
                    </h4>
                    <a href="/manage/drug_groups" class="btn btn-light btn-add">
                        <i class="mdi mdi-currency-usd me-1"></i>تسجيل تكلفة مجموعة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-8 mx-auto">
                        <form id="addGroupCodeForm" method="POST" action="/manage/drug_group_codes">
                            <div class="card group-card shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="mdi mdi-plus-box me-2"></i>بيانات المجموعة الدوائية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="code" class="form-label">كود المجموعة</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="mdi mdi-barcode"></i></span>
                                                    <input type="text" class="form-control" id="code" name="code" value="{{ next_code }}" readonly>
                                                </div>
                                                <small class="text-muted">سيتم توليد الكود تلقائياً ({{ next_code }}) عند الإضافة</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">اسم المجموعة</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="mdi mdi-package-variant"></i></span>
                                                    <input type="text" class="form-control" id="name" name="name" required placeholder="أدخل اسم المجموعة">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="description" class="form-label">وصف المجموعة</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="mdi mdi-text-box"></i></span>
                                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="أدخل وصفاً مختصراً للمجموعة (اختياري)"></textarea>
                                        </div>
                                    </div>
                                    <div class="text-center mt-4">
                                        <button type="submit" class="btn btn-primary btn-add">
                                            <i class="mdi mdi-content-save me-1"></i>حفظ المجموعة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card group-card shadow-sm mt-4">
                    <div class="card-header bg-light">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0"><i class="mdi mdi-format-list-bulleted me-2"></i>قائمة المجموعات الدوائية</h5>
                            </div>
                            <div class="col-auto">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                                    <input type="text" id="searchGroupCode" class="form-control" placeholder="بحث...">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if codes %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-custom" id="groupCodesTable">
                                <thead>
                                    <tr class="text-center">
                                        <th>#</th>
                                        <th>كود المجموعة</th>
                                        <th>اسم المجموعة</th>
                                        <th>وصف المجموعة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for code in codes %}
                                    <tr>
                                        <td class="text-center">{{ loop.index }}</td>
                                        <td class="text-center fw-bold">{{ code.code }}</td>
                                        <td>{{ code.name }}</td>
                                        <td>{{ code.description or '-' }}</td>
                                        <td class="text-center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-info rounded-circle me-1 edit-group-code"
                                                        data-id="{{ code.id }}"
                                                        data-code="{{ code.code }}"
                                                        data-name="{{ code.name }}"
                                                        data-description="{{ code.description or '' }}">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>
                                                <form method="POST" action="/manage/drug_group_codes/{{ code.id }}/delete" class="d-inline delete-form" onsubmit="return confirm('هل أنت متأكد من حذف هذه المجموعة؟');">
                                                    <button type="submit" class="btn btn-sm btn-danger rounded-circle">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="mdi mdi-information-outline me-2"></i>لا توجد مجموعات دوائية مسجلة حالياً.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // البحث في جدول تكويد المجموعات الدوائية
        $("#searchGroupCode").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $("#groupCodesTable tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });

        // التحقق من وجود اسم قبل الإرسال (فقط لنموذج الإضافة)
        $("#addGroupCodeForm").on("submit", function(e) {
            if (!$("#name").val()) {
                e.preventDefault();
                alert("يرجى إدخال اسم المجموعة");
                $("#name").focus();
                return false;
            }
            console.log("نموذج الإضافة - البيانات صحيحة");
            return true;
        });

        // معالج خاص لنماذج الحذف - السماح بالمرور دون تحقق
        $(document).on("submit", ".delete-form", function(e) {
            console.log("نموذج حذف - السماح بالمرور");
            // لا نمنع الإرسال أبداً
            return true;
        });

        // فتح نافذة التعديل عند النقر على زر التعديل (استخدام event delegation)
        $(document).on("click", ".edit-group-code", function() {
            var id = $(this).data('id');
            var code = $(this).data('code');
            var name = $(this).data('name');
            var description = $(this).data('description');

            console.log("فتح نافذة التعديل للكود:", id, "الاسم:", name);

            // ملء النموذج بالبيانات
            $("#edit_code_id").val(id);
            $("#edit_code_value").val(code);
            $("#edit_code_name").val(name);
            $("#edit_code_description").val(description);

            // فتح النافذة المنبثقة
            $("#editGroupCodeModal").modal('show');
        });

        // حفظ التغييرات عند النقر على زر الحفظ
        $("#saveEditGroupCode").on("click", function() {
            // التحقق من صحة النموذج
            if ($("#edit_code_name").val()) {
                console.log("إرسال نموذج التعديل...");

                // إرسال البيانات باستخدام AJAX
                var formData = {
                    code_id: $("#edit_code_id").val(),
                    name: $("#edit_code_name").val(),
                    description: $("#edit_code_description").val()
                };

                console.log("البيانات المرسلة:", formData);

                $.post("/manage/drug_group_codes/update", formData)
                    .done(function(response) {
                        console.log("تم الحفظ بنجاح");
                        // إخفاء النافذة المنبثقة
                        $("#editGroupCodeModal").modal('hide');
                        // إعادة تحميل الصفحة لإظهار التغييرات
                        location.reload();
                    })
                    .fail(function() {
                        console.log("فشل في الحفظ");
                        alert("حدث خطأ في حفظ التغييرات");
                    });
            } else {
                alert("يرجى إدخال اسم المجموعة");
                $("#edit_code_name").focus();
            }
        });

        // تأثيرات بصرية للأزرار
        $(".btn-group .btn").hover(
            function() {
                $(this).addClass("shadow-sm");
            },
            function() {
                $(this).removeClass("shadow-sm");
            }
        );

        // تأثيرات بصرية للنموذج
        $(".form-control").on("focus", function() {
            $(this).closest(".mb-3").addClass("was-validated");
        });

        // إخفاء النافذة المنبثقة عند إعادة تحميل الصفحة (بعد نجاح العملية)
        {% if get_flashed_messages() %}
        $("#editGroupCodeModal").modal('hide');
        {% endif %}
    });
</script>
{% endblock %}
