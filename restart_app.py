#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import importlib

print("🔄 إعادة تشغيل تطبيق منصرف الأدوية...")
print("=" * 50)

try:
    # إعادة تحميل الوحدة إذا كانت محملة مسبقاً
    if 'app' in sys.modules:
        print("🔄 إعادة تحميل الوحدة...")
        importlib.reload(sys.modules['app'])
    
    print("📦 تحميل التطبيق...")
    from app import app
    print("✅ تم تحميل التطبيق بنجاح")
    
    # التحقق من وجود المسار
    print("🔍 التحقق من المسارات...")
    routes = [rule.rule for rule in app.url_map.iter_rules()]
    if '/manage/drug_categories/update' in routes:
        print("✅ مسار update_drug_category موجود")
    else:
        print("❌ مسار update_drug_category غير موجود")
        print("المسارات الموجودة:")
        for route in routes:
            if 'drug_categories' in route:
                print(f"  - {route}")
    
    print("🌐 بدء تشغيل الخادم...")
    print("🔗 رابط التطبيق: http://localhost:8080")
    print("=" * 50)
    print("📝 ملاحظة: اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    # تشغيل التطبيق
    app.run(
        host='0.0.0.0',
        port=8080,
        debug=True,
        use_reloader=False
    )
    
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("💡 تأكد من تثبيت Flask: pip install flask")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    print("💡 تحقق من ملف app.py")
    
finally:
    print("\n👋 تم إيقاف التطبيق")
