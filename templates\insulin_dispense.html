{% extends "base.html" %}

{% block title %}منصرف الأنسولين - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 20px 20px 0 0 !important;
        border: none !important;
        padding: 20px 30px;
    }

    .section-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        overflow: hidden;
        height: 100%;
    }

    .section-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .section-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 15px 20px;
        margin: -1px -1px 20px -1px;
        border-radius: 15px 15px 0 0;
    }

    .insulin-section .section-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .list-section .section-header {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .saved-section .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 12px 15px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .btn-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(17, 153, 142, 0.3);
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
        background: linear-gradient(135deg, #0d8377 0%, #2dd36f 100%);
    }

    .btn-danger {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
    }

    .btn-warning {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
        color: white;
        font-weight: 600;
    }

    .btn-warning:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .table {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
        text-align: center;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .table tbody td {
        padding: 15px;
        text-align: center;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .alert-warning {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        border: none;
        border-radius: 15px;
        color: #333;
    }

    .alert-info {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border: none;
        border-radius: 15px;
        color: #333;
    }

    .badge {
        border-radius: 20px;
        padding: 8px 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .section-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .section-card:nth-child(2) {
        animation-delay: 0.2s;
    }

    .section-card:nth-child(3) {
        animation-delay: 0.4s;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="main-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0 fw-bold text-white">
                            <i class="mdi mdi-needle me-3"></i>منصرف الأنسولين
                        </h3>
                        <div>
                            <a href="/manage/insulin_codes" class="btn btn-light me-2">
                                <i class="mdi mdi-barcode me-1"></i>إدارة تكويد الأنسولين
                            </a>
                            <a href="/" class="btn btn-light">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form id="insulin-dispense-form" method="POST" action="/insulin/dispense">
                        <div class="row">
                            <!-- بيانات المنصرف -->
                            <div class="col-lg-4">
                                <div class="section-card mb-4">
                                    <div class="section-header">
                                        <h6 class="mb-0 fw-bold"><i class="mdi mdi-information me-2"></i>بيانات المنصرف</h6>
                                    </div>
                                    <div class="p-3">
                                        <div class="mb-3">
                                            <input type="month" class="form-control" id="dispense_month" name="dispense_month" required>
                                        </div>
                                        <div class="mb-3">
                                            <select class="form-select" id="branch_id" name="branch_id" required>
                                                <option value="">-- اختر الفرع --</option>
                                                {% for branch in branches %}
                                                <option value="{{ branch.id }}">{{ branch.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <select class="form-select" id="area_id" name="area_id" required disabled>
                                                <option value="">-- اختر المنطقة --</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <select class="form-select" id="clinic_id" name="clinic_id" required disabled>
                                                <option value="">-- اختر العيادة --</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <select class="form-select" id="category" name="category" required>
                                                <option value="">-- اختر الفئة --</option>
                                                {% for category in insulin_categories %}
                                                <option value="{{ category.name }}">{{ category.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <select class="form-select" id="type" name="type" required disabled>
                                                <option value="">-- اختر الفئة أولاً --</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- بيانات الأنسولين -->
                            <div class="col-lg-8">
                                <div class="section-card insulin-section mb-4">
                                    <div class="section-header">
                                        <h6 class="mb-0 fw-bold"><i class="mdi mdi-needle me-2"></i>بيانات الأنسولين</h6>
                                    </div>
                                    <div class="p-3">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <select class="form-select" id="insulin_code_id" name="insulin_code_id" disabled>
                                                        <option value="">-- اختر النوع أولاً --</option>
                                                    </select>
                                                    <label for="insulin_code_id">تكويد الأنسولين</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="name" name="name" required readonly>
                                                    <label for="name">اسم الأنسولين</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="unit" name="unit" required readonly>
                                                    <label for="unit">الوحدة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="display_category" readonly>
                                                    <label for="display_category">الفئة المختارة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-floating">
                                                    <input type="number" min="1" class="form-control" id="quantity" name="quantity" required>
                                                    <label for="quantity">الكمية</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-floating">
                                                    <input type="number" min="1" class="form-control" id="cases_count" name="cases_count" required>
                                                    <label for="cases_count">عدد الحالات</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-floating">
                                                    <input type="number" step="0.01" min="0.01" class="form-control" id="price" name="price" required>
                                                    <label for="price">سعر الوحدة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-floating">
                                                    <input type="number" step="0.01" min="0" class="form-control" id="balance" name="balance" required>
                                                    <label for="balance">الرصيد</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="number" step="0.01" min="0" class="form-control" id="cost" name="cost" readonly>
                                                    <label for="cost">التكلفة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="number" step="0.01" min="0" class="form-control" id="rate" name="rate" readonly>
                                                    <label for="rate">المعدل</label>
                                                </div>
                                            </div>
                                            <div class="col-12 text-center mt-3">
                                                <button type="button" id="add-insulin-btn" class="btn btn-primary btn-lg">
                                                    <i class="mdi mdi-plus-circle me-1"></i>إضافة للقائمة
                                                </button>
                                                <button type="button" id="save-insulin-btn" class="btn btn-success btn-lg ms-2">
                                                    <i class="mdi mdi-content-save me-1"></i>حفظ المنصرف
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قائمة البيانات المؤقتة (الأصناف المضافة للقائمة) -->
                        <div id="items_list_container" class="mt-4" style="display: none;">
                            <div class="card shadow-sm">
                                <div class="card-header bg-info text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="mdi mdi-format-list-bulleted me-1"></i>قائمة الأصناف المضافة للمنصرف
                                        </h5>
                                        <span class="badge bg-light text-info">
                                            <i class="mdi mdi-information-outline me-1"></i><span id="items_count_info">0</span> صنف
                                        </span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <i class="mdi mdi-alert-circle-outline me-2"></i>
                                        <strong>ملاحظة:</strong> هذه قائمة مؤقتة للأصناف التي أضفتها. اضغط على زر "حفظ المنصرف" لحفظها في قاعدة البيانات.
                                    </div>
                                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                        <table class="table table-bordered table-hover" id="items_table">
                                            <thead class="table-info sticky-top">
                                                <tr class="text-center">
                                                    <th>الصنف</th>
                                                    <th>الوحدة</th>
                                                    <th>النوع</th>
                                                    <th>الكمية</th>
                                                    <th>عدد الحالات</th>
                                                    <th>سعر الوحدة</th>
                                                    <th>التكلفة</th>
                                                    <th>المعدل</th>
                                                    <th>الرصيد</th>
                                                    <th>الفئة</th>
                                                    <th>حذف</th>
                                                </tr>
                                            </thead>
                                            <tbody id="items_list">
                                                <!-- ستتم إضافة العناصر هنا بواسطة JavaScript -->
                                            </tbody>
                                            <tfoot class="table-info">
                                                <tr>
                                                    <th colspan="6" class="text-end">الإجمالي:</th>
                                                    <th class="text-center fw-bold" id="total_cost_display">0.00</th>
                                                    <th colspan="4"></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <input type="hidden" id="items_data" name="items_data">
                                    <input type="hidden" id="total_cost" name="total_cost" value="0">
                                </div>
                            </div>
                        </div>

                        <!-- تم نقل زر حفظ المنصرف بجوار زر إضافة للقائمة -->

                        <!-- قائمة المنصرف المحفوظ -->
                        <div class="mt-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-primary text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="mdi mdi-database me-1"></i>قائمة منصرف الأنسولين المحفوظ
                                        </h5>
                                        <span class="badge bg-light text-primary">
                                            <i class="mdi mdi-information-outline me-1"></i>{{ saved_items|length if saved_items else 0 }} صنف
                                        </span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    {% if saved_items %}
                                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                        <table class="table table-bordered table-hover" id="saved_items_table">
                                            <thead class="table-primary sticky-top">
                                                <tr class="text-center">
                                                    <th>#</th>
                                                    <th>الصنف</th>
                                                    <th>النوع</th>
                                                    <th>الوحدة</th>
                                                    <th>الكمية</th>
                                                    <th>عدد الحالات</th>
                                                    <th>السعر</th>
                                                    <th>التكلفة</th>
                                                    <th>المعدل</th>
                                                    <th>الرصيد</th>
                                                    <th>الفئة</th>
                                                    <th>العيادة</th>
                                                    <th>شهر الصرف</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for item in saved_items %}
                                                <tr>
                                                    <td class="text-center">{{ loop.index }}</td>
                                                    <td>{{ item.name }}</td>
                                                    <td class="text-center">{{ item.type }}</td>
                                                    <td class="text-center">{{ item.unit }}</td>
                                                    <td class="text-center">{{ item.quantity }}</td>
                                                    <td class="text-center">{{ item.cases_count }}</td>
                                                    <td class="text-center">{{ item.price }}</td>
                                                    <td class="text-center fw-bold">{{ (item.quantity * item.price)|round(2) }}</td>
                                                    <td class="text-center">{{ item.rate|default(0)|round(2) }}</td>
                                                    <td class="text-center">{{ item.balance|default(0) }}</td>
                                                    <td class="text-center">{{ item.category|default('') }}</td>
                                                    <td>{{ item.clinic_name }}</td>
                                                    <td class="text-center">{{ item.dispense_month }}</td>
                                                    <td class="text-center">
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-primary edit-insulin-item"
                                                                    data-id="{{ item.id }}"
                                                                    data-name="{{ item.name }}"
                                                                    data-type="{{ item.type }}"
                                                                    data-unit="{{ item.unit }}"
                                                                    data-quantity="{{ item.quantity }}"
                                                                    data-cases-count="{{ item.cases_count }}"
                                                                    data-price="{{ item.price }}"
                                                                    data-cost="{{ item.cost }}"
                                                                    data-rate="{{ item.rate }}"
                                                                    data-balance="{{ item.balance }}"
                                                                    data-category="{{ item.category }}"
                                                                    data-clinic-id="{{ item.clinic_id }}"
                                                                    data-dispense-month="{{ item.dispense_month[:7] if item.dispense_month else '' }}"
                                                                    data-insulin-code-id="{{ item.insulin_code_id }}">
                                                                <i class="mdi mdi-pencil"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-danger delete-insulin-item" data-id="{{ item.id }}">
                                                                <i class="mdi mdi-delete"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-info">
                                        <i class="mdi mdi-information-outline me-2"></i>لا توجد أصناف أنسولين محفوظة حالياً.
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- تم نقل الأزرار إلى أعلى الصفحة -->
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
    <!-- نافذة تعديل الأنسولين -->
    <div class="modal fade" id="editInsulinModal" tabindex="-1" aria-labelledby="editInsulinModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editInsulinModalLabel">تعديل بيانات الأنسولين</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-insulin-form" method="POST" action="/insulin/update">
                        <input type="hidden" id="edit_insulin_id" name="insulin_id">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="edit_insulin_code_id" name="insulin_code_id">
                                        <option value="">-- اختر تكويد الأنسولين --</option>
                                        {% for code in insulin_codes %}
                                        <option value="{{ code.id }}" data-name="{{ code.name }}">{{ code.code }} - {{ code.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <label for="edit_insulin_code_id">تكويد الأنسولين</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="edit_name" name="name" required>
                                    <label for="edit_name">اسم الأنسولين</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <select class="form-select" id="edit_type" name="type" required>
                                        <option value="">-- اختر النوع --</option>
                                        {% for type in insulin_types %}
                                        <option value="{{ type.name }}">{{ type.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <label for="edit_type">النوع</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <select class="form-select" id="edit_unit" name="unit" required>
                                        <option value="">-- اختر الوحدة --</option>
                                        <option value="فيال">فيال</option>
                                        <option value="قلم">قلم</option>
                                        <option value="خرطوشة">خرطوشة</option>
                                        <option value="علبة">علبة</option>
                                    </select>
                                    <label for="edit_unit">الوحدة</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <select class="form-select" id="edit_category" name="category" required>
                                        <option value="">-- اختر الفئة --</option>
                                        {% for category in insulin_categories %}
                                        <option value="{{ category.name }}">{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <label for="edit_category">الفئة</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="number" min="1" class="form-control" id="edit_cases_count" name="cases_count" required>
                                    <label for="edit_cases_count">عدد الحالات</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="number" min="0.01" step="0.01" class="form-control" id="edit_quantity" name="quantity" required>
                                    <label for="edit_quantity">الكمية</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="number" min="0.01" step="0.01" class="form-control" id="edit_price" name="price" required>
                                    <label for="edit_price">السعر</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="number" min="0" step="0.01" class="form-control" id="edit_balance" name="balance" required>
                                    <label for="edit_balance">الرصيد</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <input type="number" min="0" step="0.01" class="form-control" id="edit_cost" name="cost">
                                    <label for="edit_cost">التكلفة</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <input type="number" min="0" step="0.01" class="form-control" id="edit_rate" name="rate">
                                    <label for="edit_rate">المعدل</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <select class="form-select" id="edit_clinic_id" name="clinic_id" required>
                                        <option value="">-- اختر العيادة --</option>
                                        {% for clinic in clinics %}
                                        <option value="{{ clinic.id }}">{{ clinic.name }} - {{ clinic.area_name }} - {{ clinic.branch_name }}</option>
                                        {% endfor %}
                                    </select>
                                    <label for="edit_clinic_id">العيادة</label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-floating">
                                    <input type="month" class="form-control" id="edit_dispense_month" name="dispense_month" required>
                                    <label for="edit_dispense_month">شهر الصرف</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="save-edit-insulin">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal fade" id="deleteInsulinModal" tabindex="-1" aria-labelledby="deleteInsulinModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteInsulinModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذا الصنف من منصرف الأنسولين؟</p>
                    <p class="text-danger fw-bold">لا يمكن التراجع عن هذه العملية.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="delete-insulin-form" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
    // قائمة العناصر المضافة
    var itemsList = [];

    $(document).ready(function() {
        // التحقق مما إذا كان يجب إعادة تعيين القائمة المؤقتة
        {% if reset_temp_list %}
            // تفريغ القائمة المؤقتة
            itemsList = [];
            // إخفاء قسم القائمة المؤقتة
            $('#items_list_container').hide();
            // تحديث عرض القائمة
            updateItemsList();
        {% endif %}
        // تعيين التاريخ الحالي كقيمة افتراضية لشهر الصرف
        var today = new Date();
        $('#dispense_month').val(formatMonthInput(today));

        // تحميل المناطق عند اختيار الفرع
        $('#branch_id').on('change', function() {
            var branchId = $(this).val();
            if (branchId) {
                // تفعيل حقل المنطقة
                $('#area_id').prop('disabled', false);

                // تفريغ حقول المنطقة والعيادة
                $('#area_id').html('<option value="">-- اختر المنطقة --</option>');
                $('#clinic_id').html('<option value="">-- اختر العيادة --</option>');
                $('#clinic_id').prop('disabled', true);

                // جلب المناطق من الخادم
                $.getJSON('/api/areas/' + branchId, function(data) {
                    $.each(data, function(index, area) {
                        $('#area_id').append('<option value="' + area.id + '">' + area.name + '</option>');
                    });
                });
            } else {
                // تعطيل حقول المنطقة والعيادة
                $('#area_id').prop('disabled', true);
                $('#clinic_id').prop('disabled', true);

                // تفريغ حقول المنطقة والعيادة
                $('#area_id').html('<option value="">-- اختر المنطقة --</option>');
                $('#clinic_id').html('<option value="">-- اختر العيادة --</option>');
            }
        });

        // تحميل العيادات عند اختيار المنطقة
        $('#area_id').on('change', function() {
            var areaId = $(this).val();
            if (areaId) {
                // تفعيل حقل العيادة
                $('#clinic_id').prop('disabled', false);

                // تفريغ حقل العيادة
                $('#clinic_id').html('<option value="">-- اختر العيادة --</option>');

                // جلب العيادات من الخادم
                $.getJSON('/api/clinics/' + areaId, function(data) {
                    $.each(data, function(index, clinic) {
                        $('#clinic_id').append('<option value="' + clinic.id + '">' + clinic.name + '</option>');
                    });
                });
            } else {
                // تعطيل حقل العيادة
                $('#clinic_id').prop('disabled', true);

                // تفريغ حقل العيادة
                $('#clinic_id').html('<option value="">-- اختر العيادة --</option>');
            }
        });

        // ربط الفئة بالنوع
        $('#category').on('change', function() {
            var selectedCategory = $(this).val();
            console.log('تم اختيار الفئة:', selectedCategory);

            if (selectedCategory) {
                // تفعيل حقل النوع
                $('#type').prop('disabled', false);

                // تفريغ حقل النوع
                $('#type').html('<option value="">-- اختر النوع --</option>');

                // إضافة الأنواع المناسبة للفئة
                {% for type in insulin_types %}
                $('#type').append('<option value="{{ type.name }}">{{ type.name }}</option>');
                {% endfor %}

                // تحديث عرض الفئة
                $('#display_category').val(selectedCategory);
            } else {
                // تعطيل حقل النوع
                $('#type').prop('disabled', true);
                $('#type').html('<option value="">-- اختر الفئة أولاً --</option>');
                $('#display_category').val('');
            }

            // إعادة تعيين الحقول التابعة
            $('#insulin_code_id').prop('disabled', true);
            $('#insulin_code_id').html('<option value="">-- اختر النوع أولاً --</option>');
            $('#name').val('');
            $('#unit').val('');
        });

        // ربط النوع بالتكويد
        $('#type').on('change', function() {
            var selectedType = $(this).val();
            console.log('تم اختيار النوع:', selectedType);

            if (selectedType) {
                // تفعيل حقل التكويد
                $('#insulin_code_id').prop('disabled', false);

                // تفريغ حقل التكويد
                $('#insulin_code_id').html('<option value="">-- جاري التحميل... --</option>');

                // جلب التكويدات من الخادم حسب النوع
                $.getJSON('/api/insulin_codes/' + encodeURIComponent(selectedType), function(data) {
                    $('#insulin_code_id').html('<option value="">-- اختر تكويد الأنسولين --</option>');

                    $.each(data, function(index, code) {
                        $('#insulin_code_id').append('<option value="' + code.id + '" data-name="' + code.name + '" data-unit="' + code.unit + '">' + code.code + ' - ' + code.name + '</option>');
                    });

                    console.log('تم تحميل ' + data.length + ' تكويد للنوع: ' + selectedType);
                }).fail(function() {
                    $('#insulin_code_id').html('<option value="">-- خطأ في التحميل --</option>');
                    console.error('فشل في تحميل التكويدات للنوع:', selectedType);
                });
            } else {
                // تعطيل حقل التكويد
                $('#insulin_code_id').prop('disabled', true);
                $('#insulin_code_id').html('<option value="">-- اختر النوع أولاً --</option>');
                $('#name').val('');
                $('#unit').val('');
            }
        });

        // ملء اسم الأنسولين والوحدة تلقائيًا عند اختيار التكويد
        $('#insulin_code_id').on('change', function() {
            var selectedOption = $(this).find('option:selected');
            if (selectedOption.val()) {
                var insulinName = selectedOption.data('name');
                var insulinUnit = selectedOption.data('unit');
                $('#name').val(insulinName);
                $('#unit').val(insulinUnit);
                console.log('تم تحديد الأنسولين:', insulinName, 'الوحدة:', insulinUnit);
            } else {
                $('#name').val('');
                $('#unit').val('');
            }
        });

        // حساب التكلفة عند تغيير الكمية أو السعر
        $('#quantity, #price').on('input', function() {
            calculateCost();
        });

        // إدخال المعدل يدويًا
        $('#rate').prop('readonly', false);

        // إضافة عنصر إلى القائمة
        $('#add-insulin-btn').on('click', function() {
            addItemToList();
        });

        // حذف عنصر من القائمة
        $(document).on('click', '.remove-item', function() {
            var index = $(this).data('index');
            removeItemFromList(index);
        });

        // معالجة زر حفظ المنصرف
        $('#save-insulin-btn').on('click', function() {
            if (itemsList.length === 0) {
                alert('يرجى إضافة صنف واحد على الأقل قبل الحفظ');
                return;
            }

            // التحقق من اختيار العيادة
            if (!$('#clinic_id').val()) {
                alert('يرجى اختيار العيادة');
                return;
            }

            // تحديث البيانات المخفية
            $('#items_data').val(JSON.stringify(itemsList));
            var totalCost = updateTotalCost();
            $('#total_cost').val(totalCost.toFixed(2));

            // إنشاء بيانات النموذج
            var formData = new FormData();
            formData.append('clinic_id', $('#clinic_id').val());
            formData.append('dispense_month', $('#dispense_month').val());
            formData.append('items_data', JSON.stringify(itemsList));
            formData.append('total_cost', totalCost.toFixed(2));

            // عرض مؤشر التحميل
            var saveBtn = $(this);
            var originalText = saveBtn.html();
            saveBtn.html('<i class="mdi mdi-loading mdi-spin me-1"></i>جاري الحفظ...');
            saveBtn.prop('disabled', true);

            // طباعة البيانات للتصحيح
            console.log('بيانات النموذج المرسلة:', formData);

            // إنشاء بيانات النموذج البسيطة
            var simpleData = {
                clinic_id: $('#clinic_id').val(),
                dispense_month: $('#dispense_month').val(),
                items_data: JSON.stringify(itemsList),
                total_cost: totalCost.toFixed(2)
            };

            // طباعة البيانات للتصحيح
            console.log('بيانات النموذج المرسلة:', simpleData);

            // إرسال البيانات باستخدام AJAX
            $.ajax({
                url: '/insulin/dispense',
                type: 'POST',
                data: simpleData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'  // إضافة هذا الرأس لتحديد أن الطلب من AJAX
                },
                success: function(response) {
                    console.log('استجابة النجاح:', response);
                    alert('تم حفظ منصرف الأنسولين بنجاح');
                    // إعادة تحميل الصفحة لعرض البيانات المحدثة
                    location.reload();
                },
                error: function(xhr, status, error) {
                    console.error('Error saving insulin:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);

                    try {
                        var errorResponse = JSON.parse(xhr.responseText);
                        alert('حدث خطأ أثناء حفظ المنصرف: ' + errorResponse.message);
                    } catch (e) {
                        alert('حدث خطأ أثناء حفظ المنصرف. يرجى المحاولة مرة أخرى.');
                    }

                    // إعادة زر الحفظ إلى حالته الأصلية
                    saveBtn.html(originalText);
                    saveBtn.prop('disabled', false);
                }
            });
        });
    });

    // دالة لحساب التكلفة فقط
    function calculateCost() {
        var quantity = parseFloat($('#quantity').val()) || 0;
        var price = parseFloat($('#price').val()) || 0;

        var cost = quantity * price;

        $('#cost').val(cost.toFixed(2));
    }

    // دالة لإضافة عنصر إلى القائمة
    function addItemToList() {
        // التحقق من إدخال البيانات المطلوبة
        var name = $('#name').val();
        var type = $('#type').val();
        var unit = $('#unit').val();
        var quantity = parseFloat($('#quantity').val());
        var price = parseFloat($('#price').val());
        var casesCount = parseInt($('#cases_count').val());
        var balance = parseFloat($('#balance').val());
        var category = $('#category').val();
        var insulinCodeId = $('#insulin_code_id').val();
        var insulinCodeText = $('#insulin_code_id option:selected').text();

        if (!name || !type || !unit || !quantity || !price || !casesCount || !balance || !category) {
            alert('يرجى إدخال جميع البيانات المطلوبة');
            return;
        }

        // الحصول على التكلفة والمعدل
        var cost = parseFloat($('#cost').val()) || 0;
        var rate = parseFloat($('#rate').val()) || 0;

        // إضافة العنصر إلى القائمة
        var item = {
            name: name,
            type: type,
            unit: unit,
            quantity: quantity,
            price: price,
            cases_count: casesCount,
            cost: cost,
            rate: rate,
            balance: balance,
            category: category,
            insulin_code_id: insulinCodeId,
            insulin_code_text: insulinCodeId ? insulinCodeText : ''
        };

        itemsList.push(item);

        // تحديث عرض القائمة
        updateItemsList();

        // إظهار قائمة العناصر
        $('#items_list_container').show();

        // إعادة تعيين حقول الإدخال
        $('#name').val('');
        $('#type').val('');
        $('#unit').val('');
        $('#quantity').val('');
        $('#price').val('');
        $('#cases_count').val('');
        $('#cost').val('');
        $('#rate').val('');
        $('#balance').val('');
        $('#category').val('');
        // لا نقوم بإعادة تعيين تكويد الأنسولين لتسهيل إدخال أصناف متعددة من نفس التكويد
    }

    // دالة لحذف عنصر من القائمة
    function removeItemFromList(index) {
        itemsList.splice(index, 1);
        updateItemsList();

        // إخفاء القائمة إذا كانت فارغة
        if (itemsList.length === 0) {
            $('#items_list_container').hide();
        }
    }

    // تحديث عرض قائمة العناصر
    function updateItemsList() {
        // تفريغ القائمة
        $('#items_list').empty();

        // إضافة العناصر إلى القائمة
        itemsList.forEach(function(item, index) {
            var row = '<tr>' +
                '<td>' + item.name + (item.insulin_code_text ? ' <span class="badge bg-info">' + item.insulin_code_text + '</span>' : '') + '</td>' +
                '<td class="text-center">' + item.unit + '</td>' +
                '<td class="text-center">' + item.type + '</td>' +
                '<td class="text-center">' + item.quantity + '</td>' +
                '<td class="text-center">' + item.cases_count + '</td>' +
                '<td class="text-center">' + item.price + '</td>' +
                '<td class="text-center fw-bold">' + item.cost.toFixed(2) + '</td>' +
                '<td class="text-center">' + item.rate.toFixed(2) + '</td>' +
                '<td class="text-center">' + item.balance + '</td>' +
                '<td class="text-center">' + item.category + '</td>' +
                '<td class="text-center">' +
                '<button type="button" class="btn btn-sm btn-danger remove-item" data-index="' + index + '">' +
                '<i class="mdi mdi-delete"></i>' +
                '</button>' +
                '</td>' +
                '</tr>';

            $('#items_list').append(row);
        });

        // تحديث التكلفة الإجمالية
        updateTotalCost();

        // تحديث عدد الأصناف
        $('#items_count_info').text(itemsList.length);

        // تحديث البيانات المخفية
        $('#items_data').val(JSON.stringify(itemsList));
    }

    // دالة لحساب التكلفة الإجمالية لجميع العناصر
    function updateTotalCost() {
        var totalCost = 0;

        // حساب مجموع تكاليف جميع العناصر
        itemsList.forEach(function(item) {
            totalCost += item.cost;
        });

        // عرض التكلفة الإجمالية بتنسيق مناسب
        $('#total_cost_display').text(totalCost.toFixed(2));
        $('#total_cost').val(totalCost.toFixed(2));

        // إضافة تأثير بصري عند تغيير القيمة
        $('#total_cost_display').addClass('text-danger');
        setTimeout(function() {
            $('#total_cost_display').removeClass('text-danger');
        }, 300);

        return totalCost;
    }

    // معالجة زر تعديل الأنسولين
    $(document).on('click', '.edit-insulin-item', function() {
        var id = $(this).data('id');
        var name = $(this).data('name');
        var type = $(this).data('type');
        var unit = $(this).data('unit');
        var quantity = $(this).data('quantity');
        var casesCount = $(this).data('cases-count');
        var price = $(this).data('price');
        var cost = $(this).data('cost');
        var rate = $(this).data('rate');
        var balance = $(this).data('balance');
        var category = $(this).data('category');
        var clinicId = $(this).data('clinic-id');
        var dispenseMonth = $(this).data('dispense-month');
        var insulinCodeId = $(this).data('insulin-code-id');

        // طباعة معلومات للتصحيح
        console.log('بيانات الصنف المراد تعديله:');
        console.log('معرف الصنف:', id);
        console.log('الاسم:', name);
        console.log('النوع:', type);
        console.log('الوحدة:', unit);
        console.log('الكمية:', quantity);
        console.log('عدد الحالات:', casesCount);
        console.log('السعر:', price);
        console.log('التكلفة:', cost);
        console.log('المعدل:', rate);
        console.log('الرصيد:', balance);
        console.log('الفئة:', category);
        console.log('معرف العيادة:', clinicId);
        console.log('شهر الصرف:', dispenseMonth);
        console.log('معرف تكويد الأنسولين:', insulinCodeId);

        // ملء النموذج بالبيانات
        $('#edit_insulin_id').val(id);
        $('#edit_name').val(name);
        $('#edit_type').val(type);
        $('#edit_unit').val(unit);
        $('#edit_quantity').val(quantity);
        $('#edit_cases_count').val(casesCount);
        $('#edit_price').val(price);
        $('#edit_cost').val(cost);
        $('#edit_rate').val(rate);
        $('#edit_balance').val(balance);
        $('#edit_category').val(category);
        $('#edit_dispense_month').val(dispenseMonth);
        $('#edit_insulin_code_id').val(insulinCodeId);

        // حساب التكلفة إذا لم تكن موجودة
        if (!cost || cost == 0) {
            var calculatedCost = quantity * price;
            $('#edit_cost').val(calculatedCost.toFixed(2));
        }

        // تحديد العيادة في القائمة المنسدلة
        // أولاً، تأكد من أن جميع الخيارات محملة
        var clinicFound = false;
        $('#edit_clinic_id option').each(function() {
            if ($(this).val() == clinicId) {
                clinicFound = true;
                return false; // للخروج من الحلقة
            }
        });

        if (clinicFound) {
            $('#edit_clinic_id').val(clinicId);
            console.log('تم تحديد العيادة في القائمة المنسدلة:', clinicId);
        } else {
            console.log('لم يتم العثور على العيادة في القائمة المنسدلة. جاري جلب العيادات...');
            // جلب العيادات من الخادم
            $.getJSON('/api/clinics', function(data) {
                // تفريغ قائمة العيادات
                $('#edit_clinic_id').html('<option value="">-- اختر العيادة --</option>');

                // إضافة العيادات إلى القائمة
                $.each(data, function(index, clinic) {
                    var selected = (clinic.id == clinicId) ? 'selected' : '';
                    $('#edit_clinic_id').append('<option value="' + clinic.id + '" ' + selected + '>' + clinic.name + ' - ' + clinic.area_name + ' - ' + clinic.branch_name + '</option>');
                });

                console.log('تم تحميل العيادات في نموذج التعديل وتحديد العيادة:', clinicId);
            });
        }

        // عرض النافذة المنبثقة
        var editModal = new bootstrap.Modal(document.getElementById('editInsulinModal'));
        editModal.show();
    });

    // حساب التكلفة في نموذج التعديل عند تغيير الكمية أو السعر
    $('#edit_quantity, #edit_price').on('input', function() {
        console.log("تم تغيير الكمية أو السعر في نموذج التعديل");
        var quantity = parseFloat($('#edit_quantity').val()) || 0;
        var price = parseFloat($('#edit_price').val()) || 0;
        var cost = quantity * price;
        $('#edit_cost').val(cost.toFixed(2));
        console.log(`الكمية: ${quantity}, السعر: ${price}, التكلفة المحسوبة: ${cost.toFixed(2)}`);
    });

    // ملء اسم الأنسولين تلقائيًا عند اختيار التكويد في نموذج التعديل
    $('#edit_insulin_code_id').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        if (selectedOption.val()) {
            var insulinName = selectedOption.data('name');
            $('#edit_name').val(insulinName);
        }
    });

    // معالجة زر حفظ التغييرات
    $('#save-edit-insulin').on('click', function() {
        console.log("تم النقر على زر حفظ التغييرات");

        // التحقق من صحة البيانات
        if ($('#edit-insulin-form')[0].checkValidity()) {
            console.log("النموذج صالح، جاري الإرسال...");

            // تحديث قيمة التكلفة قبل الإرسال
            var quantity = parseFloat($('#edit_quantity').val()) || 0;
            var price = parseFloat($('#edit_price').val()) || 0;
            var cost = quantity * price;
            $('#edit_cost').val(cost.toFixed(2));

            // إرسال النموذج
            $('#edit-insulin-form').submit();
        } else {
            console.log("النموذج غير صالح، عرض رسائل التحقق");
            // تنشيط التحقق من صحة النموذج
            $('#edit-insulin-form')[0].reportValidity();
        }
    });

    // معالجة زر حذف الأنسولين
    $(document).on('click', '.delete-insulin-item', function() {
        var id = $(this).data('id');
        console.log("تم النقر على زر حذف الأنسولين للعنصر رقم: " + id);

        // تعيين عنوان النموذج
        $('#delete-insulin-form').attr('action', '/insulin/' + id + '/delete');
        console.log("تم تعيين عنوان نموذج الحذف إلى: " + $('#delete-insulin-form').attr('action'));

        // عرض النافذة المنبثقة
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteInsulinModal'));
        deleteModal.show();
    });
</script>
{% endblock %}
