{% extends "base.html" %}

{% block title %}إدارة مجموعات الأدوية - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 20px 20px 0 0 !important;
        border: none !important;
        padding: 20px 30px;
    }

    .section-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        overflow: hidden;
        height: 100%;
    }

    .section-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 15px 20px;
        margin: -1px -1px 20px -1px;
        border-radius: 15px 15px 0 0;
    }

    .table {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
        text-align: center;
    }

    .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .search-box {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        border: 2px solid #e3e6f0;
        padding: 8px 20px;
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="main-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0 fw-bold text-white">
                            <i class="mdi mdi-package-variant me-3"></i>إدارة مجموعات الأدوية
                        </h3>
                        <div>
                            <a href="{{ url_for('add_drugs_batch') }}" class="btn btn-light me-2">
                                <i class="mdi mdi-plus me-1"></i>إضافة مجموعة
                            </a>
                            <a href="{{ url_for('manage_drugs_new') }}" class="btn btn-light me-2">
                                <i class="mdi mdi-pill me-1"></i>إدارة الأدوية
                            </a>
                            <a href="{{ url_for('index') }}" class="btn btn-light">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_drugs or 0 }}</h4>
                                    <p class="mb-0">إجمالي الأدوية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>{{ total_categories or 0 }}</h4>
                                    <p class="mb-0">التصنيفات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>{{ expired_drugs or 0 }}</h4>
                                    <p class="mb-0">أدوية منتهية الصلاحية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>{{ recent_drugs or 0 }}</h4>
                                    <p class="mb-0">أدوية مضافة اليوم</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قائمة التصنيفات -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 fw-bold">
                                    <i class="mdi mdi-format-list-bulleted me-2"></i>الأدوية حسب التصنيف
                                </h6>
                                <div style="position: relative; width: 250px;">
                                    <input type="text" id="searchCategory" class="search-box" placeholder="بحث في التصنيفات..." style="width: 100%;">
                                </div>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-3" style="font-size: 2rem;"></i>
                                <strong>صفحة إدارة مجموعات الأدوية</strong>
                                <br>يمكنك من هنا إدارة الأدوية بشكل مجمع حسب التصنيفات.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تطابق عدد الأسطر
    document.querySelector('form').addEventListener('submit', function(e) {
        const drugsList = document.getElementById('drugs_list').value.trim().split('\n').filter(line => line.trim());
        const scientificNames = document.getElementById('scientific_names').value.trim().split('\n').filter(line => line.trim());
        
        if (scientificNames.length > 0 && scientificNames.length !== drugsList.length) {
            e.preventDefault();
            alert('عدد الأسماء العلمية يجب أن يتطابق مع عدد الأدوية');
        }
    });
});
</script>
{% endblock %}
