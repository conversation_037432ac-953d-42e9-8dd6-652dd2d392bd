{% extends "base.html" %}

{% block title %}تقرير المجموعات الدوائية - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="mdi mdi-package-variant me-2"></i>تقرير المجموعات الدوائية
                        </h4>
                        <div>
                            <button onclick="window.print()" class="btn btn-light me-2">
                                <i class="mdi mdi-printer me-1"></i>طباعة
                            </button>
                            <a href="{{ url_for('reports') }}" class="btn btn-light">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><strong>تاريخ التقرير:</strong> {{ now.strftime('%Y-%m-%d %H:%M') }}</h6>
                            {% if month %}
                            <h6><strong>الشهر:</strong> {{ month }}</h6>
                            {% endif %}
                            <h6><strong>النطاق:</strong> {{ scope_type }}</h6>
                        </div>
                        <div class="col-md-6 text-end">
                            <h6><strong>إجمالي المجموعات:</strong> {{ total_groups }}</h6>
                            <h6><strong>إجمالي التكلفة:</strong> {{ "{:,.2f}".format(total_cost) }} ج.م</h6>
                        </div>
                    </div>

                    <!-- جدول المجموعات الدوائية -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="mdi mdi-format-list-bulleted me-2"></i>تفاصيل المجموعات الدوائية
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if groups_data %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>#</th>
                                            <th>اسم المجموعة</th>
                                            <th>التكلفة</th>
                                            <th>العيادة</th>
                                            <th>المنطقة</th>
                                            <th>الفرع</th>
                                            <th>شهر الصرف</th>
                                            <th>الكود</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for group in groups_data %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td><strong>{{ group.group_name }}</strong></td>
                                            <td>{{ "{:,.2f}".format(group.cost) }} ج.م</td>
                                            <td>{{ group.clinic_name }}</td>
                                            <td>{{ group.area_name }}</td>
                                            <td>{{ group.branch_name }}</td>
                                            <td>{{ group.dispense_month }}</td>
                                            <td>
                                                {% if group.code_name %}
                                                    <span class="badge bg-info">{{ group.code_value }} - {{ group.code_name }}</span>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-secondary">
                                        <tr>
                                            <th colspan="2">الإجمالي</th>
                                            <th>{{ "{:,.2f}".format(total_cost) }} ج.م</th>
                                            <th colspan="5">{{ total_groups }} مجموعة</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-3" style="font-size: 2rem;"></i>
                                <strong>لا توجد مجموعات دوائية في الفترة المحددة.</strong>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    {% if groups_data %}
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">متوسط التكلفة</h6>
                                    <h4 class="text-primary">{{ "{:,.2f}".format(total_cost / total_groups) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">أعلى تكلفة</h6>
                                    <h4 class="text-success">{{ "{:,.2f}".format(groups_data[0].cost) }} ج.م</h4>
                                    <small class="text-muted">{{ groups_data[0].group_name }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إضافة وظائف التفاعل إذا لزم الأمر
$(document).ready(function() {
    // يمكن إضافة المزيد من الوظائف هنا
});
</script>
{% endblock %}
