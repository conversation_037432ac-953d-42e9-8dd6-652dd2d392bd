{% extends 'base.html' %}

{% block title %}تقرير المقارنة{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-info" onclick="showPrintOptions('pdf')" title="حفظ كـ PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>حفظ PDF
                    </button>
                    <button class="btn btn-primary" onclick="showPrintOptions('print')" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                    <button class="btn btn-outline-primary" onclick="window.print()" title="طباعة مباشرة">
                        <i class="mdi mdi-printer-outline me-1"></i>طباعة مباشرة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-compare me-2"></i>{{ report_title }}
                </h4>
                <div class="no-print">
                    <span class="badge bg-light text-dark">
                        <i class="mdi mdi-calendar-range me-1"></i>تقرير مقارنة
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="report-header">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                <h2><i class="mdi mdi-file-document-outline me-2"></i>{{ report_title }}</h2>
                <p><i class="mdi mdi-calendar me-2"></i>الفترة: {{ period }}</p>
                {% if parent_name %}
                <p><i class="mdi mdi-map-marker me-2"></i>النطاق: {{ parent_name }}</p>
                {% endif %}
                {% if category_name %}
                <p><i class="mdi mdi-tag me-2"></i>التصنيف: {{ category_name }}</p>
                {% endif %}
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-chart-bar" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">إجمالي العناصر</h5>
                            <h3>{{ items|length }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-cash-multiple" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">إجمالي التكلفة</h5>
                            <h3>{{ "{:,.2f}".format(total_cost) }} ج.م</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-percent" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">متوسط التكلفة</h5>
                            <h3>{{ "{:,.2f}".format(total_cost / items|length if items|length > 0 else 0) }} ج.م</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المقارنة -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="mdi mdi-table me-2"></i>تفاصيل المقارنة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead style="background: linear-gradient(135deg, #6c757d, #495057); color: white;">
                                <tr>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                        <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                        <span>#</span>
                                    </th>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                        {% if scope_type == 'all' %}
                                            <i class="mdi mdi-office-building me-2" style="color: #f39c12;"></i>
                                            <span>اسم الفرع</span>
                                        {% elif scope_type == 'branch' %}
                                            <i class="mdi mdi-map-marker me-2" style="color: #f39c12;"></i>
                                            <span>اسم المنطقة</span>
                                        {% else %}
                                            <i class="mdi mdi-hospital-building me-2" style="color: #f39c12;"></i>
                                            <span>اسم العيادة</span>
                                        {% endif %}
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 120px;">
                                        <i class="mdi mdi-pill me-2" style="color: #2ecc71;"></i>
                                        <span>عدد الأدوية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(الأصناف)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 150px;">
                                        <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                        <span>إجمالي التكلفة</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 120px;">
                                        <i class="mdi mdi-percent me-2" style="color: #e74c3c;"></i>
                                        <span>النسبة المئوية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(من الإجمالي)</small>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr>
                                    <td class="text-center">{{ loop.index }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if scope_type == 'all' %}
                                                <i class="mdi mdi-office-building text-primary me-2"></i>
                                            {% elif scope_type == 'branch' %}
                                                <i class="mdi mdi-map-marker text-success me-2"></i>
                                            {% else %}
                                                <i class="mdi mdi-hospital-building text-info me-2"></i>
                                            {% endif %}
                                            <span>{{ item.name }}</span>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ item.drugs_count }}</span>
                                    </td>
                                    <td class="text-center">
                                        <strong>{{ "{:,.2f}".format(item.total_cost) }}</strong>
                                    </td>
                                    <td class="text-center">
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                 style="width: {{ (item.total_cost / total_cost * 100) if total_cost > 0 else 0 }}%">
                                                {{ "{:.1f}".format((item.total_cost / total_cost * 100) if total_cost > 0 else 0) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-success">
                                    <th colspan="2" class="text-start">
                                        <i class="mdi mdi-calculator me-2"></i>الإجمالي العام
                                    </th>
                                    <th class="text-center">
                                        {% set total_drugs = 0 %}
                                        {% for item in items %}
                                            {% set total_drugs = total_drugs + item.drugs_count %}
                                        {% endfor %}
                                        {{ total_drugs }}
                                    </th>
                                    <th class="text-center">{{ "{:,.2f}".format(total_cost) }} ج.م</th>
                                    <th class="text-center">100.0%</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary no-print">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>



<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

<!-- مربع حوار خيارات الطباعة -->
<div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">خيارات الطباعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">اتجاه الصفحة:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                        <label class="form-check-label" for="orientationLandscape">أفقي (Landscape)</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                        <label class="form-check-label" for="orientationPortrait">رأسي (Portrait)</label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">حجم الخط:</label>
                    <select class="form-select" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script>
    var printAction = 'print';
    var printModal;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل صفحة تقرير المقارنة بنجاح');

        // التأكد من وجود Bootstrap
        if (typeof bootstrap === 'undefined') {
            console.warn('Bootstrap غير محمل - سيتم استخدام الطباعة المباشرة');
        }

        // إضافة مستمعات للأزرار كبديل
        const printBtn = document.querySelector('button[onclick*="showPrintOptions(\'print\')"]');
        const pdfBtn = document.querySelector('button[onclick*="showPrintOptions(\'pdf\')"]');

        if (printBtn) {
            printBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                showPrintOptions('print');
            });
        }

        if (pdfBtn) {
            pdfBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                showPrintOptions('pdf');
            });
        }
    });

    function showPrintOptions(action) {
        console.log('showPrintOptions called with action:', action);
        printAction = action;

        // التأكد من وجود Bootstrap
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap غير محمل');
            alert('خطأ: Bootstrap غير محمل. سيتم الطباعة مباشرة.');
            performPrint();
            return;
        }

        // إنشاء أو عرض النافذة المنبثقة
        const modalElement = document.getElementById('printOptionsModal');
        if (!modalElement) {
            console.error('عنصر النافذة المنبثقة غير موجود');
            alert('خطأ: نافذة الخيارات غير موجودة. سيتم الطباعة مباشرة.');
            performPrint();
            return;
        }

        if (!printModal) {
            printModal = new bootstrap.Modal(modalElement);

            // إضافة مستمع للزر
            const confirmButton = document.getElementById('confirmPrint');
            if (confirmButton) {
                confirmButton.addEventListener('click', function() {
                    console.log('تم الضغط على زر التأكيد');
                    printModal.hide();
                    setTimeout(performPrint, 300); // انتظار قصير لإغلاق النافذة
                });
            }
        }

        printModal.show();
    }

    function performPrint() {
        try {
            console.log('performPrint called with action:', printAction);

            // الحصول على الخيارات
            var orientationElement = document.querySelector('input[name="pageOrientation"]:checked');
            var fontSizeElement = document.getElementById('fontSize');

            var orientation = orientationElement ? orientationElement.value : 'landscape';
            var fontSize = fontSizeElement ? fontSizeElement.value : 'medium';

            console.log('Orientation:', orientation, 'Font Size:', fontSize);

            // إزالة أي أنماط طباعة سابقة
            var existingStyle = document.getElementById('print-orientation-style');
            if (existingStyle) {
                document.head.removeChild(existingStyle);
            }

            // إنشاء أنماط الطباعة الجديدة
            var styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';

            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };

            var printCSS = `
                @page {
                    size: ${orientation};
                    margin: 1cm;
                }

                @media print {
                    body {
                        font-size: ${fontSizeMap[fontSize]};
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                    }

                    .table {
                        font-size: ${fontSizeMap[fontSize]};
                        border-collapse: collapse;
                    }

                    .table th, .table td {
                        border: 1px solid #000 !important;
                        padding: 5px !important;
                    }

                    .card-header {
                        background-color: #f0f0f0 !important;
                        color: #000 !important;
                        -webkit-print-color-adjust: exact;
                    }

                    .btn, .no-print {
                        display: none !important;
                    }

                    .container-fluid {
                        padding: 0 !important;
                    }

                    .report-logo {
                        display: block !important;
                        max-width: 120px;
                        margin: 0 auto 20px;
                    }
                }
            `;

            styleElement.innerHTML = printCSS;
            document.head.appendChild(styleElement);

            console.log('Print styles applied, calling window.print()');

            // تأخير قصير للتأكد من تطبيق الأنماط
            setTimeout(function() {
                window.print();

                // إزالة الأنماط بعد الطباعة
                setTimeout(function() {
                    if (document.head.contains(styleElement)) {
                        document.head.removeChild(styleElement);
                        console.log('Print styles removed');
                    }
                }, 2000);
            }, 100);

        } catch (error) {
            console.error("Error in performPrint:", error);
            alert("حدث خطأ أثناء الطباعة: " + error.message);
        }
    }

    async function exportToExcel() {
        try {
            console.log('بدء تصدير Excel...');

            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('تقرير المقارنة');

            let currentRow = 1;

            // إضافة شعار الشركة (نص بدلاً من صورة)
            const logoCell = worksheet.getCell('A' + currentRow);
            logoCell.value = 'الهيئة العامة للتأمين الصحي';
            logoCell.font = { bold: true, size: 16, color: { argb: 'FF1F4E79' } };
            logoCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':E' + currentRow);
            currentRow += 2;

            // إضافة عنوان التقرير
            const titleCell = worksheet.getCell('A' + currentRow);
            titleCell.value = document.querySelector('h2').innerText || 'تقرير المقارنة';
            titleCell.font = { bold: true, size: 18, color: { argb: 'FFFFFFFF' } };
            titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF667EEA' } };
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':E' + currentRow);
            currentRow += 2;

            // إضافة معلومات التقرير
            const reportInfo = document.querySelectorAll('.report-header p');
            reportInfo.forEach(function(info) {
                const infoCell = worksheet.getCell('A' + currentRow);
                infoCell.value = info.innerText;
                infoCell.font = { bold: true, size: 12 };
                infoCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FC' } };
                currentRow++;
            });
            currentRow++; // سطر فارغ

            // إضافة الإحصائيات السريعة
            const statsTitle = worksheet.getCell('A' + currentRow);
            statsTitle.value = 'الإحصائيات السريعة';
            statsTitle.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
            statsTitle.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
            statsTitle.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':C' + currentRow);
            currentRow++;

            // إضافة بيانات الإحصائيات
            const statsCards = document.querySelectorAll('.card.bg-primary, .card.bg-success, .card.bg-info');
            statsCards.forEach(function(card, index) {
                const title = card.querySelector('h5').innerText;
                const value = card.querySelector('h3').innerText;

                const titleCell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                titleCell.value = title;
                titleCell.font = { bold: true };
                titleCell.alignment = { horizontal: 'center' };

                const valueCell = worksheet.getCell(String.fromCharCode(65 + index) + (currentRow + 1));
                valueCell.value = value;
                valueCell.font = { bold: true, color: { argb: 'FF0066CC' } };
                valueCell.alignment = { horizontal: 'center' };
            });
            currentRow += 3; // سطرين للإحصائيات + سطر فارغ

            // إضافة عناوين الأعمدة
            const table = document.querySelector('.table');
            const headers = [];
            table.querySelectorAll('thead th').forEach(function(th) {
                headers.push(th.innerText.replace(/\n/g, ' ').trim());
            });

            headers.forEach((header, index) => {
                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                cell.value = header;
                cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6C757D' } };
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
            currentRow++;

            // إضافة بيانات الصفوف
            table.querySelectorAll('tbody tr').forEach(function(tr) {
                const cells = tr.querySelectorAll('td');
                cells.forEach((cell, index) => {
                    const excelCell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    let cellValue = cell.innerText.trim();

                    // تحويل الأرقام إلى قيم رقمية
                    if (index === 2 || index === 3) { // أعمدة الأرقام
                        const numValue = parseFloat(cellValue.replace(/[^\d.-]/g, ''));
                        if (!isNaN(numValue)) {
                            excelCell.value = numValue;
                            if (index === 3) { // عمود التكلفة
                                excelCell.numFmt = '#,##0.00';
                            }
                        } else {
                            excelCell.value = cellValue;
                        }
                    } else {
                        excelCell.value = cellValue;
                    }

                    excelCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    excelCell.border = {
                        top: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                        left: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                        bottom: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                        right: { style: 'thin', color: { argb: 'FFDDDDDD' } }
                    };

                    // تلوين صفوف متناوبة
                    if (currentRow % 2 === 0) {
                        excelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } };
                    }
                });
                currentRow++;
            });

            // إضافة صف الإجمالي
            const footerRow = table.querySelector('tfoot tr');
            if (footerRow) {
                const footerCells = footerRow.querySelectorAll('th');
                footerCells.forEach((cell, index) => {
                    const excelCell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    let cellValue = cell.innerText.trim();

                    if (index === 2 || index === 3) { // أعمدة الأرقام
                        const numValue = parseFloat(cellValue.replace(/[^\d.-]/g, ''));
                        if (!isNaN(numValue)) {
                            excelCell.value = numValue;
                            if (index === 3) { // عمود التكلفة
                                excelCell.numFmt = '#,##0.00';
                            }
                        } else {
                            excelCell.value = cellValue;
                        }
                    } else {
                        excelCell.value = cellValue;
                    }

                    excelCell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                    excelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
                    excelCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    excelCell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
                currentRow += 2;
            }

            // إضافة تاريخ التقرير
            const dateCell = worksheet.getCell('A' + currentRow);
            dateCell.value = 'تاريخ إنشاء التقرير: ' + new Date().toLocaleDateString('ar-EG');
            dateCell.font = { italic: true, size: 10 };
            dateCell.alignment = { horizontal: 'right' };
            currentRow++;

            // إضافة حقوق الملكية
            const copyrightCell = worksheet.getCell('A' + currentRow);
            copyrightCell.value = 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © ' + new Date().getFullYear();
            copyrightCell.font = { italic: true, size: 9, color: { argb: 'FF666666' } };
            copyrightCell.alignment = { horizontal: 'center' };
            worksheet.mergeCells('A' + currentRow + ':E' + currentRow);

            // تحديد عرض الأعمدة
            worksheet.columns = [
                { width: 8 },   // #
                { width: 25 },  // اسم الفرع/المنطقة/العيادة
                { width: 15 },  // عدد الأدوية
                { width: 18 },  // إجمالي التكلفة
                { width: 15 }   // النسبة المئوية
            ];

            // تصدير الملف
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const fileName = "تقرير_المقارنة_منسق_" + new Date().toISOString().slice(0, 10) + ".xlsx";
            saveAs(blob, fileName);

            console.log('تم تصدير Excel بنجاح');
        } catch (error) {
            console.error("Error exporting to Excel:", error);
            alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
        }
    }

    // دالة طباعة بديلة بسيطة
    function simplePrint() {
        console.log('استخدام الطباعة البسيطة');

        // إخفاء العناصر غير المرغوب فيها
        var noprint = document.querySelectorAll('.no-print, .btn');
        noprint.forEach(function(el) {
            el.style.display = 'none';
        });

        // طباعة
        window.print();

        // إعادة إظهار العناصر
        setTimeout(function() {
            noprint.forEach(function(el) {
                el.style.display = '';
            });
        }, 1000);
    }

    // دالة احتياطية للطباعة
    window.fallbackPrint = function() {
        console.log('استخدام الطباعة الاحتياطية');
        simplePrint();
    };

    // إضافة مستمع لأخطاء JavaScript
    window.addEventListener('error', function(e) {
        console.error('خطأ JavaScript:', e.error);
        if (e.error && e.error.message && e.error.message.includes('bootstrap')) {
            console.log('خطأ متعلق بـ Bootstrap - سيتم استخدام الطباعة البديلة');
        }
    });
</script>

<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    .card {
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .table {
        border-radius: 10px;
        overflow: hidden;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.1);
        transform: scale(1.01);
    }

    .progress {
        border-radius: 10px;
        overflow: hidden;
    }

    .badge {
        border-radius: 20px;
    }

    @media print {
        .btn, .no-print {
            display: none !important;
        }

        .card {
            border: 1px solid #ddd !important;
            box-shadow: none !important;
            break-inside: avoid;
            border-radius: 0 !important;
        }

        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
            -webkit-print-color-adjust: exact;
        }

        .table {
            font-size: 11px;
            border-collapse: collapse;
        }

        .table th, .table td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
        }

        .table thead th {
            background-color: #f8f9fa !important;
            color: #000 !important;
            -webkit-print-color-adjust: exact;
        }

        .container-fluid {
            padding: 0;
            background: white !important;
        }

        body {
            background: white !important;
            font-size: 12pt;
        }

        .report-logo {
            display: block !important;
            max-width: 120px;
            height: auto;
            margin: 0 auto 15px;
        }

        .progress {
            border: 1px solid #ddd;
            background-color: #f8f9fa !important;
        }

        .progress-bar {
            background-color: #28a745 !important;
            -webkit-print-color-adjust: exact;
        }

        .badge {
            border: 1px solid #ddd;
            background-color: #f8f9fa !important;
            color: #000 !important;
        }

        /* إضافة حقوق الملكية في نهاية صفحة الطباعة */
        .container-fluid::after {
            content: 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}';
            display: block;
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            page-break-inside: avoid;
        }

        /* تحسين كسر الصفحات */
        .card {
            page-break-inside: avoid;
        }

        .table {
            page-break-inside: auto;
        }

        .table tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        .table thead {
            display: table-header-group;
        }

        .table tfoot {
            display: table-footer-group;
        }
    }

    /* تحسينات إضافية للواجهة */
    .report-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .report-header h2 {
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .report-header p {
        color: #7f8c8d;
        margin-bottom: 5px;
    }
</style>
{% endblock %}
