{% extends 'base.html' %}

{% block title %}تقرير المقارنة{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-secondary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-compare me-2"></i>{{ report_title }}
                </h4>
                <button class="btn btn-light btn-print no-print" onclick="window.print()">
                    <i class="mdi mdi-printer me-1"></i>طباعة التقرير
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="report-header">
                <img src="{{ url_for('static', filename='images/Ahmed.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                <h2><i class="mdi mdi-file-document-outline me-2"></i>{{ report_title }}</h2>
                <p><i class="mdi mdi-calendar me-2"></i>الفترة: {{ period }}</p>
                {% if parent_name %}
                <p><i class="mdi mdi-map-marker me-2"></i>النطاق: {{ parent_name }}</p>
                {% endif %}
                {% if category_name %}
                <p><i class="mdi mdi-tag me-2"></i>التصنيف: {{ category_name }}</p>
                {% endif %}
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-chart-bar" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">إجمالي العناصر</h5>
                            <h3>{{ items|length }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-cash-multiple" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">إجمالي التكلفة</h5>
                            <h3>{{ "{:,.2f}".format(total_cost) }} ج.م</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-percent" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">متوسط التكلفة</h5>
                            <h3>{{ "{:,.2f}".format(total_cost / items|length if items|length > 0 else 0) }} ج.م</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المقارنة -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="mdi mdi-table me-2"></i>تفاصيل المقارنة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead style="background: linear-gradient(135deg, #6c757d, #495057); color: white;">
                                <tr>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                        <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                        <span>#</span>
                                    </th>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                        {% if scope_type == 'all' %}
                                            <i class="mdi mdi-office-building me-2" style="color: #f39c12;"></i>
                                            <span>اسم الفرع</span>
                                        {% elif scope_type == 'branch' %}
                                            <i class="mdi mdi-map-marker me-2" style="color: #f39c12;"></i>
                                            <span>اسم المنطقة</span>
                                        {% else %}
                                            <i class="mdi mdi-hospital-building me-2" style="color: #f39c12;"></i>
                                            <span>اسم العيادة</span>
                                        {% endif %}
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 120px;">
                                        <i class="mdi mdi-pill me-2" style="color: #2ecc71;"></i>
                                        <span>عدد الأدوية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(الأصناف)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 150px;">
                                        <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                        <span>إجمالي التكلفة</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 120px;">
                                        <i class="mdi mdi-percent me-2" style="color: #e74c3c;"></i>
                                        <span>النسبة المئوية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(من الإجمالي)</small>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr>
                                    <td class="text-center">{{ loop.index }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if scope_type == 'all' %}
                                                <i class="mdi mdi-office-building text-primary me-2"></i>
                                            {% elif scope_type == 'branch' %}
                                                <i class="mdi mdi-map-marker text-success me-2"></i>
                                            {% else %}
                                                <i class="mdi mdi-hospital-building text-info me-2"></i>
                                            {% endif %}
                                            <span>{{ item.name }}</span>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ item.drugs_count }}</span>
                                    </td>
                                    <td class="text-center">
                                        <strong>{{ "{:,.2f}".format(item.total_cost) }}</strong>
                                    </td>
                                    <td class="text-center">
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                 style="width: {{ (item.total_cost / total_cost * 100) if total_cost > 0 else 0 }}%">
                                                {{ "{:.1f}".format((item.total_cost / total_cost * 100) if total_cost > 0 else 0) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-success">
                                    <th colspan="2" class="text-start">
                                        <i class="mdi mdi-calculator me-2"></i>الإجمالي العام
                                    </th>
                                    <th class="text-center">
                                        {% set total_drugs = 0 %}
                                        {% for item in items %}
                                            {% set total_drugs = total_drugs + item.drugs_count %}
                                        {% endfor %}
                                        {{ total_drugs }}
                                    </th>
                                    <th class="text-center">{{ "{:,.2f}".format(total_cost) }} ج.م</th>
                                    <th class="text-center">100.0%</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary no-print">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- أزرار الإجراءات -->
<div class="action-buttons no-print">
    <button class="action-btn print-btn" onclick="showPrintOptions('print')" title="طباعة التقرير">
        <i class="mdi mdi-printer"></i>
    </button>
    <button class="action-btn excel-btn" onclick="exportToExcel()" title="تصدير إلى Excel">
        <i class="mdi mdi-file-excel"></i>
    </button>
    <button class="action-btn pdf-btn" onclick="showPrintOptions('pdf')" title="طباعة كملف PDF"
            style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; border: none; border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3); transition: all 0.3s ease;">
        <div style="text-align: center; line-height: 1;">
            <i class="mdi mdi-file-pdf" style="font-size: 18px; display: block; margin-bottom: 3px;"></i>
            <span style="font-size: 14px; font-weight: bold; display: block; letter-spacing: 0.5px;">PDF</span>
        </div>
    </button>
    <a href="{{ url_for('reports') }}" class="action-btn back-btn" title="العودة إلى التقارير">
        <i class="mdi mdi-arrow-left"></i>
    </a>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    function exportToExcel() {
        const table = document.querySelector('table');
        const wb = XLSX.utils.table_to_book(table, {sheet: "تقرير المقارنة"});
        XLSX.writeFile(wb, `تقرير_المقارنة_${new Date().getFullYear()}.xlsx`);
    }

    function showPrintOptions(action) {
        if (action === 'pdf') {
            window.print();
        } else {
            window.print();
        }
    }
</script>
{% endblock %}
