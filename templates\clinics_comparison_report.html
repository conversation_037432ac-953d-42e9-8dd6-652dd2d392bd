{% extends 'base.html' %}

{% block title %}مقارنة بين العيادات{% endblock %}

{% block styles %}
<style>
    @media print {
        body {
            background-color: #fff !important;
        }
        .no-print {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .card-header {
            background-color: #f8f9fc !important;
            color: #000 !important;
            border-bottom: 2px solid #17a2b8 !important;
        }
        .table {
            width: 100% !important;
            border-collapse: collapse !important;
        }
        .table th, .table td {
            border: 1px solid #ddd !important;
        }
        footer {
            display: none !important;
        }
    }

    .report-header {
        background-color: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        text-align: center;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .report-header h2 {
        color: #17a2b8;
        margin-bottom: 1rem;
    }

    .report-header p {
        color: #5a5c69;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .report-logo {
        max-width: 150px;
        height: auto;
        margin-bottom: 15px;
    }

    @media print {
        .report-logo {
            display: block;
            max-width: 150px;
            height: auto;
            margin: 0 auto 15px;
        }
    }

    .comparison-card {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
    }

    .comparison-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }

    .total-card {
        background-color: #17a2b8;
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
        text-align: center;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .total-card h3 {
        margin-bottom: 0;
    }

    .btn-print {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .btn-print:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* تنسيق الرسم البياني */
    .chart-container {
        height: 400px;
        margin-bottom: 2rem;
    }

    /* تنسيق جدول المقارنة */
    .comparison-table th {
        background-color: #f8f9fc;
        font-weight: 700;
    }

    .comparison-table .bg-light-success {
        background-color: rgba(28, 200, 138, 0.1);
    }

    .comparison-table .bg-light-danger {
        background-color: rgba(231, 74, 59, 0.1);
    }

    .comparison-table .text-success {
        color: #1cc88a !important;
    }

    .comparison-table .text-danger {
        color: #e74a3b !important;
    }

    /* أنماط أزرار الإجراءات */
    .action-buttons {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .action-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: none;
        font-size: 24px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-decoration: none;
    }

    .action-btn:hover {
        transform: scale(1.1);
        color: white;
        text-decoration: none;
    }

    .print-btn {
        background-color: #17a2b8;
    }

    .print-btn:hover {
        background-color: #138496;
    }

    .back-btn {
        background-color: #6c757d;
    }

    .back-btn:hover {
        background-color: #5a6268;
    }

    .excel-btn {
        background-color: #1d6f42;
    }

    .excel-btn:hover {
        background-color: #185a36;
    }

    .pdf-btn {
        background-color: #e74a3b;
    }

    .pdf-btn:hover {
        background-color: #c0392b;
    }

    .clinic-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .clinic-header {
        background-color: #17a2b8;
        color: white;
        padding: 1rem;
        font-weight: 700;
    }

    .clinic-stats {
        background-color: #f8f9fc;
        padding: 1rem;
        border-bottom: 1px solid #e3e6f0;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .stat-item:last-child {
        margin-bottom: 0;
    }

    .stat-value {
        font-weight: 700;
        color: #17a2b8;
    }

    .area-info {
        background-color: #e9ecef;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        color: #6c757d;
        border-bottom: 1px solid #dee2e6;
    }

    .branch-info {
        background-color: #dee2e6;
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
        color: #495057;
        border-bottom: 1px solid #ced4da;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-hospital-building me-2"></i>مقارنة بين العيادات
                </h4>
                <button class="btn btn-light btn-print no-print" onclick="window.print()">
                    <i class="mdi mdi-printer me-1"></i>طباعة التقرير
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="report-header">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                <h2><i class="mdi mdi-file-document-outline me-2"></i>{{ report_title }}</h2>
                <p><i class="mdi mdi-calendar me-2"></i>الفترة: {{ period }}</p>
                <p><i class="mdi mdi-map-marker me-2"></i>المنطقة: {{ area_name }}</p>
                <p><i class="mdi mdi-domain me-2"></i>الفرع: {{ branch_name }}</p>
                <p><i class="mdi mdi-tag-multiple me-2"></i>تصنيف الدواء: {{ category_name }}</p>
            </div>

            <!-- الرسم البياني -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="mdi mdi-chart-bar me-2"></i>رسم بياني للمقارنة بين العيادات</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="clinicsChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- جدول المقارنة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="mdi mdi-table me-2"></i>جدول المقارنة بين العيادات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover comparison-table">
                            <thead style="background: linear-gradient(135deg, #8e44ad, #9b59b6); color: white;">
                                <tr>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                        <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                        <span>#</span>
                                    </th>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                        <i class="mdi mdi-hospital-building me-2" style="color: #f39c12;"></i>
                                        <span>اسم العيادة</span>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 120px;">
                                        <i class="mdi mdi-pill me-2" style="color: #2ecc71;"></i>
                                        <span>عدد الأدوية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(الأصناف)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 150px;">
                                        <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                        <span>إجمالي التكلفة</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e74c3c; min-width: 120px;">
                                        <i class="mdi mdi-percent me-2" style="color: #e74c3c;"></i>
                                        <span>النسبة المئوية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(من الإجمالي)</small>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for clinic in clinics %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ clinic.name }}</td>
                                    <td class="text-center">{{ clinic.drugs_count }}</td>
                                    <td class="text-center fw-bold">{{ clinic.total_cost }} جنيه</td>
                                    <td class="text-center">{{ clinic.percentage }}%</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-info">
                                    <th colspan="3" class="text-start">الإجمالي</th>
                                    <th class="text-center">{{ total_cost }} جنيه</th>
                                    <th class="text-center">100%</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- تفاصيل العيادات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="mdi mdi-format-list-bulleted me-2"></i>تفاصيل العيادات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for clinic in clinics %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="clinic-card">
                                <div class="clinic-header">
                                    <h6 class="mb-0">{{ clinic.name }}</h6>
                                </div>
                                <div class="branch-info">
                                    <i class="mdi mdi-domain me-1"></i>الفرع: {{ branch_name }}
                                </div>
                                <div class="area-info">
                                    <i class="mdi mdi-map-marker me-1"></i>المنطقة: {{ area_name }}
                                </div>
                                <div class="clinic-stats">
                                    <div class="stat-item">
                                        <span>إجمالي التكلفة:</span>
                                        <span class="stat-value">{{ clinic.total_cost }} جنيه</span>
                                    </div>
                                    <div class="stat-item">
                                        <span>النسبة المئوية:</span>
                                        <span class="stat-value">{{ clinic.percentage }}%</span>
                                    </div>
                                    <div class="progress mb-3" style="height: 10px;">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ clinic.percentage }}%"></div>
                                    </div>
                                    <div class="stat-item">
                                        <span>عدد الأدوية:</span>
                                        <span class="stat-value">{{ clinic.drugs_count }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary no-print">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- أزرار الإجراءات -->
<div class="action-buttons no-print">
    <button class="action-btn print-btn" onclick="showPrintOptions('print')" title="طباعة التقرير">
        <i class="mdi mdi-printer"></i>
    </button>
    <button class="action-btn excel-btn" onclick="exportToExcel()" title="تصدير إلى Excel">
        <i class="mdi mdi-file-excel"></i>
    </button>
    <button class="action-btn pdf-btn" onclick="showPrintOptions('pdf')" title="طباعة كملف PDF">
        <i class="mdi mdi-file-pdf"></i>
    </button>
    <a href="{{ url_for('reports') }}" class="action-btn back-btn" title="العودة إلى التقارير">
        <i class="mdi mdi-arrow-left"></i>
    </a>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script>
    // إنشاء الرسم البياني
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('clinicsChart').getContext('2d');

        const clinicNames = [
            {% for clinic in clinics %}
            '{{ clinic.name }}',
            {% endfor %}
        ];

        const clinicCosts = [
            {% for clinic in clinics %}
            {{ clinic.total_cost }},
            {% endfor %}
        ];

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: clinicNames,
                datasets: [{
                    label: 'إجمالي التكلفة (جنيه)',
                    data: clinicCosts,
                    backgroundColor: [
                        '#17a2b8',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b',
                        '#858796',
                        '#5a5c69'
                    ],
                    borderColor: [
                        '#17a2b8',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b',
                        '#858796',
                        '#5a5c69'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'مقارنة التكلفة بين العيادات'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' جنيه';
                            }
                        }
                    }
                }
            }
        });
    });

    // وظائف الطباعة والتصدير
    function showPrintOptions(action) {
        if (action === 'print') {
            window.print();
        } else if (action === 'pdf') {
            window.print();
        }
    }

    // وظيفة تصدير البيانات إلى Excel
    async function exportToExcel() {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('مقارنة العيادات');

            let currentRow = 1;

            // إضافة عنوان التقرير
            const titleCell = worksheet.getCell('A' + currentRow);
            titleCell.value = 'مقارنة بين العيادات';
            titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
            titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF17A2B8' } };
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':E' + currentRow);
            currentRow += 2;

            // إضافة معلومات التقرير
            const reportInfo = [
                'الفترة: {{ period }}',
                'المنطقة: {{ area_name }}',
                'الفرع: {{ branch_name }}',
                'تصنيف الدواء: {{ category_name }}'
            ];

            reportInfo.forEach(function(info) {
                const infoCell = worksheet.getCell('A' + currentRow);
                infoCell.value = info;
                infoCell.font = { bold: true, size: 12 };
                infoCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FC' } };
                worksheet.mergeCells('A' + currentRow + ':E' + currentRow);
                currentRow++;
            });
            currentRow++;

            // عناوين الأعمدة
            const headers = ['#', 'اسم العيادة', 'عدد الأدوية', 'إجمالي التكلفة', 'النسبة المئوية'];
            headers.forEach((header, index) => {
                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                cell.value = header;
                cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF17A2B8' } };
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
            currentRow++;

            // بيانات العيادات
            {% for clinic in clinics %}
            const clinicData{{ loop.index }} = [{{ loop.index }}, '{{ clinic.name }}', {{ clinic.drugs_count }}, {{ clinic.total_cost }}, '{{ clinic.percentage }}%'];
            clinicData{{ loop.index }}.forEach((data, index) => {
                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                cell.value = data;
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.border = {
                    top: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                    left: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                    bottom: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                    right: { style: 'thin', color: { argb: 'FFDDDDDD' } }
                };
            });
            currentRow++;
            {% endfor %}

            // الإجمالي
            const totalData = ['', 'الإجمالي', '', {{ total_cost }}, '100%'];
            totalData.forEach((data, index) => {
                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                cell.value = data;
                cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF17A2B8' } };
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });

            // تحديد عرض الأعمدة
            worksheet.columns = [
                { width: 5 },   // #
                { width: 25 },  // اسم العيادة
                { width: 15 },  // عدد الأدوية
                { width: 18 },  // إجمالي التكلفة
                { width: 15 }   // النسبة المئوية
            ];

            // تصدير الملف
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const fileName = "مقارنة_العيادات_" + new Date().toISOString().slice(0, 10) + ".xlsx";
            saveAs(blob, fileName);
        } catch (error) {
            console.error("Error exporting to Excel:", error);
            alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
        }
    }
</script>
{% endblock %}
