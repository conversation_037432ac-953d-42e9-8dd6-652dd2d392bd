{% extends "base.html" %}

{% block title %}صرف الأدوية - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 20px 20px 0 0 !important;
        border: none !important;
        padding: 20px 30px;
    }

    .section-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .section-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .section-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 15px 20px;
        margin: -1px -1px 20px -1px;
        border-radius: 15px 15px 0 0;
    }

    .drug-section .section-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .list-section .section-header {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 12px 15px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(17, 153, 142, 0.3);
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
        background: linear-gradient(135deg, #0d8377 0%, #2dd36f 100%);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 15px 40px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border: none;
        border-radius: 25px;
        padding: 15px 40px;
        font-weight: 600;
        color: #333;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(168, 237, 234, 0.3);
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(168, 237, 234, 0.4);
        color: #333;
    }

    .btn-danger {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
    }

    .btn-warning {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
        color: white;
        font-weight: 600;
    }

    .btn-warning:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .table-warning {
        background-color: rgba(255, 193, 7, 0.1) !important;
        border: 2px solid #ffc107;
    }

    .table {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
        text-align: center;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .table tbody td {
        padding: 15px;
        text-align: center;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .badge {
        border-radius: 20px;
        padding: 8px 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .total-card {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(17, 153, 142, 0.3);
        transition: all 0.3s ease;
    }

    .total-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(17, 153, 142, 0.4);
    }

    .history-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-top: 30px;
    }

    .history-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 20px 30px;
        border-radius: 20px 20px 0 0;
        border: none;
    }

    .floating-label {
        position: relative;
        margin-bottom: 20px;
    }

    .floating-label .form-control,
    .floating-label .form-select {
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .floating-label label {
        position: absolute;
        top: 15px;
        right: 15px;
        transition: all 0.3s ease;
        color: #6c757d;
        pointer-events: none;
        background: white;
        padding: 0 5px;
        border-radius: 5px;
    }

    .floating-label .form-control:focus + label,
    .floating-label .form-control:not(:placeholder-shown) + label,
    .floating-label .form-select:focus + label,
    .floating-label .form-select:not([value=""]) + label {
        top: -8px;
        font-size: 0.8rem;
        color: #667eea;
        font-weight: 600;
    }

    .icon-input {
        position: relative;
    }

    .icon-input i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        z-index: 10;
    }

    .icon-input .form-control,
    .icon-input .form-select {
        padding-left: 45px;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .section-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .section-card:nth-child(2) {
        animation-delay: 0.2s;
    }

    .section-card:nth-child(3) {
        animation-delay: 0.4s;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="main-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0 fw-bold text-white">
                            <i class="mdi mdi-pill me-3"></i>صرف الأدوية
                        </h3>
                        <div>
                            <a href="{{ url_for('manage_drugs_new') }}" class="btn btn-light me-2">
                                <i class="mdi mdi-pill me-1"></i>إدارة الأدوية
                            </a>
                            <a href="/" class="btn btn-light">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form id="drug-dispense-form" method="POST" action="/dispense_new">
                        <div class="row g-4">
                            <!-- بيانات المنصرف -->
                            <div class="col-lg-4">
                                <div class="section-card mb-4">
                                    <div class="section-header">
                                        <h6 class="mb-0 fw-bold"><i class="mdi mdi-information me-2"></i>بيانات المنصرف</h6>
                                    </div>
                                    <div class="p-3">
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-hospital-building"></i>
                                            <select class="form-select" id="clinic_id" name="clinic_id" required>
                                                <option value="">-- اختر العيادة --</option>
                                                {% for clinic in clinics %}
                                                <option value="{{ clinic.id }}">{{ clinic.name }} ({{ clinic.area_name }} - {{ clinic.branch_name }})</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-calendar"></i>
                                            <input type="month" class="form-control" id="dispense_month" name="dispense_month" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- بيانات الدواء -->
                                <div class="section-card drug-section">
                                    <div class="section-header">
                                        <h6 class="mb-0 fw-bold"><i class="mdi mdi-pill me-2"></i>بيانات الدواء</h6>
                                    </div>
                                    <div class="p-3">
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-tag-multiple"></i>
                                            <select class="form-select" id="category_id" required>
                                                <option value="">-- اختر التصنيف --</option>
                                                {% for category in categories %}
                                                <option value="{{ category.id }}">{{ category.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-pill"></i>
                                            <select class="form-select" id="drug_id" required disabled>
                                                <option value="">-- اختر التصنيف أولاً --</option>
                                            </select>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="icon-input mb-3">
                                                    <i class="mdi mdi-package-variant"></i>
                                                    <input type="number" step="0.01" min="0.01" class="form-control" id="quantity" placeholder="الكمية" required>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="icon-input mb-3">
                                                    <i class="mdi mdi-currency-usd"></i>
                                                    <input type="number" step="0.01" min="0.01" class="form-control" id="price" placeholder="السعر" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="icon-input mb-3">
                                                    <i class="mdi mdi-archive"></i>
                                                    <input type="number" min="1" class="form-control" id="cases_count" placeholder="عدد الحالات" required>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="icon-input mb-3">
                                                    <i class="mdi mdi-calculator"></i>
                                                    <input type="text" class="form-control" id="item_cost" placeholder="التكلفة" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-center">
                                            <button type="button" id="add-drug-btn" class="btn btn-success btn-lg">
                                                <i class="mdi mdi-plus me-2"></i>إضافة إلى القائمة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- قائمة الأدوية المضافة -->
                            <div class="col-lg-8">
                                <div class="section-card list-section">
                                    <div class="section-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 fw-bold"><i class="mdi mdi-format-list-bulleted me-2"></i>قائمة الأدوية المضافة</h6>
                                            <span class="badge bg-white text-dark" id="items-count">0 عنصر</span>
                                        </div>
                                    </div>
                                    <div class="p-3">
                                        <div id="items-list" class="table-responsive">
                                            <table class="table table-hover mb-0">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>الدواء</th>
                                                        <th>التصنيف</th>
                                                        <th>الكمية</th>
                                                        <th>السعر</th>
                                                        <th>عدد الحالات</th>
                                                        <th>التكلفة</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="items-table-body">
                                                    <tr id="no-items-row">
                                                        <td colspan="8" class="text-center text-muted py-5">
                                                            <i class="mdi mdi-package-variant-closed" style="font-size: 3rem; opacity: 0.3;"></i>
                                                            <br>لا توجد أدوية مضافة بعد
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- إجمالي التكلفة -->
                                        <div class="row mt-4">
                                            <div class="col-md-6 offset-md-6">
                                                <div class="total-card">
                                                    <h5 class="mb-0 fw-bold">
                                                        <i class="mdi mdi-cash-multiple me-2"></i>
                                                        إجمالي التكلفة: <span id="total-cost">0.00</span> جنيه
                                                    </h5>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- أزرار الحفظ -->
                                        <div class="text-center mt-4">
                                            <button type="button" id="save-dispensed-btn" class="btn btn-primary btn-lg me-3" disabled>
                                                <i class="mdi mdi-content-save me-2"></i>حفظ المنصرف
                                            </button>
                                            <button type="button" class="btn btn-secondary btn-lg" onclick="clearList()">
                                                <i class="mdi mdi-refresh me-2"></i>مسح القائمة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- حقول مخفية للبيانات -->
                        <input type="hidden" id="items_data" name="items_data" value="">
                        <input type="hidden" id="total_cost" name="total_cost" value="0">
                    </form>
                </div>
            </div>

            <!-- سجل الصرف الأخير -->
            {% if dispensed_items %}
            <div class="history-card">
                <div class="history-header">
                    <h4 class="mb-0 fw-bold">
                        <i class="mdi mdi-history me-3"></i>سجل الصرف الأخير
                    </h4>
                </div>
                <div class="p-4">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>العيادة</th>
                                    <th>الدواء</th>
                                    <th>التصنيف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>عدد الحالات</th>
                                    <th>التكلفة</th>
                                    <th>تاريخ الصرف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in dispensed_items %}
                                <tr>
                                    <td><span class="badge bg-primary">{{ loop.index }}</span></td>
                                    <td>{{ item.clinic_name }}</td>
                                    <td><strong>{{ item.drug_name }}</strong></td>
                                    <td><span class="badge bg-info">{{ item.category_name }}</span></td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.price }} ج.م</td>
                                    <td>{{ item.cases_count }}</td>
                                    <td><strong class="text-success">{{ item.total_cost }} ج.م</strong></td>
                                    <td>{{ item.dispense_month }}</td>
                                    <td>
                                        <a href="/dispense/{{ item.id }}/edit" class="btn btn-sm btn-warning me-1" title="تعديل">
                                            <i class="mdi mdi-pencil"></i>
                                        </a>
                                        <form method="POST" action="/dispense/{{ item.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا السجل؟');">
                                            <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                <i class="mdi mdi-delete"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // متغيرات عامة
    var itemsList = [];
    var itemCounter = 0;
    var selectedCategoryId = null;
    var selectedCategoryName = null;
    var editingIndex = -1;

    $(document).ready(function() {
        // تعيين التاريخ الحالي كقيمة افتراضية لشهر الصرف
        var today = new Date();
        $('#dispense_month').val(formatMonthInput(today));

        // تحديث قائمة الأدوية عند اختيار التصنيف
        $('#category_id').change(function() {
            var categoryId = $(this).val();
            var categoryName = $(this).find('option:selected').text();
            var drugSelect = $('#drug_id');

            if (categoryId) {
                // حفظ التصنيف المختار
                selectedCategoryId = categoryId;
                selectedCategoryName = categoryName;

                // تعطيل قائمة التصنيف بعد الاختيار
                $(this).prop('disabled', true);

                drugSelect.prop('disabled', false);
                drugSelect.html('<option value="">جاري التحميل...</option>');

                $.ajax({
                    url: '/api/drugs_by_category/' + categoryId,
                    type: 'GET',
                    success: function(data) {
                        drugSelect.html('<option value="">-- اختر الدواء --</option>');
                        $.each(data.drugs, function(index, drug) {
                            drugSelect.append('<option value="' + drug.id + '">' + drug.name + '</option>');
                        });
                    },
                    error: function() {
                        drugSelect.html('<option value="">خطأ في تحميل الأدوية</option>');
                    }
                });
            } else {
                selectedCategoryId = null;
                selectedCategoryName = null;
                drugSelect.prop('disabled', true);
                drugSelect.html('<option value="">-- اختر التصنيف أولاً --</option>');
            }
        });

        // حساب التكلفة عند تغيير الكمية أو السعر
        $('#quantity, #price').on('input', function() {
            calculateItemCost();
        });

        // إضافة عنصر إلى القائمة
        $('#add-drug-btn').on('click', function() {
            addItemToList();
        });

        // حفظ المنصرف
        $('#save-dispensed-btn').on('click', function() {
            saveDispensed();
        });

        // حذف عنصر من القائمة
        $(document).on('click', '.remove-item', function() {
            var index = $(this).data('index');
            removeItemFromList(index);
        });

        // تعديل عنصر في القائمة
        $(document).on('click', '.edit-item', function() {
            var index = $(this).data('index');
            editItemInList(index);
        });
    });

    // دالة لحساب تكلفة العنصر
    function calculateItemCost() {
        var quantity = parseFloat($('#quantity').val()) || 0;
        var price = parseFloat($('#price').val()) || 0;
        var cost = quantity * price;
        $('#item_cost').val(cost.toFixed(2));
    }

    // دالة لإضافة عنصر إلى القائمة
    function addItemToList() {
        var drugId = $('#drug_id').val();
        var drugName = $('#drug_id option:selected').text();
        var quantity = parseFloat($('#quantity').val());
        var price = parseFloat($('#price').val());
        var casesCount = parseInt($('#cases_count').val());

        // التحقق من البيانات
        if (!selectedCategoryId || !drugId || !quantity || !price || !casesCount) {
            alert('يرجى إدخال جميع البيانات المطلوبة');
            return;
        }

        var cost = quantity * price;

        // إضافة أو تحديث العنصر
        var item = {
            drug_id: drugId,
            drug_name: drugName,
            category_name: selectedCategoryName,
            quantity: quantity,
            price: price,
            cases_count: casesCount,
            cost: cost
        };

        if (editingIndex >= 0) {
            // تحديث عنصر موجود
            itemsList[editingIndex] = item;
            editingIndex = -1;
            $('#add-drug-btn').html('<i class="mdi mdi-plus me-2"></i>إضافة إلى القائمة');
        } else {
            // إضافة عنصر جديد
            itemsList.push(item);
        }

        updateItemsTable();
        clearDrugForm();
        updateSaveButton();
    }

    // دالة لحذف عنصر من القائمة
    function removeItemFromList(index) {
        if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
            itemsList.splice(index, 1);
            updateItemsTable();
            updateSaveButton();

            // إلغاء وضع التعديل إذا كان العنصر المحذوف قيد التعديل
            if (editingIndex === index) {
                cancelEdit();
            } else if (editingIndex > index) {
                editingIndex--;
            }
        }
    }

    // دالة لتعديل عنصر في القائمة
    function editItemInList(index) {
        var item = itemsList[index];

        // ملء النموذج ببيانات العنصر
        $('#drug_id').val(item.drug_id);
        $('#quantity').val(item.quantity);
        $('#price').val(item.price);
        $('#cases_count').val(item.cases_count);
        $('#item_cost').val(item.cost.toFixed(2));

        // تعيين وضع التعديل
        editingIndex = index;
        $('#add-drug-btn').html('<i class="mdi mdi-check me-2"></i>تحديث العنصر');

        // إضافة زر إلغاء
        if ($('#cancel-edit-btn').length === 0) {
            $('#add-drug-btn').after('<button type="button" id="cancel-edit-btn" class="btn btn-secondary ms-2" onclick="cancelEdit()"><i class="mdi mdi-close me-2"></i>إلغاء</button>');
        }
    }

    // دالة لإلغاء التعديل
    function cancelEdit() {
        editingIndex = -1;
        $('#add-drug-btn').html('<i class="mdi mdi-plus me-2"></i>إضافة إلى القائمة');
        $('#cancel-edit-btn').remove();
        clearDrugForm();
    }

    // دالة لتحديث جدول العناصر
    function updateItemsTable() {
        var tbody = $('#items-table-body');
        tbody.empty();

        if (itemsList.length === 0) {
            tbody.append('<tr id="no-items-row"><td colspan="8" class="text-center text-muted py-5"><i class="mdi mdi-package-variant-closed" style="font-size: 3rem; opacity: 0.3;"></i><br>لا توجد أدوية مضافة بعد</td></tr>');
            $('#total-cost').text('0.00');
            $('#items-count').text('0 عنصر');
        } else {
            var totalCost = 0;
            $.each(itemsList, function(index, item) {
                totalCost += item.cost;
                var editingClass = (editingIndex === index) ? ' table-warning' : '';
                var row = '<tr class="' + editingClass + '">' +
                    '<td><span class="badge bg-primary">' + (index + 1) + '</span></td>' +
                    '<td><strong>' + item.drug_name + '</strong></td>' +
                    '<td><span class="badge bg-info">' + item.category_name + '</span></td>' +
                    '<td>' + item.quantity + '</td>' +
                    '<td>' + item.price + ' ج.م</td>' +
                    '<td>' + item.cases_count + '</td>' +
                    '<td><strong class="text-success">' + item.cost.toFixed(2) + ' ج.م</strong></td>' +
                    '<td>' +
                        '<button type="button" class="btn btn-sm btn-warning me-1 edit-item" data-index="' + index + '" title="تعديل"><i class="mdi mdi-pencil"></i></button>' +
                        '<button type="button" class="btn btn-sm btn-danger remove-item" data-index="' + index + '" title="حذف"><i class="mdi mdi-delete"></i></button>' +
                    '</td>' +
                    '</tr>';
                tbody.append(row);
            });
            $('#total-cost').text(totalCost.toFixed(2));
            $('#items-count').text(itemsList.length + ' عنصر');
        }
    }

    // دالة لمسح حقول الدواء فقط (بدون التصنيف)
    function clearDrugForm() {
        $('#drug_id').val('');
        $('#quantity').val('');
        $('#price').val('');
        $('#cases_count').val('');
        $('#item_cost').val('');
    }

    // دالة لمسح النموذج بالكامل
    function clearForm() {
        $('#category_id').val('').prop('disabled', false);
        $('#drug_id').html('<option value="">-- اختر التصنيف أولاً --</option>').prop('disabled', true);
        $('#quantity').val('');
        $('#price').val('');
        $('#cases_count').val('');
        $('#item_cost').val('');
        selectedCategoryId = null;
        selectedCategoryName = null;
    }

    // دالة لمسح القائمة
    function clearList() {
        if (itemsList.length > 0 && confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
            itemsList = [];
            updateItemsTable();
            updateSaveButton();
            cancelEdit();
            clearForm(); // مسح النموذج بالكامل وإعادة تفعيل التصنيف
        }
    }

    // دالة لتحديث زر الحفظ
    function updateSaveButton() {
        var saveBtn = $('#save-dispensed-btn');
        if (itemsList.length > 0) {
            saveBtn.prop('disabled', false);
        } else {
            saveBtn.prop('disabled', true);
        }
    }

    // دالة لحفظ المنصرف
    function saveDispensed() {
        var clinicId = $('#clinic_id').val();
        var dispenseMonth = $('#dispense_month').val();

        if (!clinicId || !dispenseMonth) {
            alert('يرجى اختيار العيادة وشهر الصرف');
            return;
        }

        if (itemsList.length === 0) {
            alert('يرجى إضافة عنصر واحد على الأقل إلى القائمة');
            return;
        }

        // تحديث البيانات المخفية
        $('#items_data').val(JSON.stringify(itemsList));
        var totalCost = itemsList.reduce((sum, item) => sum + item.cost, 0);
        $('#total_cost').val(totalCost.toFixed(2));

        // عرض مؤشر التحميل
        var saveBtn = $('#save-dispensed-btn');
        var originalText = saveBtn.html();
        saveBtn.html('<i class="mdi mdi-loading mdi-spin me-1"></i>جاري الحفظ...');
        saveBtn.prop('disabled', true);

        // إرسال البيانات
        $.ajax({
            url: '/dispense_new',
            type: 'POST',
            data: {
                clinic_id: clinicId,
                dispense_month: dispenseMonth,
                items_data: JSON.stringify(itemsList),
                total_cost: totalCost.toFixed(2)
            },
            success: function(response) {
                alert('تم حفظ منصرف الأدوية بنجاح');
                // مسح القائمة وإعادة تفعيل التصنيف
                itemsList = [];
                updateItemsTable();
                updateSaveButton();
                cancelEdit();
                clearForm();
                location.reload();
            },
            error: function() {
                alert('حدث خطأ أثناء حفظ البيانات');
                saveBtn.html(originalText);
                saveBtn.prop('disabled', false);
            }
        });
    }
</script>
{% endblock %}
