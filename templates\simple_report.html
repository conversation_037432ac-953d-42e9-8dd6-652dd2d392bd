{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-info" onclick="showPrintOptions('pdf')" title="حفظ كـ PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>حفظ PDF
                    </button>
                    <button class="btn btn-primary" onclick="showPrintOptions('print')" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h4 class="mb-0">
                <i class="mdi mdi-file-document me-2"></i>{{ title }}
            </h4>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='images/Ahmed.png') }}" alt="الهيئة العامة للتأمين الصحي" style="max-width: 150px; height: auto;">
            </div>

            {% if message %}
                <div class="alert alert-info">
                    <i class="mdi mdi-information-outline me-2"></i>{{ message }}
                </div>
            {% else %}
                <!-- معلومات التقرير -->
                <div class="report-header mb-4">
                    <h2 class="text-center mb-3">
                        <i class="mdi mdi-file-document-outline me-2"></i>{{ title }}
                    </h2>
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <i class="mdi mdi-calendar text-primary" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">التاريخ</h6>
                                    <p class="mb-0">{{ date }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <i class="mdi mdi-map-marker text-success" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">النطاق</h6>
                                    <p class="mb-0">{{ scope_name }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <i class="mdi mdi-cash-multiple text-warning" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">إجمالي التكلفة</h6>
                                    <p class="mb-0">{{ "{:,.2f}".format(total_cost) }} ج.م</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول البيانات -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="mdi mdi-pill me-2"></i>تفاصيل المنصرف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead style="background: linear-gradient(135deg, #6c757d, #495057); color: white;">
                                    <tr>
                                        <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 200px;">
                                            <i class="mdi mdi-pill me-2" style="color: #f39c12;"></i>
                                            <span>اسم الدواء</span>
                                        </th>
                                        <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 100px;">
                                            <i class="mdi mdi-package-variant me-2" style="color: #2ecc71;"></i>
                                            <span>الكمية</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(الوحدات)</small>
                                        </th>
                                        <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 100px;">
                                            <i class="mdi mdi-archive me-2" style="color: #e74c3c;"></i>
                                            <span>عدد العلب</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(الصناديق)</small>
                                        </th>
                                        <th class="text-end" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px;">
                                            <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                            <span>التكلفة الإجمالية</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for drug in drugs %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="mdi mdi-pill text-primary me-2"></i>
                                                <span>{{ drug.drug_name }}</span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">{{ drug.total_quantity }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary">{{ drug.cases_count }}</span>
                                        </td>
                                        <td class="text-end">
                                            <strong>{{ "{:,.2f}".format(drug.total_cost) }}</strong>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th class="text-start">
                                            <i class="mdi mdi-calculator me-2"></i>الإجمالي العام
                                        </th>
                                        {% set total_quantity = 0 %}
                                        {% set total_cases = 0 %}
                                        {% for drug in drugs %}
                                            {% set total_quantity = total_quantity + (drug.total_quantity or 0) %}
                                            {% set total_cases = total_cases + (drug.cases_count or 0) %}
                                        {% endfor %}
                                        <th class="text-center">{{ total_quantity }}</th>
                                        <th class="text-center">{{ total_cases }}</th>
                                        <th class="text-end">{{ "{:,.2f}".format(total_cost or 0) }} ج.م</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="mt-4 text-center">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- مربع حوار خيارات الطباعة -->
<div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">خيارات الطباعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">اتجاه الصفحة:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                        <label class="form-check-label" for="orientationLandscape">أفقي (Landscape)</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                        <label class="form-check-label" for="orientationPortrait">رأسي (Portrait)</label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">حجم الخط:</label>
                    <select class="form-select" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
            </div>
        </div>
    </div>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    var printAction = 'print';
    var printModal;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل التقرير المبسط بنجاح');
    });

    function showPrintOptions(action) {
        printAction = action;
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }
        printModal.show();
    }

    function performPrint() {
        try {
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            var styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; }';

            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };
            styleElement.innerHTML += '.table { font-size: ' + fontSizeMap[fontSize] + '; }';

            document.head.appendChild(styleElement);
            window.print();

            setTimeout(function() {
                document.head.removeChild(styleElement);
            }, 1000);
        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");
        }
    }

    function exportToExcel() {
        var table = document.querySelector('.table');
        if (!table) return;

        var data = [];
        var headers = [];
        table.querySelectorAll('thead th').forEach(function(th) {
            headers.push(th.innerText.replace(/\n/g, ' ').trim());
        });
        data.push(headers);

        table.querySelectorAll('tbody tr').forEach(function(tr) {
            var row = [];
            tr.querySelectorAll('td').forEach(function(td) {
                row.push(td.innerText.replace(/\n/g, ' ').trim());
            });
            data.push(row);
        });

        var footerRow = [];
        table.querySelectorAll('tfoot tr').forEach(function(tr) {
            tr.querySelectorAll('th, td').forEach(function(cell) {
                footerRow.push(cell.innerText.replace(/\n/g, ' ').trim());
            });
        });
        if (footerRow.length > 0) {
            data.push(footerRow);
        }

        var ws = XLSX.utils.aoa_to_sheet(data);
        var wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "التقرير المبسط");

        var fileName = "التقرير_المبسط_" + new Date().toISOString().slice(0, 10) + ".xlsx";
        XLSX.writeFile(wb, fileName);
    }
</script>

<style>
    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    @media print {
        .btn, .no-print {
            display: none !important;
        }

        .card {
            border: 1px solid #ddd !important;
            box-shadow: none !important;
            break-inside: avoid;
        }

        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }

        .table {
            font-size: 12px;
        }

        .container-fluid {
            padding: 0;
        }

        /* إضافة اللوجو في بداية صفحة الطباعة */
        body::before {
            content: '';
            display: block;
            background-image: url("{{ url_for('static', filename='images/logo.png') }}");
            background-repeat: no-repeat;
            background-position: center top;
            background-size: 150px auto;
            height: 100px;
            margin-bottom: 20px;
        }

        /* إضافة حقوق الملكية في نهاية صفحة الطباعة */
        body::after {
            content: 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}';
            display: block;
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
    }
</style>
{% endblock %}
