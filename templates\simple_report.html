{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-secondary text-white">
            <h4 class="mb-0">
                <i class="mdi mdi-file-document me-2"></i>{{ title }}
            </h4>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='images/Ahmed.png') }}" alt="الهيئة العامة للتأمين الصحي" style="max-width: 150px; height: auto;">
            </div>

            {% if message %}
                <div class="alert alert-info">
                    <i class="mdi mdi-information-outline me-2"></i>{{ message }}
                </div>
            {% else %}
                <!-- معلومات التقرير -->
                <div class="report-header mb-4">
                    <h2 class="text-center mb-3">
                        <i class="mdi mdi-file-document-outline me-2"></i>{{ title }}
                    </h2>
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <i class="mdi mdi-calendar text-primary" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">التاريخ</h6>
                                    <p class="mb-0">{{ date }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <i class="mdi mdi-map-marker text-success" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">النطاق</h6>
                                    <p class="mb-0">{{ scope_name }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <i class="mdi mdi-cash-multiple text-warning" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">إجمالي التكلفة</h6>
                                    <p class="mb-0">{{ "{:,.2f}".format(total_cost) }} ج.م</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول البيانات -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="mdi mdi-pill me-2"></i>تفاصيل المنصرف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead style="background: linear-gradient(135deg, #6c757d, #495057); color: white;">
                                    <tr>
                                        <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 200px;">
                                            <i class="mdi mdi-pill me-2" style="color: #f39c12;"></i>
                                            <span>اسم الدواء</span>
                                        </th>
                                        <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 100px;">
                                            <i class="mdi mdi-package-variant me-2" style="color: #2ecc71;"></i>
                                            <span>الكمية</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(الوحدات)</small>
                                        </th>
                                        <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 100px;">
                                            <i class="mdi mdi-archive me-2" style="color: #e74c3c;"></i>
                                            <span>عدد العلب</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(الصناديق)</small>
                                        </th>
                                        <th class="text-end" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px;">
                                            <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                            <span>التكلفة الإجمالية</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for drug in drugs %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="mdi mdi-pill text-primary me-2"></i>
                                                <span>{{ drug.drug_name }}</span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">{{ drug.total_quantity }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary">{{ drug.cases_count }}</span>
                                        </td>
                                        <td class="text-end">
                                            <strong>{{ "{:,.2f}".format(drug.total_cost) }}</strong>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th class="text-start">
                                            <i class="mdi mdi-calculator me-2"></i>الإجمالي العام
                                        </th>
                                        <th class="text-center">{{ drugs|sum(attribute='total_quantity') }}</th>
                                        <th class="text-center">{{ drugs|sum(attribute='cases_count') }}</th>
                                        <th class="text-end">{{ "{:,.2f}".format(total_cost) }} ج.م</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="mt-4 text-center">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}
