{% extends 'base.html' %}

{% block title %}تعديل سجل المنصرف{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: fadeInUp 0.5s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 20px 20px 0 0 !important;
        border: none !important;
        padding: 1.5rem !important;
    }

    .form-label {
        color: #4e73df;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .form-control {
        border-radius: 10px;
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .form-control-static {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        border: 1px solid #e9ecef;
        font-weight: 500;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }

    .card-cost {
        background: linear-gradient(135deg, #20c997 0%, #0ca678 100%);
        border-radius: 15px;
        padding: 1.5rem;
        color: white;
    }

    .card-cost h5 {
        opacity: 0.9;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .card-cost h4 {
        font-size: 1.5rem;
        font-weight: 700;
    }

    .input-group {
        margin-bottom: 1.5rem;
    }

    .input-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        z-index: 10;
    }

    .highlight-change {
        animation: highlightChange 0.3s ease;
    }

    @keyframes highlightChange {
        0% { background-color: rgba(102, 126, 234, 0.2); }
        100% { background-color: transparent; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="main-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 text-white">
                            <i class="mdi mdi-pencil-box-multiple me-2"></i>تعديل سجل المنصرف
                        </h4>
                        <a href="{{ url_for('dispense_new') }}" class="btn btn-light btn-sm">
                            <i class="mdi mdi-arrow-left me-1"></i>العودة
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- معلومات الدواء -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العيادة</label>
                                <div class="form-control-static">
                                    <i class="mdi mdi-hospital-building me-2 text-primary"></i>
                                    {{ record.clinic_name }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الدواء</label>
                                <div class="form-control-static">
                                    <i class="mdi mdi-pill me-2 text-primary"></i>
                                    {{ record.drug_name }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التصنيف</label>
                                <div class="form-control-static">
                                    <i class="mdi mdi-tag me-2 text-primary"></i>
                                    <span class="badge bg-info">{{ record.category_name }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الصرف</label>
                                <div class="form-control-static">
                                    <i class="mdi mdi-calendar me-2 text-primary"></i>
                                    {{ record.dispense_month }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج التعديل -->
                    <form method="POST" action="{{ url_for('edit_dispensed_record', dispensed_id=record.id) }}" class="mt-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group position-relative mb-3">
                                    <label for="quantity" class="form-label">
                                        <i class="mdi mdi-package-variant me-1"></i>الكمية
                                    </label>
                                    <input type="number" step="0.01" min="0.01" 
                                           class="form-control" id="quantity" name="quantity" 
                                           value="{{ record.quantity }}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group position-relative mb-3">
                                    <label for="price" class="form-label">
                                        <i class="mdi mdi-currency-usd me-1"></i>السعر
                                    </label>
                                    <input type="number" step="0.01" min="0.01" 
                                           class="form-control" id="price" name="price" 
                                           value="{{ record.price }}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group position-relative mb-3">
                                    <label for="cases_count" class="form-label">
                                        <i class="mdi mdi-account-group me-1"></i>عدد الحالات
                                    </label>
                                    <input type="number" min="1" 
                                           class="form-control" id="cases_count" name="cases_count" 
                                           value="{{ record.cases_count }}" required>
                                </div>
                            </div>
                        </div>

                        <!-- بطاقة التكلفة -->
                        <div class="card card-cost mt-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="mdi mdi-cash-multiple me-2"></i>
                                    التكلفة الإجمالية:
                                </h5>
                                <h4 class="mb-0" id="total_cost_display">{{ record.quantity * record.price }}</h4>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg me-2">
                                <i class="mdi mdi-content-save me-1"></i>حفظ التعديلات
                            </button>
                            <a href="{{ url_for('dispense_new') }}" class="btn btn-secondary btn-lg">
                                <i class="mdi mdi-close me-1"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تحديث التكلفة الإجمالية عند تغيير الكمية أو السعر
        $('#quantity, #price').on('input', function() {
            updateTotalCost();
        });

        // دالة لحساب التكلفة الإجمالية
        function updateTotalCost() {
            var quantity = parseFloat($('#quantity').val()) || 0;
            var price = parseFloat($('#price').val()) || 0;
            var totalCost = quantity * price;

            // عرض التكلفة الإجمالية بتنسيق مناسب
            var totalCostDisplay = $('#total_cost_display');
            totalCostDisplay.text(totalCost.toFixed(2));
            
            // إضافة تأثير التحديث
            totalCostDisplay.addClass('highlight-change');
            setTimeout(function() {
                totalCostDisplay.removeClass('highlight-change');
            }, 300);
        }

        // تأثيرات بصرية للنماذج
        $('.form-control').on('focus', function() {
            $(this).addClass('border-primary');
        }).on('blur', function() {
            $(this).removeClass('border-primary');
        });
    });
</script>
{% endblock %}
