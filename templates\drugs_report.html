<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الأدوية - {{ report_title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .report-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .report-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
            position: relative;
            z-index: 1;
        }
        .report-header .subtitle {
            margin-top: 10px;
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        .logo-container {
            position: absolute;
            top: 20px;
            right: 30px;
            z-index: 2;
        }
        .logo-container img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .report-info {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .info-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        .table-container {
            padding: 30px;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .table thead th {
            border: none;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            transition: all 0.3s ease;
        }
        .table tbody td {
            padding: 12px 15px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        .btn-group {
            margin-bottom: 20px;
        }
        .btn-custom {
            border-radius: 25px;
            padding: 10px 20px;
            font-weight: bold;
            margin: 0 5px;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .total-row {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            font-weight: bold;
        }
        .total-row td {
            border: none;
            padding: 15px;
        }
        @media print {
            body { background: white; }
            .btn-group { display: none; }
            .report-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Header -->
        <div class="report-header">
            <div class="logo-container">
                <img src="{{ url_for('static', filename='images/Ahmed.png') }}" alt="Logo">
            </div>
            <h1><i class="mdi mdi-pill me-3"></i>تقرير الأدوية</h1>
            <div class="subtitle">{{ report_title }}</div>
        </div>

        <!-- Report Info -->
        <div class="report-info">
            <div class="row">
                <div class="col-md-4">
                    <div class="info-card">
                        <h6><i class="mdi mdi-information me-2 text-primary"></i>معلومات التقرير</h6>
                        <p class="mb-1"><strong>النطاق:</strong> {{ scope_text }}</p>
                        <p class="mb-1"><strong>السنة:</strong> {{ year }}</p>
                        <p class="mb-0"><strong>نوع التحليل:</strong> تحليل مفصل</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card">
                        <h6><i class="mdi mdi-chart-line me-2 text-success"></i>إحصائيات سريعة</h6>
                        <p class="mb-1"><strong>إجمالي الأدوية:</strong> {{ total_items }} صنف</p>
                        <p class="mb-1"><strong>إجمالي الكمية:</strong> {{ "{:,.0f}".format(total_quantity) }} وحدة</p>
                        <p class="mb-0"><strong>إجمالي التكلفة:</strong> {{ "{:,.2f}".format(total_cost) }} ج.م</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card">
                        <h6><i class="mdi mdi-clock me-2 text-warning"></i>تاريخ الإنشاء</h6>
                        <p class="mb-1"><strong>التاريخ:</strong> {{ now_date.strftime('%d/%m/%Y') }}</p>
                        <p class="mb-1"><strong>الوقت:</strong> {{ now_date.strftime('%H:%M') }}</p>
                        <p class="mb-0"><strong>المنطقة الزمنية:</strong> توقيت القاهرة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="table-container">
            <div class="btn-group d-print-none">
                <button onclick="window.print()" class="btn btn-primary btn-custom">
                    <i class="mdi mdi-printer me-2"></i>طباعة
                </button>
                <button onclick="window.history.back()" class="btn btn-secondary btn-custom">
                    <i class="mdi mdi-arrow-right me-2"></i>رجوع
                </button>
                <button onclick="exportToExcel()" class="btn btn-success btn-custom">
                    <i class="mdi mdi-file-excel me-2"></i>تصدير Excel
                </button>
                <button onclick="exportToPDF()" class="btn btn-danger btn-custom" style="background: #dc3545; border: none;">
                    <i class="mdi mdi-file-pdf-box me-2"></i>تصدير PDF
                </button>
            </div>

            <!-- Drugs Table -->
            <div class="card">
                <div class="card-header" style="background: linear-gradient(135deg, #16a085, #1abc9c); color: white;">
                    <h5 class="mb-0">
                        <i class="mdi mdi-pill me-2"></i>
                        التحليل المفصل - {{ scope_text }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead style="background: linear-gradient(135deg, #16a085, #1abc9c); color: white;">
                                <tr>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                        <i class="mdi mdi-pill me-2" style="color: #f39c12;"></i>
                                        <span>اسم الدواء</span>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 100px;">
                                        <i class="mdi mdi-package-variant me-2" style="color: #2ecc71;"></i>
                                        <span>الكمية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(الوحدات)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 100px;">
                                        <i class="mdi mdi-archive me-2" style="color: #e74c3c;"></i>
                                        <span>عدد العلب</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(الصناديق)</small>
                                    </th>
                                    <th class="text-end" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e74c3c; min-width: 120px;">
                                        <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                        <span>التكلفة الإجمالية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in drugs_data %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="mdi mdi-pill text-primary me-2"></i>
                                            <span>{{ item.name }}</span>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ "{:,.0f}".format(item.quantity) }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-warning">{{ "{:,.0f}".format(item.cases) }}</span>
                                    </td>
                                    <td class="text-end">
                                        <strong>{{ "{:,.2f}".format(item.cost) }}</strong>
                                    </td>
                                </tr>
                                {% endfor %}
                                <tr class="total-row">
                                    <td><i class="mdi mdi-calculator me-2"></i><strong>إجمالي {{ year }}</strong></td>
                                    <td class="text-center"><strong>{{ "{:,.0f}".format(total_quantity) }}</strong></td>
                                    <td class="text-center"><strong>{{ "{:,.0f}".format(total_cases) }}</strong></td>
                                    <td class="text-end"><strong>{{ "{:,.2f}".format(total_cost) }} ج.م</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center py-3" style="background: #f8f9fa; border-top: 1px solid #dee2e6;">
            <small class="text-muted">
                © {{ now_date.year }} - ك/ أحمد على أحمد (أحمد كوكب) - جميع الحقوق محفوظة
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        function exportToExcel() {
            const table = document.querySelector('table');
            const wb = XLSX.utils.table_to_book(table, {sheet: "تقرير الأدوية"});
            XLSX.writeFile(wb, `تقرير_الأدوية_${new Date().getFullYear()}.xlsx`);
        }

        function exportToPDF() {
            window.print();
        }
    </script>
</body>
</html>