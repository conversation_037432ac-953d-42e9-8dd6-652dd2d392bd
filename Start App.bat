@echo off
chcp 65001 > nul
cls
echo ============================================
echo    تشغيل تطبيق إدارة منصرف الأدوية
echo ============================================
echo.

:: الانتقال إلى مجلد التطبيق
cd /d "%~dp0"

:: التأكد من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على جهازك
    echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: التأكد من تثبيت المكتبات المطلوبة
echo جاري التحقق من المكتبات المطلوبة...
python -m pip install -r requirements.txt --quiet

:: تنظيف الشاشة
cls
echo ============================================
echo    تشغيل تطبيق إدارة منصرف الأدوية
echo ============================================
echo.

:: إزالة عمليات python القديمة على المنفذ 8080
for /f "tokens=5" %%a in ('netstat -aon ^| find ":8080" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1

echo جاري تشغيل التطبيق...
echo سيتم فتح المتصفح تلقائياً خلال لحظات...
echo.

:: تشغيل التطبيق
start /min cmd /c "python run_app.py"

:: انتظار حتى يكون المنفذ جاهزاً
:wait_loop
timeout /t 1 /nobreak > nul
netstat -an | find ":8080" | find "LISTENING" > nul
if %errorlevel% neq 0 goto wait_loop

:: فتح المتصفح
start http://localhost:8080

echo.
echo تم تشغيل التطبيق بنجاح!
echo إذا لم يفتح المتصفح تلقائياً، يرجى فتح الرابط التالي:
echo http://localhost:8080
echo.
echo لإيقاف التطبيق، أغلق هذه النافذة.
echo ============================================
pause > nul
