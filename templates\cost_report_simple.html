{% extends "base.html" %}

{% block title %}تقرير التكلفة - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); min-height: 100vh;">
    <div class="row justify-content-center">
        <div class="col-12">
            <!-- Header Card -->
            <div class="card shadow-lg mb-4" style="border: none; border-radius: 20px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
                <div class="card-header text-white text-center py-4" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border-radius: 20px 20px 0 0; border: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div></div>
                        <div class="text-center">
                            <i class="mdi mdi-currency-usd" style="font-size: 3rem; margin-bottom: 10px;"></i>
                            <h2 class="mb-0 fw-bold">تقرير التكلفة</h2>
                            <p class="mb-0 opacity-75">تحليل شامل للتكاليف والنفقات</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button onclick="window.print()" class="btn btn-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-printer me-1"></i>طباعة
                            </button>
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><strong>تاريخ التقرير:</strong> {{ now.strftime('%Y-%m-%d %H:%M') if now else 'غير محدد' }}</h6>
                            <h6><strong>السنة:</strong> {{ year }}</h6>
                            <h6><strong>نوع التحليل:</strong> {{ 'شهري' if analysis_type == 'monthly' else 'إجمالي' }}</h6>
                        </div>
                        <div class="col-md-6 text-end">
                            {% if cost_data %}
                            {% set total_cost = 0 %}
                            {% set total_dispensed = 0 %}
                            {% for item in cost_data %}
                                {% set total_cost = total_cost + (item.total_cost or 0) %}
                                {% set total_dispensed = total_dispensed + (item.total_dispensed or 0) %}
                            {% endfor %}
                            <h6><strong>إجمالي التكلفة:</strong> {{ "{:,.2f}".format(total_cost) }} ج.م</h6>
                            <h6><strong>إجمالي المنصرف:</strong> {{ total_dispensed }}</h6>
                            {% else %}
                            <h6><strong>إجمالي التكلفة:</strong> 0.00 ج.م</h6>
                            <h6><strong>إجمالي المنصرف:</strong> 0</h6>
                            {% endif %}
                        </div>
                    </div>

                    <!-- جدول التكلفة -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="mdi mdi-chart-line me-2"></i>تفاصيل التكلفة
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if cost_data %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-success">
                                        <tr>
                                            <th>#</th>
                                            {% if analysis_type == 'monthly' %}
                                            <th>الشهر</th>
                                            {% else %}
                                            <th>الفرع</th>
                                            {% endif %}
                                            <th>التكلفة الإجمالية</th>
                                            <th>عدد المنصرف</th>
                                            {% if analysis_type != 'monthly' %}
                                            <th>الأدوية المختلفة</th>
                                            {% endif %}
                                            <th>متوسط التكلفة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in cost_data %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>
                                                {% if analysis_type == 'monthly' %}
                                                    <strong>{{ item.month_year }}</strong>
                                                {% else %}
                                                    <strong>{{ item.branch_name }}</strong>
                                                {% endif %}
                                            </td>
                                            <td>{{ "{:,.2f}".format(item.total_cost or 0) }} ج.م</td>
                                            <td>{{ item.total_dispensed or 0 }}</td>
                                            {% if analysis_type != 'monthly' %}
                                            <td>{{ item.unique_drugs or 0 }}</td>
                                            {% endif %}
                                            <td>{{ "{:,.2f}".format((item.total_cost or 0) / (item.total_dispensed or 1) if (item.total_dispensed or 0) > 0 else 0) }} ج.م</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-secondary">
                                        <tr>
                                            <th colspan="2">الإجمالي</th>
                                            {% set footer_total_cost = 0 %}
                                            {% set footer_total_dispensed = 0 %}
                                            {% for item in cost_data %}
                                                {% set footer_total_cost = footer_total_cost + (item.total_cost or 0) %}
                                                {% set footer_total_dispensed = footer_total_dispensed + (item.total_dispensed or 0) %}
                                            {% endfor %}
                                            <th>{{ "{:,.2f}".format(footer_total_cost) }} ج.م</th>
                                            <th>{{ footer_total_dispensed }}</th>
                                            {% if analysis_type != 'monthly' %}
                                            <th>-</th>
                                            {% endif %}
                                            <th>-</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-3" style="font-size: 2rem;"></i>
                                <strong>لا توجد بيانات تكلفة في الفترة المحددة.</strong>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    {% if cost_data and cost_data|length > 0 %}
                    <div class="row mt-4">
                        {% set stats_total = 0 %}
                        {% set max_cost = 0 %}
                        {% set min_cost = 999999999 %}
                        {% for item in cost_data %}
                            {% set item_cost = item.total_cost or 0 %}
                            {% set stats_total = stats_total + item_cost %}
                            {% if item_cost > max_cost %}
                                {% set max_cost = item_cost %}
                            {% endif %}
                            {% if item_cost < min_cost and item_cost > 0 %}
                                {% set min_cost = item_cost %}
                            {% endif %}
                        {% endfor %}
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">متوسط التكلفة</h6>
                                    <h4 class="text-primary">{{ "{:,.2f}".format(stats_total / cost_data|length) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">أعلى تكلفة</h6>
                                    <h4 class="text-success">{{ "{:,.2f}".format(max_cost) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">أقل تكلفة</h6>
                                    <h4 class="text-warning">{{ "{:,.2f}".format(min_cost if min_cost < 999999999 else 0) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// إضافة وظائف التفاعل إذا لزم الأمر
$(document).ready(function() {
    // يمكن إضافة المزيد من الوظائف هنا
});
</script>
{% endblock %}
