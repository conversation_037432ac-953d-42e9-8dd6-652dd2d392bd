{% extends "base.html" %}

{% block title %}إضافة مجموعة أدوية - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 20px 20px 0 0 !important;
        border: none !important;
        padding: 20px 30px;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 12px 15px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .btn-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
    }

    .drug-item {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border: 2px solid #e3e6f0;
        transition: all 0.3s ease;
    }

    .drug-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #667eea;
    }

    .icon-input {
        position: relative;
    }

    .icon-input i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        z-index: 10;
    }

    .icon-input .form-control,
    .icon-input .form-select {
        padding-left: 45px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="main-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0 fw-bold text-white">
                            <i class="mdi mdi-package-variant-plus me-3"></i>إضافة مجموعة أدوية
                        </h3>
                        <div>
                            <a href="{{ url_for('manage_drugs_new') }}" class="btn btn-light me-2">
                                <i class="mdi mdi-pill me-1"></i>إدارة الأدوية
                            </a>
                            <a href="{{ url_for('index') }}" class="btn btn-light">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- طريقة النص المتعدد -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="mdi mdi-text-box-multiple me-2"></i>إضافة سريعة بالنص
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="{{ url_for('add_drugs_batch') }}">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <div class="icon-input">
                                                    <i class="mdi mdi-tag-multiple"></i>
                                                    <select class="form-select" name="category_id" required>
                                                        <option value="">-- اختر التصنيف --</option>
                                                        {% for category in categories %}
                                                        <option value="{{ category.id }}">{{ category.name }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <div class="icon-input">
                                                    <i class="mdi mdi-calendar"></i>
                                                    <input type="date" class="form-control" name="expiry_date" placeholder="تاريخ انتهاء الصلاحية (اختياري)">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="drugs_list" class="form-label">قائمة الأدوية (كل دواء في سطر منفصل):</label>
                                            <textarea class="form-control" id="drugs_list" name="drugs_list" rows="8" required
                                                      placeholder="اكتب اسم كل دواء في سطر جديد&#10;مثال:&#10;باراسيتامول&#10;أموكسيسيلين&#10;كونكور&#10;أسبرين"></textarea>
                                        </div>
                                        <div class="text-center">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="mdi mdi-content-save me-2"></i>إضافة جميع الأدوية
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
