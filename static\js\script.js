// ملف JavaScript مخصص لتطبيق منصرف الأدوية

// تنفيذ الكود عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إخفاء رسائل التنبيه تلقائياً بعد 5 ثوانٍ
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});

// دالة لحساب التكلفة الإجمالية
function calculateTotalCost(quantity, price) {
    return parseFloat(quantity) * parseFloat(price);
}

// دالة لتحديث حقل التكلفة الإجمالية
function updateTotalCost() {
    var quantity = document.getElementById('quantity');
    var price = document.getElementById('price');
    var totalCost = document.getElementById('total_cost');
    
    if (quantity && price && totalCost) {
        var cost = calculateTotalCost(quantity.value, price.value);
        totalCost.value = cost.toFixed(2);
    }
}

// إضافة مستمعي الأحداث لحقول الكمية والسعر
document.addEventListener('DOMContentLoaded', function() {
    var quantity = document.getElementById('quantity');
    var price = document.getElementById('price');
    
    if (quantity && price) {
        quantity.addEventListener('input', updateTotalCost);
        price.addEventListener('input', updateTotalCost);
    }
});
