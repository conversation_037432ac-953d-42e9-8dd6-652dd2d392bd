// Utility function for formatting dates in English format (DD/MM/YYYY)
function formatDateEnglish(date) {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
}

// Utility function for formatting month and year (MM/YYYY)
function formatMonthYearEnglish(date) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${year}`;
}

// Utility function for formatting dates for input[type="month"] (YYYY-MM)
function formatMonthInput(date) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${year}-${month}`;
}
