{% extends 'base.html' %}

{% block title %}نبذة عن المصمم - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    .designer-container {
        padding: 2rem;
    }

    .designer-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .designer-images {
        position: relative;
        display: inline-block;
        margin-bottom: 2.5rem;  /* زيادة الهامش السفلي لإفساح المجال للصورة */
        padding-bottom: 10px;   /* إضافة تباعد إضافي في الأسفل */
    }

    .designer-image {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        object-fit: cover;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border: 5px solid #fff;
    }

    .designer-logo {
        position: absolute;
        bottom: -20px;  /* تحريك الصورة للأسفل بمقدار 20 بكسل */
        right: 0;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        border: 3px solid #fff;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
        background-color: #fff;
        z-index: 2;  /* للتأكد من أن الصورة تظهر فوق العناصر الأخرى */
    }

    .designer-name {
        font-size: 2rem;
        font-weight: bold;
        color: #4e73df;
        margin-bottom: 0.5rem;
    }

    .designer-title {
        font-size: 1.2rem;
        color: #5a5c69;
        margin-bottom: 1rem;
    }

    .designer-section {
        background-color: #f8f9fc;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
    }

    .section-title {
        color: #4e73df;
        font-weight: bold;
        margin-bottom: 1.5rem;
        border-bottom: 2px solid #e3e6f0;
        padding-bottom: 0.75rem;
    }

    .section-content {
        font-size: 1.1rem;
        line-height: 1.8;
    }

    .contact-info {
        background-color: rgba(78, 115, 223, 0.1);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .contact-icon {
        font-size: 1.5rem;
        color: #4e73df;
        margin-left: 1rem;
    }

    .skills-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .skill-badge {
        background-color: #4e73df;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.9rem;
    }

    .projects-list {
        margin-top: 1.5rem;
    }

    .project-item {
        margin-bottom: 1.5rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e3e6f0;
    }

    .project-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .project-title {
        font-weight: bold;
        font-size: 1.2rem;
        color: #4e73df;
        margin-bottom: 0.5rem;
    }

    .project-date {
        font-size: 0.9rem;
        color: #858796;
        margin-bottom: 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="mdi mdi-account-badge me-2"></i>نبذة عن المصمم
                    </h4>
                </div>
                <div class="card-body">
                    <div class="designer-container">
                        <!-- رأس الصفحة -->
                        <div class="designer-header">
                            <div class="designer-images">
                                <img src="{{ url_for('static', filename='images/Ahmed.jpeg') }}" alt="ك/أحمد علي أحمد" class="designer-image">
                                <img src="{{ url_for('static', filename='images/a.k.ico') }}" alt="أحمد كوكب" class="designer-logo">
                            </div>
                            <h2 class="designer-name">ك/أحمد علي أحمد</h2>
                            <p class="designer-title">أحمد كوكب - متخصص في تطوير نظم الويب والبرمجيات</p>
                        </div>

                        <!-- نبذة شخصية -->
                        <div class="designer-section">
                            <h3 class="section-title"><i class="mdi mdi-account me-2"></i>نبذة شخصية</h3>
                            <div class="section-content">
                                <p>ك/أحمد علي أحمد، المعروف باسم أحمد كوكب، متخصص في تطوير نظم الويب والبرمجيات مع خبرة واسعة في مجال تطوير التطبيقات وتصميم قواعد البيانات.</p>
                                <p>حاصل على بكالوريوس علوم قسم الكيمياء من جامعة سوهاج عام 1992، وقد اتجه بعد ذلك إلى مجال تكنولوجيا المعلومات والبرمجة، حيث طور مهاراته الذاتية في هذا المجال.</p>
                                <p>يتميز بقدرته على تطوير حلول برمجية مبتكرة وسهلة الاستخدام، مع التركيز على تلبية احتياجات المستخدمين وتحسين كفاءة العمليات. لديه شغف خاص بتطوير تطبيقات الويب وأنظمة إدارة المعلومات الصحية.</p>
                                <p>يجمع بين خلفيته العلمية في الكيمياء ومهاراته في البرمجة لتطوير حلول متكاملة تناسب القطاع الصحي والمؤسسات الطبية.</p>
                            </div>
                        </div>

                        <!-- المهارات -->
                        <div class="designer-section">
                            <h3 class="section-title"><i class="mdi mdi-code-tags me-2"></i>المهارات التقنية</h3>
                            <div class="section-content">
                                <p>يتمتع بمجموعة متنوعة من المهارات التقنية في مجال تطوير البرمجيات:</p>
                                <div class="skills-list">
                                    <span class="skill-badge">Python</span>
                                    <span class="skill-badge">Flask</span>
                                    <span class="skill-badge">JavaScript</span>
                                    <span class="skill-badge">HTML/CSS</span>
                                    <span class="skill-badge">SQL</span>
                                    <span class="skill-badge">Bootstrap</span>
                                    <span class="skill-badge">SQLite</span>
                                    <span class="skill-badge">تطوير تطبيقات الويب</span>
                                    <span class="skill-badge">تصميم واجهات المستخدم</span>
                                    <span class="skill-badge">أنظمة إدارة المعلومات الصحية</span>
                                    <span class="skill-badge">تطوير قواعد البيانات</span>
                                    <span class="skill-badge">تحليل البيانات</span>
                                    <span class="skill-badge">تطوير النظم المتكاملة</span>
                                    <span class="skill-badge">تصميم التقارير</span>
                                </div>
                            </div>
                        </div>

                        <!-- المشاريع -->
                        <div class="designer-section">
                            <h3 class="section-title"><i class="mdi mdi-briefcase me-2"></i>المشاريع السابقة</h3>
                            <div class="section-content">
                                <div class="projects-list">
                                    <div class="project-item">
                                        <h4 class="project-title">نظام إدارة منصرف الأدوية</h4>
                                        <p class="project-date">2023 - 2024</p>
                                        <p>تطوير نظام متكامل لإدارة صرف الأدوية للعيادات والإدارات وفقاً للمناطق داخل الفروع، مع إمكانية إنشاء تقارير مفصلة وإحصائيات دقيقة. يتضمن النظام إدارة الأنسولين والمجموعات الدوائية وتصنيفات الأدوية.</p>
                                    </div>
                                    <div class="project-item">
                                        <h4 class="project-title">نظام دليل أدوية التأمين الصحي الإلكتروني والبروتوكولات المنظمة لعمليات الصرف</h4>
                                        <p class="project-date">2022 - 2023</p>
                                        <p>تطوير نظام إلكتروني شامل لدليل أدوية التأمين الصحي مع البروتوكولات المنظمة لعمليات الصرف. يتضمن النظام قاعدة بيانات كاملة للأدوية المعتمدة، آليات الصرف، الجرعات المسموح بها، والبروتوكولات العلاجية المعتمدة. كما يوفر النظام واجهة سهلة الاستخدام للصيادلة والأطباء للتحقق من قواعد الصرف وضمان الالتزام بالبروتوكولات المعتمدة.</p>
                                    </div>
                                    <div class="project-item">
                                        <h4 class="project-title">نظام إدارة المخزون الطبي</h4>
                                        <p class="project-date">2021 - 2022</p>
                                        <p>تطوير نظام لإدارة المخزون الطبي في المستشفيات والمراكز الصحية، يشمل تتبع المخزون، إدارة الطلبات، تنبيهات انخفاض المخزون، وتقارير الاستهلاك.</p>
                                    </div>
                                    <div class="project-item">
                                        <h4 class="project-title">نظام متابعة المرضى</h4>
                                        <p class="project-date">2019 - 2021</p>
                                        <p>تطوير نظام لمتابعة حالات المرضى في العيادات والمستشفيات، يتضمن سجلات المرضى، متابعة العلاج، جدولة المواعيد، وإصدار التقارير الطبية.</p>
                                    </div>
                                    <div class="project-item">
                                        <h4 class="project-title">نظام التقارير الإحصائية الصحية</h4>
                                        <p class="project-date">2018 - 2019</p>
                                        <p>تطوير نظام لإنشاء وإدارة التقارير الإحصائية الصحية، يتضمن تحليل البيانات، عرض المؤشرات الرئيسية، وإنشاء تقارير دورية للإدارة العليا.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="designer-section">
                            <h3 class="section-title"><i class="mdi mdi-contact-mail me-2"></i>معلومات الاتصال</h3>
                            <div class="section-content">
                                <div class="contact-info">
                                    <div class="contact-item">
                                        <i class="mdi mdi-email contact-icon"></i>
                                        <div>
                                            <strong>البريد الإلكتروني:</strong>
                                            <p><EMAIL></p>
                                        </div>
                                    </div>
                                    <div class="contact-item">
                                        <i class="mdi mdi-phone contact-icon"></i>
                                        <div>
                                            <strong>الهاتف:</strong>
                                            <p>01000314398</p>
                                        </div>
                                    </div>
                                    <div class="contact-item">
                                        <i class="mdi mdi-web contact-icon"></i>
                                        <div>
                                            <strong>الموقع الإلكتروني:</strong>
                                            <p><EMAIL></p>
                                        </div>
                                    </div>
                                    <div class="contact-item">
                                        <i class="mdi mdi-whatsapp contact-icon"></i>
                                        <div>
                                            <strong>واتساب:</strong>
                                            <p>01000314398</p>
                                        </div>
                                    </div>
                                    <div class="contact-item">
                                        <i class="mdi mdi-map-marker contact-icon"></i>
                                        <div>
                                            <strong>العنوان:</strong>
                                            <p>سوهاج، مصر</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ url_for('copyright') }}" class="btn btn-primary me-2">
                                <i class="mdi mdi-copyright me-1"></i>حقوق الملكية ك/ أحمد علي أحمد (أحمد كوكب)
                            </a>
                            <a href="{{ url_for('index') }}" class="btn btn-secondary">
                                <i class="mdi mdi-home-variant me-1"></i>العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
