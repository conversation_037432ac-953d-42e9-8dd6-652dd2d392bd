/* ملف CSS مخصص لتطبيق منصرف الأدوية */

/* تعديلات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* تنسيق العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

/* تنسيق البطاقات */
.card {
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-bottom: 0;
    padding: 15px 20px;
}

/* تنسيق الأزرار */
.btn {
    border-radius: 5px;
    padding: 8px 16px;
    transition: all 0.3s;
}

.btn-lg {
    padding: 12px 24px;
}

.btn-primary {
    background-color: #2196F3;
    border-color: #2196F3;
}

.btn-primary:hover {
    background-color: #1976D2;
    border-color: #1976D2;
}

.btn-success {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.btn-success:hover {
    background-color: #388E3C;
    border-color: #388E3C;
}

/* تنسيق النماذج */
.form-control, .form-select {
    border-radius: 5px;
    padding: 10px 15px;
    border: 1px solid #ddd;
}

.form-control:focus, .form-select:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 0.25rem rgba(33, 150, 243, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
}

/* تنسيق الجداول */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تنسيق شريط التنقل */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
}

/* تنسيق التذييل */
footer {
    border-top: 1px solid #eee;
}

/* أنماط أزرار التقارير */
.action-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    font-size: 24px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
}

.action-btn:hover {
    transform: scale(1.1);
    color: white;
    text-decoration: none;
}

.print-btn {
    background-color: #4e73df;
}

.print-btn:hover {
    background-color: #3a5fc9;
}

.back-btn {
    background-color: #6c757d;
}

.back-btn:hover {
    background-color: #5a6268;
}

.excel-btn {
    background-color: #1d6f42;
}

.excel-btn:hover {
    background-color: #185a36;
}

.pdf-btn {
    background-color: #e74a3b;
    color: white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: visible;
}

.pdf-btn:hover {
    background-color: #c0392b;
}

.pdf-btn i {
    font-size: 28px !important;
    line-height: 1;
    color: white !important;
    display: block !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

@media print {
    .action-buttons {
        display: none !important;
    }
}
