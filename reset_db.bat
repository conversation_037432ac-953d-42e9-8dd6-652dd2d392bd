@echo off
chcp 65001 > nul
echo ============================================
echo    إعادة تهيئة قاعدة البيانات
echo ============================================

:: حذف قاعدة البيانات القديمة إذا وجدت
if exist "instance\medicine_dispenser.db" (
    echo حذف قاعدة البيانات القديمة...
    del /f "instance\medicine_dispenser.db"
)

:: إعادة تشغيل التطبيق لإنشاء قاعدة بيانات جديدة
echo إنشاء قاعدة بيانات جديدة...
python run_app.py

echo ============================================
echo تم إعادة تهيئة قاعدة البيانات بنجاح
echo ============================================
pause
