{% extends "base.html" %}

{% block title %}تكلفة المجموعات الدوائية - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    .cost-card {
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
    }

    .cost-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .cost-card .card-header {
        padding: 1.25rem;
    }

    .btn-add {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .table-custom th {
        background-color: #f8f9fc;
        font-weight: 600;
    }

    .form-control, .form-select {
        border-radius: 0.5rem;
        padding: 0.6rem 1rem;
    }

    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Modal تعديل المجموعة الدوائية -->
<div class="modal fade" id="editGroupModal" tabindex="-1" aria-labelledby="editGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editGroupModalLabel">تعديل تكلفة المجموعة الدوائية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editGroupForm" method="POST" action="/manage/drug_groups/update">
                    <input type="hidden" id="edit_group_id" name="group_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_group_code_id" class="form-label">المجموعة الدوائية</label>
                                <select class="form-select" id="edit_group_code_id" name="group_code_id">
                                    <option value="">-- اختر المجموعة الدوائية --</option>
                                    {% for code in drug_group_codes %}
                                    <option value="{{ code.id }}">{{ code.code }} - {{ code.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="edit_cost" class="form-label">التكلفة</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="mdi mdi-cash"></i></span>
                                    <input type="number" step="0.01" min="0.01" class="form-control" id="edit_cost" name="cost" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_clinic_id" class="form-label">العيادة</label>
                                <select class="form-select" id="edit_clinic_id" name="clinic_id" required>
                                    <option value="">-- اختر العيادة --</option>
                                    {% for clinic in clinics %}
                                    <option value="{{ clinic.id }}">{{ clinic.name }} ({{ clinic.area_name }} - {{ clinic.branch_name }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="edit_dispense_month" class="form-label">شهر الصرف</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                    <input type="month" class="form-control" id="edit_dispense_month" name="dispense_month" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- حقل اسم المجموعة مخفي، سيتم ملؤه تلقائياً بناءً على المجموعة المختارة -->
                    <input type="hidden" id="edit_name" name="name" value="">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveEditGroup">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="mdi mdi-package-variant me-2"></i>تكلفة المجموعات الدوائية
                    </h4>
                    <a href="/manage/drug_group_codes" class="btn btn-light btn-add">
                        <i class="mdi mdi-plus-circle me-1"></i>إضافة مجموعة جديدة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-8 mx-auto">
                        <form method="POST" action="/manage/drug_groups">
                            <div class="card cost-card shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="mdi mdi-currency-usd me-2"></i>تسجيل تكلفة مجموعة دوائية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="group_code_id" class="form-label">المجموعة الدوائية</label>
                                                <select class="form-select" id="group_code_id" name="group_code_id" required>
                                                    <option value="">-- اختر المجموعة الدوائية --</option>
                                                    {% for code in group_codes %}
                                                    <option value="{{ code.id }}">{{ code.code }} - {{ code.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="cost" class="form-label">التكلفة</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="mdi mdi-cash"></i></span>
                                                    <input type="number" step="0.01" min="0.01" class="form-control" id="cost" name="cost" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="clinic_id" class="form-label">العيادة</label>
                                                <select class="form-select" id="clinic_id" name="clinic_id" required>
                                                    <option value="">-- اختر العيادة --</option>
                                                    {% for clinic in clinics %}
                                                    <option value="{{ clinic.id }}">{{ clinic.name }} ({{ clinic.area_name }} - {{ clinic.branch_name }})</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="dispense_month" class="form-label">شهر الصرف</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                                    <input type="month" class="form-control" id="dispense_month" name="dispense_month" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- حقل اسم المجموعة مخفي، سيتم ملؤه تلقائياً بناءً على المجموعة المختارة -->
                                    <input type="hidden" id="name" name="name" value="">

                                    <div class="text-center mt-3">
                                        <button type="submit" class="btn btn-primary btn-add">
                                            <i class="mdi mdi-content-save me-1"></i>حفظ التكلفة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card cost-card shadow-sm mt-4">
                    <div class="card-header bg-light">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0"><i class="mdi mdi-format-list-bulleted me-2"></i>سجل تكاليف المجموعات الدوائية</h5>
                            </div>
                            <div class="col-auto">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                                    <input type="text" id="searchGroup" class="form-control" placeholder="بحث...">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if groups %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-custom" id="groupsTable">
                                <thead>
                                    <tr class="text-center">
                                        <th>#</th>
                                        <th>كود المجموعة</th>
                                        <th>اسم المجموعة</th>
                                        <th>التكلفة</th>
                                        <th>العيادة</th>
                                        <th>المنطقة</th>
                                        <th>شهر الصرف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for group in groups %}
                                    <tr>
                                        <td class="text-center">{{ loop.index }}</td>
                                        <td class="text-center fw-bold">{{ group.code_name or '-' }}</td>
                                        <td>{% if group.code_group_name %}{{ group.code_group_name }}{% else %}{{ group.name }}{% endif %}</td>
                                        <td class="text-center fw-bold">{{ group.cost }}</td>
                                        <td>{{ group.clinic_name }}</td>
                                        <td>{{ group.area_name }}</td>
                                        <td class="text-center">{{ group.dispense_month }}</td>
                                        <td class="text-center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-info rounded-circle me-1 edit-group"
                                                        data-id="{{ group.id }}"
                                                        data-name="{{ group.name }}"
                                                        data-cost="{{ group.cost }}"
                                                        data-clinic="{{ group.clinic_id }}"
                                                        data-month="{{ group.dispense_month }}"
                                                        data-code="{{ group.group_code_id or '' }}">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>
                                                <form method="POST" action="/manage/drug_groups/{{ group.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه المجموعة؟');">
                                                    <button type="submit" class="btn btn-sm btn-danger rounded-circle">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="mdi mdi-information-outline me-2"></i>لا توجد تكاليف مجموعات دوائية مسجلة حالياً.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // البحث في جدول المجموعات الدوائية
        $("#searchGroup").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $("#groupsTable tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });

        // تعيين التاريخ الحالي كقيمة افتراضية لشهر الصرف
        var today = new Date();
        var year = today.getFullYear();
        var month = (today.getMonth() + 1).toString().padStart(2, '0');
        $('#dispense_month').val(year + '-' + month);

        // ملء حقل اسم المجموعة تلقائياً بناءً على المجموعة المختارة
        $("#group_code_id, #edit_group_code_id").on("change", function() {
            var selectedOption = $(this).find("option:selected");
            var nameField = $(this).attr('id') === 'group_code_id' ? "#name" : "#edit_name";

            if (selectedOption.val()) {
                var fullText = selectedOption.text();
                var groupName = fullText.includes(" - ") ? fullText.split(" - ")[1] : fullText;
                $(nameField).val(groupName);
                console.log("تم تعيين اسم المجموعة:", groupName);
            } else {
                $(nameField).val("");
            }
        });

        // التحقق من البيانات قبل الإرسال
        $("form").on("submit", function(e) {
            var name = $("#name").val();
            var cost = $("#cost").val();
            var clinic_id = $("#clinic_id").val();
            var dispense_month = $("#dispense_month").val();

            console.log("البيانات قبل الإرسال:", {
                name: name,
                cost: cost,
                clinic_id: clinic_id,
                dispense_month: dispense_month
            });

            if (!name) {
                alert("يرجى اختيار المجموعة الدوائية أولاً");
                e.preventDefault();
                return false;
            }
        });

        // تأثيرات بصرية للنموذج
        $(".form-control, .form-select").on("focus", function() {
            $(this).closest(".mb-3").addClass("was-validated");
        });

        // تنسيق التكلفة
        $("#cost, #edit_cost").on("blur", function() {
            if ($(this).val()) {
                var cost = parseFloat($(this).val());
                $(this).val(cost.toFixed(2));
            }
        });

        // فتح نافذة التعديل عند النقر على زر التعديل
        $(".edit-group").on("click", function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var cost = $(this).data('cost');
            var clinic = $(this).data('clinic');
            var month = $(this).data('month');
            var code = $(this).data('code');

            // ملء النموذج بالبيانات
            $("#edit_group_id").val(id);
            $("#edit_cost").val(cost);
            $("#edit_clinic_id").val(clinic);
            $("#edit_dispense_month").val(month);
            $("#edit_group_code_id").val(code);
            $("#edit_name").val(name);

            // فتح النافذة المنبثقة
            $("#editGroupModal").modal('show');
        });

        // حفظ التغييرات عند النقر على زر الحفظ
        $("#saveEditGroup").on("click", function() {
            // التحقق من صحة النموذج
            if ($("#edit_cost").val() && $("#edit_clinic_id").val() && $("#edit_dispense_month").val()) {
                // إرسال النموذج
                $("#editGroupForm").submit();
            } else {
                alert("يرجى إدخال جميع البيانات المطلوبة");
            }
        });

        // تأثيرات بصرية للأزرار
        $(".btn-group .btn").hover(
            function() {
                $(this).addClass("shadow-sm");
            },
            function() {
                $(this).removeClass("shadow-sm");
            }
        );
    });
</script>
{% endblock %}
