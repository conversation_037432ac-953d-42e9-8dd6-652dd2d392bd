from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, make_response
import sqlite3
import os
import json
from datetime import datetime, timedelta

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'مفتاح-سري-افتراضي-للتطوير'

# التأكد من وجود مجلد instance
if not os.path.exists('instance'):
    os.makedirs('instance')

# دالة للاتصال بقاعدة البيانات
def get_db_connection():
    # التأكد من وجود مجلد instance
    if not os.path.exists('instance'):
        os.makedirs('instance')

    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

# إنشاء قاعدة البيانات
def init_db():
    conn = get_db_connection()

    # إنشاء جدول الفروع
    conn.execute('''
    CREATE TABLE IF NOT EXISTS branches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE
    )
    ''')

    # إنشاء جدول المناطق
    conn.execute('''
    CREATE TABLE IF NOT EXISTS areas (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        branch_id INTEGER NOT NULL,
        FOREIGN KEY (branch_id) REFERENCES branches (id) ON DELETE CASCADE
    )
    ''')

    # إنشاء جدول العيادات
    conn.execute('''
    CREATE TABLE IF NOT EXISTS clinics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        area_id INTEGER NOT NULL,
        FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE
    )
    ''')

    # إنشاء جدول تصنيفات الأدوية
    conn.execute('''
    CREATE TABLE IF NOT EXISTS drug_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE
    )
    ''')

    # إنشاء جدول الأدوية
    conn.execute('''
    CREATE TABLE IF NOT EXISTS drugs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        scientific_name TEXT,
        category_id INTEGER NOT NULL,
        expiry_date DATE,
        FOREIGN KEY (category_id) REFERENCES drug_categories (id) ON DELETE CASCADE
    )
    ''')

    # إنشاء جدول المنصرف
    conn.execute('''
    CREATE TABLE IF NOT EXISTS dispensed (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        drug_id INTEGER NOT NULL,
        clinic_id INTEGER NOT NULL,
        dispense_month DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (drug_id) REFERENCES drugs (id) ON DELETE CASCADE,
        FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE
    )
    ''')

    # إنشاء جدول تفاصيل المنصرف
    conn.execute('''
    CREATE TABLE IF NOT EXISTS dispensed_details (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        dispensed_id INTEGER NOT NULL,
        quantity REAL NOT NULL,
        price REAL NOT NULL,
        cases_count INTEGER NOT NULL,
        FOREIGN KEY (dispensed_id) REFERENCES dispensed (id) ON DELETE CASCADE
    )
    ''')

    # إنشاء جدول تكويد المجموعات الدوائية
    conn.execute('''
    CREATE TABLE IF NOT EXISTS drug_group_codes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code INTEGER NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # إنشاء جدول المجموعات الدوائية
    conn.execute('''
    CREATE TABLE IF NOT EXISTS drug_groups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        cost REAL NOT NULL,
        clinic_id INTEGER NOT NULL,
        area_id INTEGER NOT NULL,
        dispense_month DATE NOT NULL,
        group_code_id INTEGER,
        FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
        FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE,
        FOREIGN KEY (group_code_id) REFERENCES drug_group_codes (id) ON DELETE SET NULL
    )
    ''')

    # إنشاء جدول تكويد الأنسولين
    conn.execute('''
    CREATE TABLE IF NOT EXISTS insulin_codes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code INTEGER NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # إنشاء جدول أنواع الأنسولين
    conn.execute('''
    CREATE TABLE IF NOT EXISTS insulin_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # إنشاء جدول فئات الأنسولين
    conn.execute('''
    CREATE TABLE IF NOT EXISTS insulin_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # إنشاء جدول الأنسولين
    conn.execute('''
    CREATE TABLE IF NOT EXISTS insulin_dispensed (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        unit TEXT NOT NULL,
        cases_count INTEGER NOT NULL,
        quantity REAL NOT NULL,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        rate REAL NOT NULL,
        balance REAL NOT NULL,
        category TEXT NOT NULL,
        clinic_id INTEGER NOT NULL,
        area_id INTEGER NOT NULL,
        dispense_month DATE NOT NULL,
        insulin_code_id INTEGER,
        FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
        FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE,
        FOREIGN KEY (insulin_code_id) REFERENCES insulin_codes (id) ON DELETE SET NULL
    )
    ''')

    # إضافة بعض البيانات الافتراضية للاختبار
    try:
        # إضافة الفروع
        conn.execute("INSERT INTO branches (name) VALUES ('الفرع الرئيسي')")
        conn.execute("INSERT INTO branches (name) VALUES ('الفرع الشمالي')")
        conn.execute("INSERT INTO branches (name) VALUES ('الفرع الجنوبي')")
        conn.execute("INSERT INTO branches (name) VALUES ('الفرع الشرقي')")
        conn.execute("INSERT INTO branches (name) VALUES ('الفرع الغربي')")

        # إضافة تصنيفات الأدوية
        conn.execute("INSERT INTO drug_categories (name) VALUES ('مضادات حيوية')")
        conn.execute("INSERT INTO drug_categories (name) VALUES ('مسكنات')")
        conn.execute("INSERT INTO drug_categories (name) VALUES ('أدوية القلب')")
        conn.execute("INSERT INTO drug_categories (name) VALUES ('أدوية السكري')")
        conn.execute("INSERT INTO drug_categories (name) VALUES ('أدوية الضغط')")
        conn.execute("INSERT INTO drug_categories (name) VALUES ('فيتامينات ومكملات')")

        # إضافة أنواع الأنسولين
        conn.execute("INSERT INTO insulin_types (name, description) VALUES ('سريع المفعول', 'يبدأ مفعوله خلال 15 دقيقة ويستمر لمدة 3-4 ساعات')")
        conn.execute("INSERT INTO insulin_types (name, description) VALUES ('متوسط المفعول', 'يبدأ مفعوله خلال 1-2 ساعة ويستمر لمدة 12-18 ساعة')")
        conn.execute("INSERT INTO insulin_types (name, description) VALUES ('طويل المفعول', 'يبدأ مفعوله خلال 1-2 ساعة ويستمر لمدة 24 ساعة أو أكثر')")
        conn.execute("INSERT INTO insulin_types (name, description) VALUES ('مختلط', 'مزيج من الأنسولين سريع المفعول ومتوسط المفعول')")

        # إضافة فئات الأنسولين
        conn.execute("INSERT INTO insulin_categories (name, description) VALUES ('هيئة', 'أنسولين مخصص للعاملين بالهيئة')")
        conn.execute("INSERT INTO insulin_categories (name, description) VALUES ('طلاب', 'أنسولين مخصص للطلاب')")
        conn.execute("INSERT INTO insulin_categories (name, description) VALUES ('رضع', 'أنسولين مخصص للرضع والأطفال')")
        conn.execute("INSERT INTO insulin_categories (name, description) VALUES ('مرأة معيلة', 'أنسولين مخصص للنساء المعيلات')")

        # إضافة المناطق
        conn.execute("INSERT INTO areas (name, branch_id) VALUES ('منطقة وسط المدينة', 1)")
        conn.execute("INSERT INTO areas (name, branch_id) VALUES ('منطقة شرق المدينة', 1)")
        conn.execute("INSERT INTO areas (name, branch_id) VALUES ('منطقة شمال المدينة', 2)")
        conn.execute("INSERT INTO areas (name, branch_id) VALUES ('منطقة جنوب المدينة', 3)")
        conn.execute("INSERT INTO areas (name, branch_id) VALUES ('منطقة غرب المدينة', 5)")
        conn.execute("INSERT INTO areas (name, branch_id) VALUES ('منطقة الضواحي الشرقية', 4)")

        # إضافة العيادات
        conn.execute("INSERT INTO clinics (name, area_id) VALUES ('عيادة الرحمة', 1)")
        conn.execute("INSERT INTO clinics (name, area_id) VALUES ('عيادة الشفاء', 1)")
        conn.execute("INSERT INTO clinics (name, area_id) VALUES ('عيادة النور', 2)")
        conn.execute("INSERT INTO clinics (name, area_id) VALUES ('عيادة الأمل', 3)")
        conn.execute("INSERT INTO clinics (name, area_id) VALUES ('عيادة السلام', 4)")
        conn.execute("INSERT INTO clinics (name, area_id) VALUES ('عيادة الصحة', 5)")
        conn.execute("INSERT INTO clinics (name, area_id) VALUES ('عيادة الحياة', 6)")

        # إضافة الأدوية
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('كونكور', 'بيسوبرولول', 3)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('أموكسيسيلين', 'أموكسيسيلين', 1)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('باراسيتامول', 'باراسيتامول', 2)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('أنسولين', 'إنسولين بشري', 4)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('كابتوبريل', 'كابتوبريل', 5)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('فيتامين د', 'كوليكالسيفيرول', 6)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('أتينولول', 'أتينولول', 5)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('ميتفورمين', 'ميتفورمين', 4)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('أزيثرومايسين', 'أزيثرومايسين', 1)")
        conn.execute("INSERT INTO drugs (name, scientific_name, category_id) VALUES ('إيبوبروفين', 'إيبوبروفين', 2)")

        # إضافة بيانات المنصرف للاختبار
        current_date = datetime.now().strftime('%Y-%m-01')
        last_month = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-01')
        two_months_ago = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-01')

        # منصرف كونكور بأسعار مختلفة
        # إضافة منصرف 1
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (1, 1, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 120, 30, 2)", (dispensed_id,))

        # إضافة منصرف 2
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (1, 1, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 180, 40, 3)", (dispensed_id,))

        # إضافة منصرف 3
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (1, 1, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 600, 30, 6)", (dispensed_id,))

        # إضافة منصرف 4
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (1, 1, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 500, 40, 5)", (dispensed_id,))

        # منصرف أموكسيسيلين
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (2, 2, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 300, 25, 10)", (dispensed_id,))

        # منصرف باراسيتامول
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (3, 3, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 200, 15, 20)", (dispensed_id,))

        # منصرف أنسولين
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (4, 4, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 150, 60, 15)", (dispensed_id,))

        # منصرف كابتوبريل
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (5, 5, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 250, 35, 25)", (dispensed_id,))

        # منصرف فيتامين د
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (6, 6, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 100, 20, 10)", (dispensed_id,))

        # منصرف أتينولول
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (7, 7, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 180, 45, 18)", (dispensed_id,))

        # منصرف ميتفورمين
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (8, 1, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 220, 30, 22)", (dispensed_id,))

        # منصرف أزيثرومايسين
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (9, 2, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 150, 50, 15)", (dispensed_id,))

        # منصرف إيبوبروفين
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (10, 3, ?)", (current_date,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 300, 18, 30)", (dispensed_id,))

        # إضافة منصرفات للشهر الماضي
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (1, 1, ?)", (last_month,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 100, 30, 10)", (dispensed_id,))

        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (2, 2, ?)", (last_month,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 200, 25, 20)", (dispensed_id,))

        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (3, 3, ?)", (last_month,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 150, 15, 15)", (dispensed_id,))

        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (4, 4, ?)", (last_month,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 120, 60, 12)", (dispensed_id,))

        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (5, 5, ?)", (last_month,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 180, 35, 18)", (dispensed_id,))

        # إضافة منصرفات لشهرين سابقين
        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (1, 1, ?)", (two_months_ago,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 80, 30, 8)", (dispensed_id,))

        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (2, 2, ?)", (two_months_ago,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 150, 25, 15)", (dispensed_id,))

        conn.execute("INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (3, 3, ?)", (two_months_ago,))
        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.execute("INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, 120, 15, 12)", (dispensed_id,))

    except sqlite3.IntegrityError:
        # تجاهل الأخطاء إذا كانت البيانات موجودة بالفعل
        pass

    conn.commit()
    conn.close()

# تهيئة قاعدة البيانات
init_db()

# المسارات
@app.route('/')
def index():
    conn = get_db_connection()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('index.html', branches=branches)

@app.route('/copyright')
def copyright():
    current_year = datetime.now().year
    return render_template('copyright.html', current_year=current_year)

@app.route('/designer')
def designer():
    return render_template('designer.html')

@app.route('/manage/branches', methods=['GET', 'POST'])
def manage_branches():
    if request.method == 'POST':
        name = request.form.get('name')
        if name:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO branches (name) VALUES (?)', (name,))
                conn.commit()
                flash('تم إضافة الفرع بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا الفرع موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم الفرع', 'danger')

    conn = get_db_connection()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('manage_branches.html', branches=branches)

@app.route('/manage/branches/<int:branch_id>/delete', methods=['POST'])
def delete_branch(branch_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM branches WHERE id = ?', (branch_id,))
    conn.commit()
    conn.close()
    flash('تم حذف الفرع بنجاح', 'success')
    return redirect(url_for('manage_branches'))

@app.route('/manage/branches/update', methods=['POST'])
def update_branch():
    branch_id = request.form.get('branch_id')
    name = request.form.get('name')

    if branch_id and name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE branches SET name = ? WHERE id = ?', (name, branch_id))
            conn.commit()
            flash('تم تحديث الفرع بنجاح', 'success')
        except sqlite3.IntegrityError:
            flash('هذا الفرع موجود بالفعل', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم الفرع', 'danger')

    return redirect(url_for('manage_branches'))

@app.route('/manage/areas', methods=['GET', 'POST'])
def manage_areas():
    if request.method == 'POST':
        name = request.form.get('name')
        branch_id = request.form.get('branch_id')

        if name and branch_id:
            conn = get_db_connection()
            conn.execute('INSERT INTO areas (name, branch_id) VALUES (?, ?)', (name, branch_id))
            conn.commit()
            conn.close()
            flash('تم إضافة المنطقة بنجاح', 'success')
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    conn = get_db_connection()
    areas = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('manage_areas.html', areas=areas, branches=branches)

@app.route('/manage/areas/<int:area_id>/delete', methods=['POST'])
def delete_area(area_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM areas WHERE id = ?', (area_id,))
    conn.commit()
    conn.close()
    flash('تم حذف المنطقة بنجاح', 'success')
    return redirect(url_for('manage_areas'))

@app.route('/manage/areas/update', methods=['POST'])
def update_area():
    area_id = request.form.get('area_id')
    name = request.form.get('name')
    branch_id = request.form.get('branch_id')

    if area_id and name and branch_id:
        conn = get_db_connection()
        conn.execute('UPDATE areas SET name = ?, branch_id = ? WHERE id = ?', (name, branch_id, area_id))
        conn.commit()
        conn.close()
        flash('تم تحديث المنطقة بنجاح', 'success')
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_areas'))

@app.route('/manage/clinics', methods=['GET', 'POST'])
def manage_clinics():
    if request.method == 'POST':
        name = request.form.get('name')
        area_id = request.form.get('area_id')

        if name and area_id:
            conn = get_db_connection()
            conn.execute('INSERT INTO clinics (name, area_id) VALUES (?, ?)', (name, area_id))
            conn.commit()
            conn.close()
            flash('تم إضافة العيادة بنجاح', 'success')
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    areas = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    conn.close()
    return render_template('manage_clinics.html', clinics=clinics, areas=areas)

@app.route('/manage/clinics/<int:clinic_id>/delete', methods=['POST'])
def delete_clinic(clinic_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM clinics WHERE id = ?', (clinic_id,))
    conn.commit()
    conn.close()
    flash('تم حذف العيادة بنجاح', 'success')
    return redirect(url_for('manage_clinics'))

@app.route('/manage/clinics/update', methods=['POST'])
def update_clinic():
    clinic_id = request.form.get('clinic_id')
    name = request.form.get('name')
    area_id = request.form.get('area_id')

    if clinic_id and name and area_id:
        conn = get_db_connection()
        conn.execute('UPDATE clinics SET name = ?, area_id = ? WHERE id = ?', (name, area_id, clinic_id))
        conn.commit()
        conn.close()
        flash('تم تحديث العيادة بنجاح', 'success')
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_clinics'))

@app.route('/api/areas/<int:branch_id>')
def get_areas(branch_id):
    conn = get_db_connection()
    areas = conn.execute('SELECT * FROM areas WHERE branch_id = ?', (branch_id,)).fetchall()
    conn.close()
    return jsonify([{'id': area['id'], 'name': area['name']} for area in areas])

@app.route('/api/clinics/<int:area_id>')
def get_clinics(area_id):
    conn = get_db_connection()
    clinics = conn.execute('SELECT * FROM clinics WHERE area_id = ?', (area_id,)).fetchall()
    conn.close()
    return jsonify([{'id': clinic['id'], 'name': clinic['name']} for clinic in clinics])

@app.route('/api/clinics')
def get_all_clinics():
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    conn.close()
    return jsonify([{
        'id': clinic['id'],
        'name': f"{clinic['name']} - {clinic['area_name']} - {clinic['branch_name']}"
    } for clinic in clinics])

@app.route('/api/drugs_by_category/<int:category_id>')
def get_drugs_by_category(category_id):
    conn = get_db_connection()
    drugs = conn.execute('SELECT * FROM drugs WHERE category_id = ?', (category_id,)).fetchall()
    conn.close()
    return jsonify([{'id': drug['id'], 'name': drug['name']} for drug in drugs])

@app.route('/manage/drugs/new', methods=['GET', 'POST'])
def manage_drugs_new():
    if request.method == 'POST':
        name = request.form.get('name')
        scientific_name = request.form.get('scientific_name')
        category_id = request.form.get('category_id')
        expiry_date = request.form.get('expiry_date')

        if name and category_id:
            conn = get_db_connection()
            conn.execute(
                'INSERT INTO drugs (name, scientific_name, category_id, expiry_date) VALUES (?, ?, ?, ?)',
                (name, scientific_name, category_id, expiry_date)
            )
            conn.commit()
            conn.close()
            flash('تم إضافة الدواء بنجاح', 'success')
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    conn = get_db_connection()
    drugs = conn.execute('''
        SELECT drugs.*, drug_categories.name as category_name
        FROM drugs
        JOIN drug_categories ON drugs.category_id = drug_categories.id
    ''').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    conn.close()
    return render_template('manage_drugs_new.html', drugs=drugs, categories=categories)

@app.route('/manage/drugs', methods=['GET', 'POST'])
def manage_drugs():
    if request.method == 'POST':
        name = request.form.get('name')
        scientific_name = request.form.get('scientific_name')
        category_id = request.form.get('category_id')
        expiry_date = request.form.get('expiry_date')

        if name and category_id:
            conn = get_db_connection()
            conn.execute(
                'INSERT INTO drugs (name, scientific_name, category_id, expiry_date) VALUES (?, ?, ?, ?)',
                (name, scientific_name, category_id, expiry_date)
            )
            conn.commit()
            conn.close()
            flash('تم إضافة الدواء بنجاح', 'success')
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    conn = get_db_connection()
    drugs = conn.execute('''
        SELECT drugs.*, drug_categories.name as category_name
        FROM drugs
        JOIN drug_categories ON drugs.category_id = drug_categories.id
    ''').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    conn.close()
    return render_template('manage_drugs.html', drugs=drugs, categories=categories)

@app.route('/manage/drugs/<int:drug_id>/delete', methods=['POST'])
def delete_drug(drug_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM drugs WHERE id = ?', (drug_id,))
    conn.commit()
    conn.close()
    flash('تم حذف الدواء بنجاح', 'success')
    return redirect(url_for('manage_drugs'))

@app.route('/manage/drugs/update', methods=['POST'])
def update_drug():
    drug_id = request.form.get('drug_id')
    name = request.form.get('name')
    scientific_name = request.form.get('scientific_name')
    category_id = request.form.get('category_id')
    expiry_date = request.form.get('expiry_date')

    if drug_id and name and category_id:
        conn = get_db_connection()
        conn.execute(
            'UPDATE drugs SET name = ?, scientific_name = ?, category_id = ?, expiry_date = ? WHERE id = ?',
            (name, scientific_name, category_id, expiry_date, drug_id)
        )
        conn.commit()
        conn.close()
        flash('تم تحديث الدواء بنجاح', 'success')
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_drugs'))

@app.route('/dispense/new', methods=['GET', 'POST'])
def dispense_new():
    if request.method == 'POST':
        drug_id = request.form.get('drug_id')
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        quantity = request.form.get('quantity')
        price = request.form.get('price')
        cases_count = request.form.get('cases_count')

        if drug_id and clinic_id and dispense_month and quantity and price and cases_count:
            try:
                # تحويل التاريخ
                dispense_date = f"{dispense_month}-01"

                conn = get_db_connection()

                # إنشاء سجل المنصرف
                conn.execute(
                    'INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (?, ?, ?)',
                    (drug_id, clinic_id, dispense_date)
                )
                conn.commit()

                # الحصول على معرف السجل المضاف
                dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]

                # إضافة تفاصيل المنصرف
                conn.execute(
                    'INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, ?, ?, ?)',
                    (dispensed_id, float(quantity), float(price), int(cases_count))
                )
                conn.commit()
                conn.close()

                flash('تم تسجيل المنصرف بنجاح', 'success')
                return redirect(url_for('dispense_new'))
            except Exception as e:
                flash(f'حدث خطأ أثناء حفظ البيانات: {str(e)}', 'danger')
                return redirect(url_for('dispense_new'))
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    # جلب البيانات اللازمة للعرض
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()

    categories = conn.execute('SELECT * FROM drug_categories').fetchall()

    # جلب آخر 10 منصرفات
    dispensed_items = conn.execute('''
        SELECT
            d.id,
            c.name as clinic_name,
            dr.name as drug_name,
            dd.quantity,
            dd.price,
            dd.cases_count,
            (dd.quantity * dd.price) as total_cost,
            d.dispense_month
        FROM dispensed d
        JOIN clinics c ON d.clinic_id = c.id
        JOIN drugs dr ON d.drug_id = dr.id
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        ORDER BY d.id DESC
        LIMIT 10
    ''').fetchall()

    conn.close()

    return render_template('dispense_new.html', clinics=clinics, categories=categories, dispensed_items=dispensed_items)

@app.route('/dispense', methods=['GET', 'POST'])
def dispense():
    if request.method == 'POST':
        drug_id = request.form.get('drug_id')
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        items_data = request.form.get('items_data')

        if drug_id and clinic_id and dispense_month and items_data:
            try:
                # تحويل البيانات من JSON إلى قائمة
                import json
                items = json.loads(items_data)

                if not items:
                    flash('يرجى إضافة عنصر واحد على الأقل إلى القائمة', 'danger')
                    return redirect(url_for('dispense'))

                # تحويل التاريخ
                dispense_date = f"{dispense_month}-01"

                conn = get_db_connection()

                # إضافة سجل المنصرف لكل عنصر
                for item in items:
                    item_drug_id = item.get('drug_id', drug_id)  # استخدام معرف الدواء من العنصر إذا كان موجودًا، وإلا استخدام المعرف العام
                    quantity = item.get('quantity')
                    price = item.get('price')
                    cases_count = item.get('cases_count')

                    if quantity and price and cases_count:
                        # إنشاء سجل المنصرف
                        conn.execute(
                            'INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (?, ?, ?)',
                            (item_drug_id, clinic_id, dispense_date)
                        )
                        conn.commit()

                        # الحصول على معرف السجل المضاف
                        dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]

                        # إضافة تفاصيل المنصرف
                        conn.execute(
                            'INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, ?, ?, ?)',
                            (dispensed_id, float(quantity), float(price), int(cases_count))
                        )
                        conn.commit()

                conn.close()

                flash(f'تم تسجيل {len(items)} من بيانات المنصرف بنجاح', 'success')
                return redirect(url_for('dispense'))
            except json.JSONDecodeError:
                flash('حدث خطأ في تنسيق البيانات', 'danger')
                return redirect(url_for('dispense'))
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    # جلب البيانات اللازمة للعرض
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()

    # جلب تصنيفات الأدوية
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()

    # جلب سجلات الصرف الأخيرة
    dispensed_items = conn.execute('''
        SELECT
            d.id,
            c.name as clinic_name,
            a.name as area_name,
            b.name as branch_name,
            dr.name as drug_name,
            dc.name as category_name,
            dd.quantity,
            dd.price,
            dd.cases_count,
            dd.quantity * dd.price as total_cost,
            d.dispense_month
        FROM dispensed d
        JOIN clinics c ON d.clinic_id = c.id
        JOIN areas a ON c.area_id = a.id
        JOIN branches b ON a.branch_id = b.id
        JOIN drugs dr ON d.drug_id = dr.id
        JOIN drug_categories dc ON dr.category_id = dc.id
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        ORDER BY d.created_at DESC
        LIMIT 10
    ''').fetchall()

    conn.close()

    return render_template('dispense.html', clinics=clinics, categories=categories, dispensed_items=dispensed_items)

@app.route('/dispense/<int:dispensed_id>/delete', methods=['POST'])
def delete_dispensed(dispensed_id):
    print(f"Attempting to delete dispensed record with ID: {dispensed_id}")
    conn = get_db_connection()
    try:
        # التحقق من وجود السجل
        record = conn.execute('SELECT id FROM dispensed WHERE id = ?', (dispensed_id,)).fetchone()
        if not record:
            flash(f'سجل المنصرف رقم {dispensed_id} غير موجود', 'warning')
            return redirect(url_for('dispense'))

        # التحقق من وجود تفاصيل المنصرف
        details = conn.execute('SELECT id FROM dispensed_details WHERE dispensed_id = ?', (dispensed_id,)).fetchall()
        print(f"Found {len(details)} detail records for dispensed ID: {dispensed_id}")

        # حذف تفاصيل المنصرف أولاً بسبب قيود المفتاح الأجنبي
        result = conn.execute('DELETE FROM dispensed_details WHERE dispensed_id = ?', (dispensed_id,))
        print(f"Deleted {result.rowcount} records from dispensed_details")

        # حذف سجل المنصرف
        result = conn.execute('DELETE FROM dispensed WHERE id = ?', (dispensed_id,))
        print(f"Deleted {result.rowcount} records from dispensed")

        conn.commit()
        flash('تم حذف سجل المنصرف بنجاح', 'success')
    except Exception as e:
        conn.rollback()
        import traceback
        error_details = traceback.format_exc()
        print(f"Error deleting dispensed record: {e}")
        print(f"Error details: {error_details}")
        flash(f'حدث خطأ أثناء حذف السجل: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('dispense'))

@app.route('/dispense/<int:dispensed_id>/edit', methods=['GET', 'POST'])
def edit_dispensed(dispensed_id):
    if request.method == 'POST':
        quantity = request.form.get('quantity')
        price = request.form.get('price')
        cases_count = request.form.get('cases_count')

        if quantity and price and cases_count:
            conn = get_db_connection()
            try:
                # تحديث تفاصيل المنصرف
                conn.execute(
                    'UPDATE dispensed_details SET quantity = ?, price = ?, cases_count = ? WHERE dispensed_id = ?',
                    (float(quantity), float(price), int(cases_count), dispensed_id)
                )
                conn.commit()
                flash('تم تحديث سجل المنصرف بنجاح', 'success')
            except Exception as e:
                conn.rollback()
                flash(f'حدث خطأ أثناء تحديث السجل: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

        return redirect(url_for('dispense'))

    # عرض نموذج التعديل
    conn = get_db_connection()
    dispensed_item = conn.execute('''
        SELECT
            d.id,
            d.drug_id,
            d.clinic_id,
            c.name as clinic_name,
            dr.name as drug_name,
            dd.quantity,
            dd.price,
            dd.cases_count,
            d.dispense_month
        FROM dispensed d
        JOIN clinics c ON d.clinic_id = c.id
        JOIN drugs dr ON d.drug_id = dr.id
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        WHERE d.id = ?
    ''', (dispensed_id,)).fetchone()

    if not dispensed_item:
        conn.close()
        flash('سجل المنصرف غير موجود', 'danger')
        return redirect(url_for('dispense'))

    conn.close()
    return render_template('edit_dispensed.html', item=dispensed_item)

@app.route('/reports')
def reports():
    conn = get_db_connection()
    areas = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()

    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
    ''').fetchall()

    branches = conn.execute('SELECT * FROM branches').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    insulin_categories = conn.execute('SELECT * FROM insulin_categories').fetchall()
    conn.close()

    return render_template('reports_new.html', areas=areas, clinics=clinics, branches=branches, categories=categories, insulin_categories=insulin_categories)

@app.route('/reports/comparison')
def comparison_report():
    """تقرير مقارنة بين الفروع أو المناطق أو العيادات"""
    # الحصول على معلمات التقرير
    scope_type = request.args.get('scope_type', 'all')  # نوع النطاق (الكل، فرع، منطقة)
    parent_id = request.args.get('parent_id')  # معرف الفرع أو المنطقة
    date_range = request.args.get('date_range', 'month')  # الفترة الزمنية
    category_id = request.args.get('category_id')  # تصنيف الدواء (اختياري)

    # معلمات الفترة الزمنية المخصصة
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    conn = get_db_connection()

    try:
        # طباعة معلومات التشخيص
        print(f"DEBUG: comparison_report - scope_type={scope_type}, parent_id={parent_id}, date_range={date_range}, category_id={category_id}")

        # تحديد الفترة الزمنية
        today = datetime.now()
        period_desc = ""
        date_filter = ""
        date_params = []

        if date_range == 'month':
            # الشهر الحالي
            current_month = today.strftime('%Y-%m')
            date_filter = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            period_desc = f"شهر {current_month}"

        elif date_range == 'quarter':
            # الربع الحالي
            current_quarter = (today.month - 1) // 3 + 1
            start_month = (current_quarter - 1) * 3 + 1

            # إنشاء قائمة بالأشهر في الربع الحالي
            quarter_months = []
            for i in range(3):
                month_num = start_month + i
                year = today.year
                if month_num > 12:
                    month_num -= 12
                    year += 1
                quarter_months.append(f"{year}-{month_num:02d}")

            # بناء شرط SQL للربع
            date_filter = "AND ("
            for i, month in enumerate(quarter_months):
                if i > 0:
                    date_filter += " OR "
                date_filter += "strftime('%Y-%m', d.dispense_month) = ?"
                date_params.append(month)
            date_filter += ")"

            period_desc = f"الربع {current_quarter} من عام {today.year}"

        elif date_range == 'year':
            # السنة الحالية
            current_year = today.year
            date_filter = "AND strftime('%Y', d.dispense_month) = ?"
            date_params = [str(current_year)]
            period_desc = f"عام {current_year}"

        elif date_range == 'custom' and start_date and end_date:
            # فترة مخصصة - تحويل التواريخ إلى أشهر
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

                # إنشاء قائمة بالأشهر بين التاريخين
                months_between = []
                current_date = start_date_obj.replace(day=1)
                end_month = end_date_obj.replace(day=1)

                while current_date <= end_month:
                    months_between.append(current_date.strftime('%Y-%m'))
                    # الانتقال إلى الشهر التالي
                    if current_date.month == 12:
                        current_date = current_date.replace(year=current_date.year + 1, month=1)
                    else:
                        current_date = current_date.replace(month=current_date.month + 1)

                # بناء شرط SQL للفترة المخصصة
                if months_between:
                    date_filter = "AND ("
                    for i, month in enumerate(months_between):
                        if i > 0:
                            date_filter += " OR "
                        date_filter += "strftime('%Y-%m', d.dispense_month) = ?"
                        date_params.append(month)
                    date_filter += ")"
                else:
                    # إذا لم تكن هناك أشهر، استخدم شرطًا لن يتحقق أبدًا
                    date_filter = "AND 1=0"

                period_desc = f"من {start_date} إلى {end_date}"
            except ValueError:
                # في حالة حدوث خطأ في تنسيق التاريخ، استخدم الشهر الحالي
                current_month = today.strftime('%Y-%m')
                date_filter = "AND strftime('%Y-%m', d.dispense_month) = ?"
                date_params = [current_month]
                period_desc = f"شهر {current_month}"
        else:
            # افتراضي: الشهر الحالي
            current_month = today.strftime('%Y-%m')
            date_filter = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            period_desc = f"شهر {current_month}"

        # تحديد نطاق التقرير وبناء الاستعلام المناسب
        items = []
        parent_name = ""
        report_title = ""
        selected_category_name = "جميع التصنيفات"

        # تحديد تصنيف الدواء
        category_filter = ""
        category_params = []

        if category_id and str(category_id).strip():
            category_filter = "AND dr.category_id = ?"
            category_params = [category_id]

            # الحصول على اسم التصنيف
            category_result = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category_result:
                selected_category_name = category_result['name']

        # تحديد ما إذا كان سيتم تجميع البيانات حسب التصنيفات
        show_categories_breakdown = not (category_id and str(category_id).strip())  # إذا لم يتم اختيار تصنيف محدد، نعرض تفصيل التصنيفات

        # بناء الاستعلام حسب نوع النطاق
        if scope_type == 'all':
            # مقارنة بين الفروع
            report_title = "مقارنة بين الفروع"

            if show_categories_breakdown:
                # استعلام لعرض تفصيل التصنيفات لكل فرع
                query = f'''
                    SELECT
                        b.id as entity_id,
                        b.name as entity_name,
                        dc.id as category_id,
                        dc.name as category_name,
                        COUNT(DISTINCT dr.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as total_cost
                    FROM dispensed d
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN drug_categories dc ON dr.category_id = dc.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE 1=1
                    {date_filter}
                    GROUP BY b.id, b.name, dc.id, dc.name
                    ORDER BY b.name, total_cost DESC
                '''
            else:
                # استعلام عادي للمقارنة بين الفروع
                query = f'''
                    SELECT
                        b.id as entity_id,
                        b.name as entity_name,
                        NULL as category_id,
                        NULL as category_name,
                        COUNT(DISTINCT dr.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as total_cost
                    FROM dispensed d
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE 1=1
                    {date_filter}
                    {category_filter}
                    GROUP BY b.id, b.name
                    ORDER BY total_cost DESC
                '''

            query_params = date_params + ([] if show_categories_breakdown else category_params)

        elif scope_type == 'branch' and parent_id:
            # مقارنة بين المناطق داخل فرع محدد
            branch = conn.execute('SELECT name FROM branches WHERE id = ?', (parent_id,)).fetchone()
            if branch:
                parent_name = branch['name']
                report_title = f"مقارنة بين مناطق فرع {parent_name}"

            if show_categories_breakdown:
                # استعلام لعرض تفصيل التصنيفات لكل منطقة
                query = f'''
                    SELECT
                        a.id as entity_id,
                        a.name as entity_name,
                        dc.id as category_id,
                        dc.name as category_name,
                        COUNT(DISTINCT dr.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as total_cost
                    FROM dispensed d
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN drug_categories dc ON dr.category_id = dc.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE a.branch_id = ?
                    {date_filter}
                    GROUP BY a.id, a.name, dc.id, dc.name
                    ORDER BY a.name, total_cost DESC
                '''
            else:
                # استعلام عادي للمقارنة بين المناطق
                query = f'''
                    SELECT
                        a.id as entity_id,
                        a.name as entity_name,
                        NULL as category_id,
                        NULL as category_name,
                        COUNT(DISTINCT dr.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as total_cost
                    FROM dispensed d
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE a.branch_id = ?
                    {date_filter}
                    {category_filter}
                    GROUP BY a.id, a.name
                    ORDER BY total_cost DESC
                '''

            query_params = [parent_id] + date_params + ([] if show_categories_breakdown else category_params)

        elif scope_type == 'area' and parent_id:
            # مقارنة بين العيادات داخل منطقة محددة
            area = conn.execute('''
                SELECT a.name, b.name as branch_name
                FROM areas a
                JOIN branches b ON a.branch_id = b.id
                WHERE a.id = ?
            ''', (parent_id,)).fetchone()

            if area:
                parent_name = area['name']
                report_title = f"مقارنة بين عيادات منطقة {parent_name}"

            if show_categories_breakdown:
                # استعلام لعرض تفصيل التصنيفات لكل عيادة
                query = f'''
                    SELECT
                        c.id as entity_id,
                        c.name as entity_name,
                        dc.id as category_id,
                        dc.name as category_name,
                        COUNT(DISTINCT dr.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as total_cost
                    FROM dispensed d
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN drug_categories dc ON dr.category_id = dc.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE c.area_id = ?
                    {date_filter}
                    GROUP BY c.id, c.name, dc.id, dc.name
                    ORDER BY c.name, total_cost DESC
                '''
            else:
                # استعلام عادي للمقارنة بين العيادات
                query = f'''
                    SELECT
                        c.id as entity_id,
                        c.name as entity_name,
                        NULL as category_id,
                        NULL as category_name,
                        COUNT(DISTINCT dr.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as total_cost
                    FROM dispensed d
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE c.area_id = ?
                    {date_filter}
                    {category_filter}
                    GROUP BY c.id, c.name
                    ORDER BY total_cost DESC
                '''

            query_params = [parent_id] + date_params + ([] if show_categories_breakdown else category_params)

        else:
            # افتراضي: مقارنة بين الفروع
            report_title = "مقارنة بين الفروع"

            if show_categories_breakdown:
                # استعلام لعرض تفصيل التصنيفات لكل فرع
                query = f'''
                    SELECT
                        b.id as entity_id,
                        b.name as entity_name,
                        dc.id as category_id,
                        dc.name as category_name,
                        COUNT(DISTINCT dr.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as total_cost
                    FROM dispensed d
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN drug_categories dc ON dr.category_id = dc.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE 1=1
                    {date_filter}
                    GROUP BY b.id, b.name, dc.id, dc.name
                    ORDER BY b.name, total_cost DESC
                '''
            else:
                # استعلام عادي للمقارنة بين الفروع
                query = f'''
                    SELECT
                        b.id as entity_id,
                        b.name as entity_name,
                        NULL as category_id,
                        NULL as category_name,
                        COUNT(DISTINCT dr.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as total_cost
                    FROM dispensed d
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE 1=1
                    {date_filter}
                    {category_filter}
                    GROUP BY b.id, b.name
                    ORDER BY total_cost DESC
                '''

            query_params = date_params + ([] if show_categories_breakdown else category_params)

        # تنفيذ الاستعلام
        result = conn.execute(query, query_params).fetchall()

        # تحويل النتائج إلى قائمة من القواميس
        total_cost = 0

        if show_categories_breakdown:
            # تنظيم البيانات حسب الكيانات والتصنيفات
            entities_data = {}
            categories_data = {}

            for row in result:
                entity_id = row['entity_id']
                entity_name = row['entity_name']
                category_id = row['category_id']
                category_name = row['category_name']

                # إضافة الكيان إذا لم يكن موجودًا
                if entity_id not in entities_data:
                    entities_data[entity_id] = {
                        'id': entity_id,
                        'name': entity_name,
                        'categories': {},
                        'total_cost': 0,
                        'drugs_count': 0
                    }

                # إضافة التصنيف إذا لم يكن موجودًا
                if category_id not in categories_data:
                    categories_data[category_id] = {
                        'id': category_id,
                        'name': category_name,
                        'entities': {},
                        'total_cost': 0
                    }

                # إضافة بيانات التصنيف للكيان
                if category_id not in entities_data[entity_id]['categories']:
                    entities_data[entity_id]['categories'][category_id] = {
                        'name': category_name,
                        'total_cost': row['total_cost'],
                        'drugs_count': row['drugs_count']
                    }
                else:
                    entities_data[entity_id]['categories'][category_id]['total_cost'] += row['total_cost']
                    entities_data[entity_id]['categories'][category_id]['drugs_count'] += row['drugs_count']

                # إضافة بيانات الكيان للتصنيف
                if entity_id not in categories_data[category_id]['entities']:
                    categories_data[category_id]['entities'][entity_id] = {
                        'name': entity_name,
                        'total_cost': row['total_cost'],
                        'drugs_count': row['drugs_count']
                    }
                else:
                    categories_data[category_id]['entities'][entity_id]['total_cost'] += row['total_cost']
                    categories_data[category_id]['entities'][entity_id]['drugs_count'] += row['drugs_count']

                # تحديث إجماليات الكيان
                entities_data[entity_id]['total_cost'] += row['total_cost']
                entities_data[entity_id]['drugs_count'] += row['drugs_count']

                # تحديث إجماليات التصنيف
                categories_data[category_id]['total_cost'] += row['total_cost']

                # تحديث إجمالي التكلفة
                total_cost += row['total_cost']

            # تحويل القواميس إلى قوائم
            entities_list = list(entities_data.values())
            categories_list = list(categories_data.values())

            # ترتيب الكيانات حسب التكلفة الإجمالية (تنازلياً)
            entities_list.sort(key=lambda x: x['total_cost'], reverse=True)

            # ترتيب التصنيفات حسب التكلفة الإجمالية (تنازلياً)
            categories_list.sort(key=lambda x: x['total_cost'], reverse=True)

            # حساب النسب المئوية للكيانات
            for entity in entities_list:
                if total_cost > 0:
                    entity['percentage'] = round((entity['total_cost'] / total_cost) * 100, 1)
                else:
                    entity['percentage'] = 0

            # حساب النسب المئوية للتصنيفات
            for category in categories_list:
                if total_cost > 0:
                    category['percentage'] = round((category['total_cost'] / total_cost) * 100, 1)
                else:
                    category['percentage'] = 0

            # استخدام التصنيفات كعناصر رئيسية للمقارنة
            items = categories_list

            # إضافة بيانات الكيانات والتصنيفات للقالب
            comparison_data = {
                'entities': entities_list,
                'categories': categories_list,
                'show_categories_breakdown': True
            }

        else:
            # معالجة البيانات العادية (بدون تفصيل التصنيفات)
            items = []  # تعريف قائمة فارغة للعناصر
            for row in result:
                item = {
                    'id': row['entity_id'],
                    'name': row['entity_name'],
                    'drugs_count': row['drugs_count'],
                    'total_cost': row['total_cost']
                }
                items.append(item)
                total_cost += row['total_cost']

            # حساب النسب المئوية
            for item in items:
                if total_cost > 0:
                    item['percentage'] = round((item['total_cost'] / total_cost) * 100, 1)
                else:
                    item['percentage'] = 0

            # إضافة بيانات المقارنة للقالب
            comparison_data = {
                'entities': items,
                'categories': [],
                'show_categories_breakdown': False
            }

        # إضافة تصنيف الدواء إلى عنوان التقرير
        if category_id and str(category_id).strip():
            report_title += f" - تصنيف {selected_category_name}"
        else:
            report_title += " - جميع التصنيفات"

        conn.close()

        # إذا لم تكن هناك بيانات، عرض رسالة مناسبة
        if not items:
            message = "لا توجد بيانات للمقارنة في الفترة المحددة"
            if scope_type == 'branch' and parent_name:
                message += f" لفرع {parent_name}"
            elif scope_type == 'area' and parent_name:
                message += f" لمنطقة {parent_name}"

            if category_id and str(category_id).strip():
                message += f" لتصنيف {selected_category_name}"

            return render_template(
                'simple_report.html',
                title="تقرير المقارنة",
                message=message
            )

        return render_template(
            'comparison_report.html',
            items=items,
            total_cost=total_cost,
            report_title=report_title,
            period=period_desc,
            scope_type=scope_type,
            parent_name=parent_name,
            category_name=selected_category_name,
            comparison_data=comparison_data,
            show_categories_breakdown=show_categories_breakdown
        )

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in comparison_report: {e}")
        print(f"Error details: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/reports/monthly')
def monthly_report():
    """تقرير شهري للمنصرف حسب المنطقة"""
    month = request.args.get('month')
    area_id = request.args.get('area_id')
    category_id = request.args.get('category_id')  # تصنيف الدواء (اختياري)

    if not month or not area_id:
        flash('يرجى اختيار الشهر والمنطقة', 'warning')
        return redirect(url_for('reports'))

    conn = get_db_connection()

    try:
        # الحصول على معلومات المنطقة
        area = conn.execute('''
            SELECT a.id, a.name, b.name as branch_name
            FROM areas a
            JOIN branches b ON a.branch_id = b.id
            WHERE a.id = ?
        ''', (area_id,)).fetchone()

        if not area:
            flash('المنطقة غير موجودة', 'danger')
            return redirect(url_for('reports'))

        # بناء استعلام المنصرف مع مراعاة تصنيف الدواء إذا تم تحديده
        query_params = [area_id, month]
        category_filter = ""
        selected_category_name = "جميع التصنيفات"

        if category_id and str(category_id).strip():  # التحقق من أن معرف التصنيف ليس فارغًا
            category_filter = "AND dr.category_id = ?"
            query_params.append(category_id)

            # الحصول على اسم التصنيف
            category_result = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category_result:
                selected_category_name = category_result['name']
            else:
                selected_category_name = "غير معروف"

        # الحصول على بيانات المنصرف للمنطقة في الشهر المحدد مع تجميع حسب السعر والتصنيف
        report_data = conn.execute(f'''
            SELECT
                c.id as clinic_id,
                c.name as clinic_name,
                dr.name as drug_name,
                dc.id as category_id,
                dc.name as category_name,
                dd.price,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as total_cases,
                SUM(dd.quantity * dd.price) as total_cost
            FROM dispensed d
            JOIN clinics c ON d.clinic_id = c.id
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE c.area_id = ? AND strftime('%Y-%m', d.dispense_month) = ? {category_filter}
            GROUP BY c.id, c.name, dr.name, dc.id, dc.name, dd.price
            ORDER BY c.name, dc.name, dr.name, dd.price
        ''', query_params).fetchall()

        # تنظيم البيانات حسب العيادة والتصنيف
        clinics_data = {}
        total_area_cost = 0

        for row in report_data:
            clinic_id = row['clinic_id']
            clinic_name = row['clinic_name']
            drug_name = row['drug_name']
            category_id = row['category_id']
            category_name = row['category_name']
            price = row['price']

            # إضافة العيادة إذا لم تكن موجودة
            if clinic_id not in clinics_data:
                clinics_data[clinic_id] = {
                    'name': clinic_name,
                    'categories': {},
                    'total_cost': 0
                }

            # إضافة التصنيف إذا لم يكن موجودًا
            if category_id not in clinics_data[clinic_id]['categories']:
                clinics_data[clinic_id]['categories'][category_id] = {
                    'name': category_name,
                    'drugs': [],
                    'total_cost': 0
                }

            # البحث عن الدواء بنفس السعر في القائمة
            existing_drug = None
            for drug in clinics_data[clinic_id]['categories'][category_id]['drugs']:
                if drug['drug_name'] == drug_name and drug['price'] == price:
                    existing_drug = drug
                    break

            if existing_drug:
                # إذا وجد الدواء بنفس السعر، قم بتحديث البيانات
                existing_drug['quantity'] += row['total_quantity']
                existing_drug['cases'] += row['total_cases']
                existing_drug['cost'] += row['total_cost']
            else:
                # إذا لم يوجد، أضف سجل جديد
                clinics_data[clinic_id]['categories'][category_id]['drugs'].append({
                    'drug_name': drug_name,
                    'quantity': row['total_quantity'],
                    'price': price,
                    'cases': row['total_cases'],
                    'cost': row['total_cost']
                })

            # تحديث إجماليات التصنيف والعيادة
            clinics_data[clinic_id]['categories'][category_id]['total_cost'] += row['total_cost']
            clinics_data[clinic_id]['total_cost'] += row['total_cost']
            total_area_cost += row['total_cost']

        # تحويل القاموس إلى قائمة للعرض مع تنظيم التصنيفات
        clinics_list = []
        for clinic_id, clinic_data in clinics_data.items():
            # تحويل قاموس التصنيفات إلى قائمة
            categories_list = []
            for category_id, category_data in clinic_data['categories'].items():
                # ترتيب الأدوية في كل تصنيف حسب التكلفة
                category_data['drugs'].sort(key=lambda x: x['cost'], reverse=True)

                categories_list.append({
                    'name': category_data['name'],
                    'drugs': category_data['drugs'],
                    'total_cost': category_data['total_cost']
                })

            # ترتيب التصنيفات حسب التكلفة الإجمالية (تنازلياً)
            categories_list.sort(key=lambda x: x['total_cost'], reverse=True)

            clinics_list.append({
                'name': clinic_data['name'],
                'categories': categories_list,
                'total_cost': clinic_data['total_cost']
            })

        # ترتيب العيادات حسب التكلفة الإجمالية (تنازلياً)
        clinics_list.sort(key=lambda x: x['total_cost'], reverse=True)

        # إعداد بيانات إجمالي المنطقة حسب التصنيفات والأدوية
        area_categories_dict = {}

        # تجميع بيانات جميع العيادات حسب التصنيف
        for clinic in clinics_list:
            for category in clinic['categories']:
                category_name = category['name']

                # إضافة التصنيف إذا لم يكن موجودًا
                if category_name not in area_categories_dict:
                    area_categories_dict[category_name] = {
                        'name': category_name,
                        'drugs': {},
                        'total_cost': 0
                    }

                # تجميع الأدوية في كل تصنيف
                for drug in category['drugs']:
                    drug_name = drug['drug_name']
                    price = drug['price']

                    # إنشاء مفتاح فريد للدواء والسعر
                    drug_price_key = f"{drug_name}_{price}"

                    if drug_price_key in area_categories_dict[category_name]['drugs']:
                        # إذا وجد الدواء بنفس السعر، قم بتحديث البيانات
                        area_categories_dict[category_name]['drugs'][drug_price_key]['quantity'] += drug['quantity']
                        area_categories_dict[category_name]['drugs'][drug_price_key]['cases'] += drug['cases']
                        area_categories_dict[category_name]['drugs'][drug_price_key]['cost'] += drug['cost']
                    else:
                        # إذا لم يوجد، أضف سجل جديد
                        area_categories_dict[category_name]['drugs'][drug_price_key] = {
                            'drug_name': drug_name,
                            'price': price,
                            'quantity': drug['quantity'],
                            'cases': drug['cases'],
                            'cost': drug['cost']
                        }

                    # تحديث إجمالي تكلفة التصنيف
                    area_categories_dict[category_name]['total_cost'] += drug['cost']

        # تحويل القاموس إلى قائمة
        area_categories_list = []
        for category_name, category_data in area_categories_dict.items():
            # تحويل قاموس الأدوية إلى قائمة
            drugs_list = list(category_data['drugs'].values())

            # ترتيب الأدوية حسب التكلفة
            drugs_list.sort(key=lambda x: x['cost'], reverse=True)

            area_categories_list.append({
                'name': category_name,
                'drugs': drugs_list,
                'total_cost': category_data['total_cost']
            })

        # ترتيب التصنيفات حسب التكلفة الإجمالية (تنازلياً)
        area_categories_list.sort(key=lambda x: x['total_cost'], reverse=True)

        # إعداد قائمة إجمالي المنطقة (للتوافق مع الكود القديم)
        area_summary = []
        for category in area_categories_list:
            for drug in category['drugs']:
                area_summary.append(drug)

        conn.close()

        # إذا لم تكن هناك بيانات، عرض رسالة مناسبة
        if not clinics_list:
            message = f"لا توجد بيانات منصرف لمنطقة {area['name']} في شهر {month}"
            if category_id and str(category_id).strip():
                message += f" لتصنيف {selected_category_name}"

            return render_template(
                'simple_report.html',
                title="التقرير الشهري",
                message=message
            )

        # تنسيق الشهر للعرض
        try:
            year, month_num = month.split('-')
            month_names = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            }
            formatted_month = f"{month_names[month_num]} {year}"
        except:
            formatted_month = month

        # إعداد عنوان التقرير
        report_title = f"تقرير المنصرف الشهري - {area['name']}"
        if category_id and str(category_id).strip():
            report_title += f" - تصنيف {selected_category_name}"
        else:
            report_title += " - جميع التصنيفات"

        return render_template(
            'new_monthly_report.html',
            area_name=area['name'],
            branch_name=area['branch_name'],
            month=formatted_month,
            clinics=clinics_list,
            total_cost=total_area_cost,
            report_title=report_title,
            category_name=selected_category_name,
            area_summary=area_summary,
            area_categories=area_categories_list
        )

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in monthly_report: {e}")
        print(f"Error details: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

    finally:
        if conn:
            conn.close()

@app.route('/reports/drugs')
def drugs_report_new():
    """تقرير استهلاك الأدوية"""
    # الحصول على معلمات التقرير
    scope_type = request.args.get('scope_type', 'all')  # نطاق التقرير (الكل، فرع، منطقة، عيادة)
    date_range = request.args.get('date_range', 'month')  # الفترة الزمنية
    category_id = request.args.get('category_id')  # تصنيف الدواء (اختياري)

    # معلمات نطاق التقرير
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    # معلمات الفترة الزمنية المخصصة
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    conn = get_db_connection()

    try:
        # طباعة معلومات التشخيص
        print(f"DEBUG: drugs_report_new - scope_type={scope_type}, date_range={date_range}, category_id={category_id}")

        # تحديد الفترة الزمنية
        today = datetime.now()
        period_desc = ""
        date_filter = ""
        date_params = []

        if date_range == 'month':
            # الشهر الحالي
            current_month = today.strftime('%Y-%m')
            date_filter = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            period_desc = f"شهر {current_month}"

        elif date_range == 'quarter':
            # الربع الحالي
            current_quarter = (today.month - 1) // 3 + 1
            start_month = (current_quarter - 1) * 3 + 1

            # إنشاء قائمة بالأشهر في الربع الحالي
            quarter_months = []
            for i in range(3):
                month_num = start_month + i
                year = today.year
                if month_num > 12:
                    month_num -= 12
                    year += 1
                quarter_months.append(f"{year}-{month_num:02d}")

            # بناء شرط SQL للربع
            date_filter = "AND ("
            for i, month in enumerate(quarter_months):
                if i > 0:
                    date_filter += " OR "
                date_filter += "strftime('%Y-%m', d.dispense_month) = ?"
                date_params.append(month)
            date_filter += ")"

            period_desc = f"الربع {current_quarter} من عام {today.year}"

        elif date_range == 'year':
            # السنة الحالية
            current_year = today.year
            date_filter = "AND strftime('%Y', d.dispense_month) = ?"
            date_params = [str(current_year)]
            period_desc = f"عام {current_year}"

        elif date_range == 'custom' and start_date and end_date:
            # فترة مخصصة - تحويل التواريخ إلى أشهر
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

                # إنشاء قائمة بالأشهر بين التاريخين
                months_between = []
                current_date = start_date_obj.replace(day=1)
                end_month = end_date_obj.replace(day=1)

                while current_date <= end_month:
                    months_between.append(current_date.strftime('%Y-%m'))
                    # الانتقال إلى الشهر التالي
                    if current_date.month == 12:
                        current_date = current_date.replace(year=current_date.year + 1, month=1)
                    else:
                        current_date = current_date.replace(month=current_date.month + 1)

                # بناء شرط SQL للفترة المخصصة
                if months_between:
                    date_filter = "AND ("
                    for i, month in enumerate(months_between):
                        if i > 0:
                            date_filter += " OR "
                        date_filter += "strftime('%Y-%m', d.dispense_month) = ?"
                        date_params.append(month)
                    date_filter += ")"
                else:
                    # إذا لم تكن هناك أشهر، استخدم شرطًا لن يتحقق أبدًا
                    date_filter = "AND 1=0"

                period_desc = f"من {start_date} إلى {end_date}"
            except ValueError:
                # في حالة حدوث خطأ في تنسيق التاريخ، استخدم الشهر الحالي
                current_month = today.strftime('%Y-%m')
                date_filter = "AND strftime('%Y-%m', d.dispense_month) = ?"
                date_params = [current_month]
                period_desc = f"شهر {current_month}"
        else:
            # افتراضي: الشهر الحالي
            current_month = today.strftime('%Y-%m')
            date_filter = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            period_desc = f"شهر {current_month}"

        # تحديد نطاق التقرير
        scope_filter = ""
        scope_params = []
        scope_name = ""

        if scope_type == 'branch' and branch_id:
            # نطاق الفرع
            scope_filter = """
                AND c.area_id IN (
                    SELECT a.id FROM areas a
                    WHERE a.branch_id = ?
                )
            """
            scope_params = [branch_id]

            # الحصول على اسم الفرع
            branch = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
            if branch:
                scope_name = branch['name']

        elif scope_type == 'area' and area_id:
            # نطاق المنطقة
            scope_filter = "AND c.area_id = ?"
            scope_params = [area_id]

            # الحصول على اسم المنطقة
            area = conn.execute('SELECT name FROM areas WHERE id = ?', (area_id,)).fetchone()
            if area:
                scope_name = area['name']

        elif scope_type == 'clinic' and clinic_id:
            # نطاق العيادة
            scope_filter = "AND d.clinic_id = ?"
            scope_params = [clinic_id]

            # الحصول على اسم العيادة
            clinic = conn.execute('SELECT name FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
            if clinic:
                scope_name = clinic['name']

        # تحديد تصنيف الدواء
        category_filter = ""
        category_params = []
        category_name = ""

        if category_id:
            category_filter = "AND dr.category_id = ?"
            category_params = [category_id]

            # الحصول على اسم التصنيف
            category = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category:
                category_name = category['name']

        # بناء استعلام الأدوية
        query_params = date_params + scope_params + category_params

        drugs_query = f'''
            SELECT
                dr.id,
                dr.name,
                dc.name as category_name,
                dd.price,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as total_cases,
                SUM(dd.quantity * dd.price) as total_cost
            FROM dispensed d
            JOIN clinics c ON d.clinic_id = c.id
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE 1=1
            {date_filter}
            {scope_filter}
            {category_filter}
            GROUP BY dr.id, dr.name, dc.name, dd.price
            ORDER BY dr.name, dd.price
        '''

        drugs_result = conn.execute(drugs_query, query_params).fetchall()

        # تنظيم البيانات حسب التصنيف
        categories_data = {}
        total_cost = 0

        for row in drugs_result:
            drug_id = row['id']
            drug_name = row['name']
            category_name = row['category_name']
            price = row['price']

            # إنشاء مفتاح فريد للدواء والسعر
            drug_price_key = f"{drug_id}_{price}"

            # إضافة التصنيف إذا لم يكن موجودًا
            if category_name not in categories_data:
                categories_data[category_name] = {
                    'name': category_name,
                    'drugs': {},
                    'total_cost': 0
                }

            # إضافة الدواء إلى التصنيف أو تحديثه
            if drug_price_key in categories_data[category_name]['drugs']:
                # إذا وجد الدواء بنفس السعر، قم بتحديث البيانات
                categories_data[category_name]['drugs'][drug_price_key]['total_quantity'] += row['total_quantity']
                categories_data[category_name]['drugs'][drug_price_key]['total_cases'] += row['total_cases']
                categories_data[category_name]['drugs'][drug_price_key]['total_cost'] += row['total_cost']
            else:
                # إذا لم يوجد، أضف سجل جديد
                categories_data[category_name]['drugs'][drug_price_key] = {
                    'id': drug_id,
                    'name': drug_name,
                    'price': price,
                    'total_quantity': row['total_quantity'],
                    'total_cases': row['total_cases'],
                    'total_cost': row['total_cost']
                }

            # تحديث إجمالي التصنيف
            categories_data[category_name]['total_cost'] += row['total_cost']
            total_cost += row['total_cost']

        # تحويل القواميس إلى قوائم للعرض
        categories_list = []
        for category_name, category_data in categories_data.items():
            # تحويل قاموس الأدوية إلى قائمة
            drugs_list = list(category_data['drugs'].values())

            categories_list.append({
                'name': category_name,
                'drugs': drugs_list,
                'total_cost': category_data['total_cost']
            })

        # ترتيب التصنيفات حسب التكلفة الإجمالية (تنازلياً)
        categories_list.sort(key=lambda x: x['total_cost'], reverse=True)

        # إعداد عنوان التقرير
        report_title = "تقرير استهلاك الأدوية"
        if scope_type != 'all' and scope_name:
            if scope_type == 'branch':
                report_title += f" - فرع {scope_name}"
            elif scope_type == 'area':
                report_title += f" - منطقة {scope_name}"
            elif scope_type == 'clinic':
                report_title += f" - عيادة {scope_name}"

        if category_id and str(category_id).strip():
            report_title += f" - تصنيف {category_name}"
        else:
            report_title += " - جميع التصنيفات"

        conn.close()

        # إذا لم تكن هناك بيانات، عرض رسالة مناسبة
        if not categories_list:
            message = "لا توجد بيانات للأدوية في الفترة المحددة"
            if scope_type != 'all' and scope_name:
                if scope_type == 'branch':
                    message += f" لفرع {scope_name}"
                elif scope_type == 'area':
                    message += f" لمنطقة {scope_name}"
                elif scope_type == 'clinic':
                    message += f" لعيادة {scope_name}"

            if category_name:
                message += f" لتصنيف {category_name}"

            return render_template(
                'simple_report.html',
                title="تقرير الأدوية",
                message=message
            )

        return render_template(
            'new_drugs_report.html',
            categories=categories_list,
            total_cost=total_cost,
            report_title=report_title,
            scope_type=scope_type,
            scope_name=scope_name,
            period=period_desc,
            category_name=category_name if category_id and str(category_id).strip() else "جميع التصنيفات"
        )

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in drugs_report: {e}")
        print(f"Error details: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

    finally:
        if conn:
            conn.close()

@app.route('/reports/clinics')
def clinics_report():
    """تقرير منصرف العيادة حسب التصنيفات"""
    clinic_id = request.args.get('clinic_id')
    month = request.args.get('month')
    category_id = request.args.get('category_id')  # تصنيف الدواء (اختياري)

    if not clinic_id or not month:
        flash('يرجى اختيار العيادة والشهر', 'warning')
        return redirect(url_for('reports'))

    conn = get_db_connection()

    try:
        # الحصول على معلومات العيادة
        clinic = conn.execute('''
            SELECT
                c.id, c.name,
                a.name as area_name,
                b.name as branch_name
            FROM clinics c
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            WHERE c.id = ?
        ''', (clinic_id,)).fetchone()

        if not clinic:
            flash('العيادة غير موجودة', 'danger')
            return redirect(url_for('reports'))

        # بناء استعلام المنصرف مع مراعاة تصنيف الدواء إذا تم تحديده
        query_params = [clinic_id, month]
        category_filter = ""
        selected_category_name = None

        if category_id and category_id.strip():  # التحقق من أن معرف التصنيف ليس فارغًا
            category_filter = "AND dc.id = ?"
            query_params.append(category_id)

            # الحصول على اسم التصنيف
            category_result = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category_result:
                selected_category_name = category_result['name']
            else:
                selected_category_name = "غير معروف"

        # الحصول على بيانات المنصرف للعيادة في الشهر المحدد مصنفة حسب التصنيف والسعر
        report_data = conn.execute(f'''
            SELECT
                dc.id as category_id,
                dc.name as category_name,
                dr.name as drug_name,
                dd.price,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as total_cases,
                SUM(dd.quantity * dd.price) as total_cost
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE d.clinic_id = ? AND strftime('%Y-%m', d.dispense_month) = ? {category_filter}
            GROUP BY dc.id, dc.name, dr.name, dd.price
            ORDER BY dc.name, dr.name, dd.price
        ''', query_params).fetchall()

        # تنظيم البيانات حسب التصنيف
        categories_data = {}
        total_clinic_cost = 0

        for row in report_data:
            category_id = row['category_id']
            category_name = row['category_name']
            drug_name = row['drug_name']
            price = row['price']

            if category_id not in categories_data:
                categories_data[category_id] = {
                    'name': category_name,
                    'drugs': [],
                    'total_cost': 0
                }

            # تجميع البيانات حسب الدواء والسعر

            # البحث عن الدواء بنفس السعر في القائمة
            existing_drug = None
            for drug in categories_data[category_id]['drugs']:
                if drug['drug_name'] == drug_name and drug['price'] == price:
                    existing_drug = drug
                    break

            if existing_drug:
                # إذا وجد الدواء بنفس السعر، قم بتحديث البيانات
                existing_drug['quantity'] += row['total_quantity']
                existing_drug['cases'] += row['total_cases']
                existing_drug['cost'] += row['total_cost']
            else:
                # إذا لم يوجد، أضف سجل جديد
                categories_data[category_id]['drugs'].append({
                    'drug_name': drug_name,
                    'quantity': row['total_quantity'],
                    'price': price,
                    'cases': row['total_cases'],
                    'cost': row['total_cost']
                })

            categories_data[category_id]['total_cost'] += row['total_cost']
            total_clinic_cost += row['total_cost']

        # تحويل القاموس إلى قائمة للعرض
        categories_list = []
        for category_id, category_data in categories_data.items():
            categories_list.append({
                'name': category_data['name'],
                'drugs': category_data['drugs'],
                'total_cost': category_data['total_cost']
            })

        # ترتيب التصنيفات حسب التكلفة الإجمالية (تنازلياً)
        categories_list.sort(key=lambda x: x['total_cost'], reverse=True)

        conn.close()

        # إذا لم تكن هناك بيانات، عرض رسالة مناسبة
        if not categories_list:
            message = f"لا توجد بيانات منصرف للعيادة {clinic['name']} في شهر {month}"
            if category_id:
                message += f" لتصنيف {selected_category_name}"

            return render_template(
                'simple_report.html',
                title="تقرير العيادة",
                message=message
            )

        # تنسيق الشهر للعرض
        try:
            year, month_num = month.split('-')
            month_names = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            }
            formatted_month = f"{month_names[month_num]} {year}"
        except:
            formatted_month = month

        # إعداد عنوان التقرير
        report_title = f"تقرير منصرف العيادة - {clinic['name']}"
        if category_id and str(category_id).strip():
            report_title += f" - تصنيف {selected_category_name}"
        else:
            report_title += " - جميع التصنيفات"

        return render_template(
            'new_clinics_report.html',
            clinic_name=clinic['name'],
            area_name=clinic['area_name'],
            branch_name=clinic['branch_name'],
            month=formatted_month,
            categories=categories_list,
            total_cost=total_clinic_cost,
            report_title=report_title,
            category_name=selected_category_name if category_id and str(category_id).strip() else "جميع التصنيفات"
        )

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in clinics_report: {e}")
        print(f"Error details: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

    finally:
        if conn:
            conn.close()

@app.route('/reports/drugs_old')
def drugs_report():
    category_id = request.args.get('category_id')
    date_range = request.args.get('date_range')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    if not date_range:
        flash('يرجى اختيار الفترة الزمنية', 'warning')
        return redirect(url_for('reports'))

    conn = get_db_connection()

    # تحديد نطاق التاريخ
    from datetime import datetime, timedelta
    today = datetime.now()
    if date_range == 'month':
        start_date = datetime(today.year, today.month, 1).strftime('%Y-%m-%d')
        # تعامل مع شهر ديسمبر
        if today.month == 12:
            end_date = datetime(today.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(today.year, today.month + 1, 1) - timedelta(days=1)
        end_date = end_date.strftime('%Y-%m-%d')
        date_range_text = f"الشهر الحالي ({today.strftime('%Y-%m')})"
    elif date_range == 'quarter':
        quarter = (today.month - 1) // 3 + 1
        start_date = datetime(today.year, (quarter - 1) * 3 + 1, 1).strftime('%Y-%m-%d')
        # تعامل مع الربع الرابع
        if quarter == 4:
            end_date = datetime(today.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(today.year, quarter * 3 + 1, 1) - timedelta(days=1)
        end_date = end_date.strftime('%Y-%m-%d')
        date_range_text = f"الربع الحالي (Q{quarter} {today.year})"
    elif date_range == 'year':
        start_date = datetime(today.year, 1, 1).strftime('%Y-%m-%d')
        end_date = datetime(today.year, 12, 31).strftime('%Y-%m-%d')
        date_range_text = f"السنة الحالية ({today.year})"
    elif date_range == 'custom':
        if not start_date or not end_date:
            conn.close()
            flash('يرجى تحديد تاريخ البداية والنهاية للفترة المخصصة', 'warning')
            return redirect(url_for('reports'))
        date_range_text = f"الفترة من {start_date} إلى {end_date}"
    else:
        conn.close()
        flash('نطاق تاريخ غير صالح', 'danger')
        return redirect(url_for('reports'))

    # بناء الاستعلام
    query = '''
        SELECT
            dr.id as drug_id,
            dr.name as drug_name,
            dc.name as category_name,
            SUM(dd.quantity) as total_quantity,
            SUM(dd.cases_count) as total_cases,
            SUM(dd.quantity * dd.price) as total_cost
        FROM dispensed d
        JOIN drugs dr ON d.drug_id = dr.id
        JOIN drug_categories dc ON dr.category_id = dc.id
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        WHERE d.dispense_month BETWEEN ? AND ?
    '''

    params = [start_date, end_date]

    if category_id:
        query += ' AND dr.category_id = ?'
        params.append(category_id)
        # الحصول على اسم التصنيف
        category = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
        if category:
            category_name = category['name']
        else:
            category_name = 'غير معروف'
    else:
        category_name = 'جميع التصنيفات'

    query += ' GROUP BY dr.id, dr.name, dc.name ORDER BY dc.name, dr.name'

    # تنفيذ الاستعلام
    drugs_data = conn.execute(query, params).fetchall()

    # تنظيم البيانات حسب التصنيف
    categories_data = {}
    total_cost = 0
    total_cases = 0

    for drug in drugs_data:
        category_name_from_db = drug['category_name']
        if category_name_from_db not in categories_data:
            categories_data[category_name_from_db] = {
                'drugs': [],
                'total': 0,
                'cases': 0
            }

        categories_data[category_name_from_db]['drugs'].append({
            'name': drug['drug_name'],
            'quantity': drug['total_quantity'],
            'cases': drug['total_cases'],
            'cost': drug['total_cost']
        })

        categories_data[category_name_from_db]['total'] += drug['total_cost']
        categories_data[category_name_from_db]['cases'] += drug['total_cases']
        total_cost += drug['total_cost']
        total_cases += drug['total_cases']

    conn.close()

    response = make_response(render_template(
        'drugs_report_new.html',
        category_name=category_name,
        date_range_text=date_range_text,
        categories_data=categories_data,
        total_cost=total_cost,
        total_cases=total_cases,
        current_year=datetime.now().year
    ))

    # إضافة رؤوس HTTP لمنع التخزين المؤقت
    response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

@app.route('/pdf_button_test')
def pdf_button_test():
    """صفحة اختبار لزر PDF"""
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>اختبار زر PDF</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                margin: 0;
                background-color: #f5f5f5;
            }
            .pdf-button {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                border: none;
                background-color: #e74c3c;
                color: white;
                font-size: 24px;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            .pdf-text {
                font-family: Arial, sans-serif;
                font-weight: bold;
                font-size: 20px;
                color: white;
                text-align: center;
                display: block;
                line-height: 1;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                letter-spacing: 0.5px;
            }
        </style>
    </head>
    <body>
        <button class="pdf-button">
            <span class="pdf-text">PDF</span>
        </button>
    </body>
    </html>
    '''

@app.route('/reports/cost')
def cost_report():
    branch_id = request.args.get('branch_id')
    year = request.args.get('year')

    if not year:
        flash('يرجى اختيار السنة', 'warning')
        return redirect(url_for('reports'))

    conn = get_db_connection()

    # بناء الاستعلام
    query = '''
        SELECT
            strftime('%m', d.dispense_month) as month,
            SUM(dd.quantity * dd.price) as total_cost
        FROM dispensed d
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        JOIN clinics c ON d.clinic_id = c.id
        JOIN areas a ON c.area_id = a.id
        WHERE strftime('%Y', d.dispense_month) = ?
    '''

    params = [year]

    if branch_id:
        query += ' AND a.branch_id = ?'
        params.append(branch_id)
        # الحصول على اسم الفرع
        branch = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
        if branch:
            branch_name = branch['name']
        else:
            branch_name = 'غير معروف'
    else:
        branch_name = 'جميع الفروع'

    query += ' GROUP BY month ORDER BY month'

    # تنفيذ الاستعلام
    monthly_costs = conn.execute(query, params).fetchall()

    # تحويل البيانات إلى تنسيق مناسب للتقرير
    months_data = {}
    total_cost = 0

    # تهيئة جميع الشهور
    for i in range(1, 13):
        month_str = f"{i:02d}"
        months_data[month_str] = 0

    # ملء البيانات
    for item in monthly_costs:
        month = item['month']
        cost = item['total_cost']
        months_data[month] = cost
        total_cost += cost

    # تحويل أسماء الشهور إلى العربية
    arabic_months = {
        '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
        '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
        '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
    }

    formatted_data = []
    for month, cost in months_data.items():
        formatted_data.append({
            'month': arabic_months[month],
            'month_num': month,
            'cost': cost
        })

    conn.close()

    # formatted_data هو بالفعل قائمة، لذلك لا نحتاج إلى تحويله
    return render_template(
        'cost_report.html',
        branch_name=branch_name,
        year=year,
        months_data=formatted_data,
        total_cost=total_cost
    )

@app.route('/reports/insulin')
def insulin_report():
    """تقرير منصرف الأنسولين"""
    # الحصول على معلمات التقرير
    scope_type = request.args.get('scope_type', 'all')  # نوع النطاق (الكل، فرع، منطقة، عيادة)
    date_range = request.args.get('date_range', 'month')  # الفترة الزمنية
    category = request.args.get('category')  # فئة الأنسولين (اختياري)
    group_by = request.args.get('group_by', 'name_price')  # طريقة تجميع البيانات (حسب الصنف والسعر، حسب الفئة، حسب النوع)

    # معلمات نطاق التقرير
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    # معلمات الفترة الزمنية المخصصة
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    conn = get_db_connection()

    try:
        # تحديد الفترة الزمنية
        today = datetime.now()
        date_filter = ""
        date_params = []
        period_desc = ""

        if date_range == 'month':
            # الشهر الحالي
            start_date = datetime(today.year, today.month, 1).strftime('%Y-%m-%d')
            end_date = (datetime(today.year, today.month + 1, 1) - timedelta(days=1)).strftime('%Y-%m-%d')
            date_filter = "AND date(i.dispense_month) BETWEEN ? AND ?"
            date_params = [start_date, end_date]
            period_desc = f"الشهر الحالي ({today.strftime('%Y-%m')})"
        elif date_range == 'quarter':
            # الربع الحالي
            quarter = (today.month - 1) // 3 + 1
            start_date = datetime(today.year, (quarter - 1) * 3 + 1, 1).strftime('%Y-%m-%d')
            if quarter == 4:
                end_date = datetime(today.year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(today.year, quarter * 3 + 1, 1) - timedelta(days=1)
            end_date = end_date.strftime('%Y-%m-%d')
            date_filter = "AND date(i.dispense_month) BETWEEN ? AND ?"
            date_params = [start_date, end_date]
            period_desc = f"الربع الحالي (Q{quarter} {today.year})"
        elif date_range == 'year':
            # السنة الحالية
            start_date = datetime(today.year, 1, 1).strftime('%Y-%m-%d')
            end_date = datetime(today.year, 12, 31).strftime('%Y-%m-%d')
            date_filter = "AND date(i.dispense_month) BETWEEN ? AND ?"
            date_params = [start_date, end_date]
            period_desc = f"السنة الحالية ({today.year})"
        elif date_range == 'custom':
            # فترة مخصصة
            if not start_date or not end_date:
                conn.close()
                flash('يرجى تحديد تاريخ البداية والنهاية للفترة المخصصة', 'warning')
                return redirect(url_for('reports'))
            date_filter = "AND date(i.dispense_month) BETWEEN ? AND ?"
            date_params = [start_date, end_date]
            period_desc = f"الفترة من {start_date} إلى {end_date}"
        else:
            conn.close()
            flash('نطاق تاريخ غير صالح', 'danger')
            return redirect(url_for('reports'))

        # تحديد نطاق التقرير
        scope_filter = ""
        scope_params = []
        scope_name = ""
        scope_type_text = ""
        show_location = True
        location_type = ""

        if scope_type == 'branch' and branch_id:
            # تقرير لفرع محدد
            branch = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
            if branch:
                scope_name = branch['name']
                scope_filter = "AND a.branch_id = ?"
                scope_params = [branch_id]
                scope_type_text = "الفرع"
                location_type = "المنطقة"
            else:
                conn.close()
                flash('الفرع غير موجود', 'danger')
                return redirect(url_for('reports'))
        elif scope_type == 'area' and area_id:
            # تقرير لمنطقة محددة
            area = conn.execute('''
                SELECT a.name, b.name as branch_name
                FROM areas a
                JOIN branches b ON a.branch_id = b.id
                WHERE a.id = ?
            ''', (area_id,)).fetchone()
            if area:
                scope_name = f"{area['name']} ({area['branch_name']})"
                scope_filter = "AND i.area_id = ?"
                scope_params = [area_id]
                scope_type_text = "المنطقة"
                location_type = "العيادة"
            else:
                conn.close()
                flash('المنطقة غير موجودة', 'danger')
                return redirect(url_for('reports'))
        elif scope_type == 'clinic' and clinic_id:
            # تقرير لعيادة محددة
            clinic = conn.execute('''
                SELECT c.name, a.name as area_name, b.name as branch_name
                FROM clinics c
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE c.id = ?
            ''', (clinic_id,)).fetchone()
            if clinic:
                scope_name = f"{clinic['name']} ({clinic['area_name']} - {clinic['branch_name']})"
                scope_filter = "AND i.clinic_id = ?"
                scope_params = [clinic_id]
                scope_type_text = "العيادة"
                show_location = False
            else:
                conn.close()
                flash('العيادة غير موجودة', 'danger')
                return redirect(url_for('reports'))
        else:
            # تقرير لجميع الفروع
            scope_type_text = "جميع الفروع"
            location_type = "الفرع"

        # تحديد فئة الأنسولين
        category_filter = ""
        category_params = []

        if category:
            category_filter = "AND i.category = ?"
            category_params = [category]

        # تحديد طريقة تجميع البيانات
        group_by_fields = ""
        order_by_fields = ""
        select_fields = ""

        if group_by == 'category':
            # تجميع حسب الفئة
            group_by_fields = "i.category, i.name, i.type, i.unit, i.price"
            order_by_fields = "i.category, i.name, i.price"
            report_title_prefix = "تقرير منصرف الأنسولين (مجمع حسب الفئة)"
        elif group_by == 'type':
            # تجميع حسب النوع
            group_by_fields = "i.type, i.name, i.category, i.unit, i.price"
            order_by_fields = "i.type, i.name, i.price"
            report_title_prefix = "تقرير منصرف الأنسولين (مجمع حسب النوع)"
        else:
            # تجميع حسب الصنف والسعر (الافتراضي)
            group_by_fields = "i.name, i.type, i.unit, i.category, i.price, i.rate, i.balance"
            order_by_fields = "i.name, i.price"
            report_title_prefix = "تقرير منصرف الأنسولين"

        # بناء عنوان التقرير
        report_title = report_title_prefix
        if scope_name:
            report_title += f" - {scope_name}"
        if category:
            report_title += f" - فئة {category}"

        # إضافة حقل الموقع إلى حقول التجميع إذا كان مطلوبًا
        location_field = f'''CASE
                WHEN '{scope_type}' = 'branch' THEN a.name
                WHEN '{scope_type}' = 'area' THEN c.name
                WHEN '{scope_type}' = 'all' THEN b.name
                ELSE NULL
            END'''

        # استعلام البيانات
        query = f'''
            SELECT
                i.name,
                i.type,
                i.unit,
                i.category,
                i.price,
                i.rate,
                i.balance,
                SUM(i.quantity) as quantity,
                SUM(i.cases_count) as cases_count,
                SUM(i.cost) as cost,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name,
                {location_field} as location_name
            FROM insulin_dispensed i
            JOIN clinics c ON i.clinic_id = c.id
            JOIN areas a ON i.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            WHERE 1=1
            {date_filter}
            {scope_filter}
            {category_filter}
            GROUP BY {group_by_fields},
                    {location_field}
            ORDER BY {order_by_fields}
        '''

        insulin_items = conn.execute(query, date_params + scope_params + category_params).fetchall()

        # حساب إجمالي التكلفة وعدد الحالات
        total_cost = sum(item['cost'] for item in insulin_items)
        total_cases = sum(item['cases_count'] for item in insulin_items)

        # تجميع البيانات حسب الفئة
        category_data = {}
        for item in insulin_items:
            cat = item['category']
            if cat not in category_data:
                category_data[cat] = {'name': cat, 'cost': 0}
            category_data[cat]['cost'] += item['cost']

        # تجميع البيانات حسب النوع
        type_data = {}
        for item in insulin_items:
            type_name = item['type']
            if type_name not in type_data:
                type_data[type_name] = {'name': type_name, 'cost': 0}
            type_data[type_name]['cost'] += item['cost']

        # تحويل البيانات إلى قوائم للرسوم البيانية
        category_data_list = list(category_data.values())
        type_data_list = list(type_data.values())

        # ترتيب البيانات حسب التكلفة
        category_data_list.sort(key=lambda x: x['cost'], reverse=True)
        type_data_list.sort(key=lambda x: x['cost'], reverse=True)

        conn.close()

        # إذا لم تكن هناك بيانات، عرض رسالة مناسبة
        if not insulin_items:
            message = "لا توجد بيانات للأنسولين في الفترة المحددة"
            if scope_type != 'all' and scope_name:
                if scope_type == 'branch':
                    message += f" لفرع {scope_name}"
                elif scope_type == 'area':
                    message += f" لمنطقة {scope_name}"
                elif scope_type == 'clinic':
                    message += f" لعيادة {scope_name}"

            if category:
                message += f" لفئة {category}"

            return render_template(
                'simple_report.html',
                title="تقرير الأنسولين",
                message=message
            )

        return render_template(
            'insulin_report.html',
            insulin_items=insulin_items,
            total_cost=total_cost,
            total_cases=total_cases,
            report_title=report_title,
            scope_type=scope_type,
            scope_name=scope_name,
            scope_type_text=scope_type_text,
            period=period_desc,
            category=category,
            show_location=show_location,
            location_type=location_type,
            category_data=category_data_list,
            type_data=type_data_list,
            group_by=group_by,
            now=datetime.now()
        )

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in insulin_report: {e}")
        print(f"Error details: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

    finally:
        if conn:
            conn.close()

@app.route('/reports/drug_groups')
def drug_groups_report():
    """تقرير تكلفة المجموعات الدوائية"""
    # الحصول على معايير التقرير
    scope_type = request.args.get('scope_type', 'all')
    month = request.args.get('month')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    if not month:
        flash('يرجى اختيار الشهر', 'warning')
        return redirect(url_for('reports'))

    # تحويل الشهر إلى تاريخ
    try:
        month_date = datetime.strptime(month, '%Y-%m')
        month_name = month_date.strftime('%B %Y')
    except:
        flash('صيغة الشهر غير صحيحة', 'danger')
        return redirect(url_for('reports'))

    # الاتصال بقاعدة البيانات
    conn = get_db_connection()

    try:
        # تحديد عنوان التقرير ونطاقه
        report_title = "تقرير تكلفة المجموعات الدوائية"
        scope_desc = "جميع الفروع"

        # الحصول على معلومات النطاق
        if scope_type == 'branch' and branch_id:
            branch = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
            if branch:
                scope_desc = f"فرع {branch['name']}"
                report_title = f"تقرير تكلفة المجموعات الدوائية - {scope_desc}"
        elif scope_type == 'area' and area_id:
            area = conn.execute('''
                SELECT a.name, b.name as branch_name
                FROM areas a
                JOIN branches b ON a.branch_id = b.id
                WHERE a.id = ?
            ''', (area_id,)).fetchone()
            if area:
                scope_desc = f"منطقة {area['name']} ({area['branch_name']})"
                report_title = f"تقرير تكلفة المجموعات الدوائية - {scope_desc}"
        elif scope_type == 'clinic' and clinic_id:
            clinic = conn.execute('''
                SELECT c.name, a.name as area_name, b.name as branch_name
                FROM clinics c
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE c.id = ?
            ''', (clinic_id,)).fetchone()
            if clinic:
                scope_desc = f"عيادة {clinic['name']} ({clinic['area_name']} - {clinic['branch_name']})"
                report_title = f"تقرير تكلفة المجموعات الدوائية - {scope_desc}"

        # الحصول على جميع مجموعات الأدوية
        all_groups = conn.execute('SELECT id, code, name FROM drug_group_codes ORDER BY name').fetchall()

        # تحضير قائمة المجموعات
        groups = []
        for group in all_groups:
            groups.append({
                'id': group['id'],
                'code': group['code'],
                'name': group['name'],
                'drugs_count': 0,
                'total_cost': 0,
                'percentage': 0
            })

        # استعلام لاستخراج تكلفة كل مجموعة حسب النطاق
        if scope_type == 'all':
            # استعلام لجميع الفروع
            cost_query = '''
                SELECT
                    group_code_id,
                    COUNT(DISTINCT id) as drugs_count,
                    SUM(cost) as total_cost
                FROM drug_groups
                WHERE strftime('%Y-%m', dispense_month) = ?
                GROUP BY group_code_id
            '''
            cost_params = [month]
        elif scope_type == 'branch' and branch_id:
            # استعلام لفرع محدد
            cost_query = '''
                SELECT
                    dg.group_code_id,
                    COUNT(DISTINCT dg.id) as drugs_count,
                    SUM(dg.cost) as total_cost
                FROM drug_groups dg
                JOIN areas a ON dg.area_id = a.id
                WHERE strftime('%Y-%m', dg.dispense_month) = ?
                AND a.branch_id = ?
                GROUP BY dg.group_code_id
            '''
            cost_params = [month, branch_id]
        elif scope_type == 'area' and area_id:
            # استعلام لمنطقة محددة
            cost_query = '''
                SELECT
                    group_code_id,
                    COUNT(DISTINCT id) as drugs_count,
                    SUM(cost) as total_cost
                FROM drug_groups
                WHERE strftime('%Y-%m', dispense_month) = ?
                AND area_id = ?
                GROUP BY group_code_id
            '''
            cost_params = [month, area_id]
        elif scope_type == 'clinic' and clinic_id:
            # استعلام لعيادة محددة
            cost_query = '''
                SELECT
                    group_code_id,
                    COUNT(DISTINCT id) as drugs_count,
                    SUM(cost) as total_cost
                FROM drug_groups
                WHERE strftime('%Y-%m', dispense_month) = ?
                AND clinic_id = ?
                GROUP BY group_code_id
            '''
            cost_params = [month, clinic_id]

        # طباعة الاستعلام والمعلمات للتشخيص
        print(f"Cost query: {cost_query}")
        print(f"Cost params: {cost_params}")

        # تنفيذ استعلام التكلفة
        cost_results = conn.execute(cost_query, cost_params).fetchall()
        print(f"Cost results count: {len(cost_results)}")

        # طباعة نتائج استعلام التكلفة للتشخيص
        for i, result in enumerate(cost_results):
            print(f"Cost result {i+1}: {dict(result)}")

        # تحديث بيانات المجموعات بالتكلفة وعدد الأدوية
        total_cost = 0
        for result in cost_results:
            try:
                group_id = result['group_code_id'] if result['group_code_id'] is not None else 0
                drugs_count = result['drugs_count'] if result['drugs_count'] is not None else 0
                group_cost = result['total_cost'] if result['total_cost'] is not None else 0

                print(f"Processing group_id={group_id}, drugs_count={drugs_count}, group_cost={group_cost}")

                # البحث عن المجموعة في القائمة
                for group in groups:
                    if group['id'] == group_id:
                        group['drugs_count'] = drugs_count
                        group['total_cost'] = group_cost
                        total_cost += group_cost
                        print(f"Updated group {group['name']}, total_cost now: {total_cost}")
                        break
            except Exception as e:
                print(f"Error processing cost result: {e}")
                print(f"Result: {dict(result)}")

        print(f"Final total_cost: {total_cost}")

        # حساب النسب المئوية وترتيب المجموعات حسب التكلفة
        try:
            if total_cost > 0:
                for group in groups:
                    try:
                        group['percentage'] = round((group['total_cost'] / total_cost) * 100, 1)
                    except Exception as e:
                        print(f"Error calculating percentage for group {group['name']}: {e}")
                        print(f"Group total_cost: {group['total_cost']}, total_cost: {total_cost}")
                        group['percentage'] = 0
            else:
                print("Total cost is zero, setting all percentages to zero")
                for group in groups:
                    group['percentage'] = 0
        except Exception as e:
            print(f"Error in percentage calculation: {e}")
            for group in groups:
                group['percentage'] = 0

        # ترتيب المجموعات حسب التكلفة تنازليًا
        groups.sort(key=lambda x: x['total_cost'], reverse=True)

        # إزالة المجموعات التي ليس لها تكلفة
        groups = [group for group in groups if group['total_cost'] > 0]
        print(f"Groups with cost > 0: {len(groups)}")

        # التأكد من وجود بيانات
        if not groups:
            message = "لا توجد بيانات للمجموعات الدوائية في الشهر المحدد"
            if scope_type != 'all' and scope_desc != "جميع الفروع":
                message += f" لـ{scope_desc}"

            return render_template(
                'simple_report.html',
                title="تقرير المجموعات الدوائية",
                message=message
            )

        # متغيرات للمقارنة بين المناطق/العيادات
        locations = []
        location_costs = []
        location_type = ""

        try:
            # إذا كان النطاق هو فرع محدد، نقوم بمقارنة المناطق
            if scope_type == 'branch' and branch_id:
                location_type = "المنطقة"

                # الحصول على المناطق في الفرع
                locations = conn.execute('''
                    SELECT id, name
                    FROM areas
                    WHERE branch_id = ?
                    ORDER BY name
                ''', (branch_id,)).fetchall()
                print(f"Found {len(locations)} areas in branch {branch_id}")

                # الحصول على تكلفة كل مجموعة في كل منطقة
                location_costs = []
                for location in locations:
                    for group in groups:
                        try:
                            query = '''
                                SELECT COALESCE(SUM(cost), 0) as cost
                                FROM drug_groups
                                WHERE area_id = ? AND group_code_id = ? AND strftime('%Y-%m', dispense_month) = ?
                            '''
                            params = (location['id'], group['id'], month)
                            print(f"Location cost query: {query}")
                            print(f"Location cost params: {params}")

                            result = conn.execute(query, params).fetchone()
                            cost = result['cost'] if result and result['cost'] is not None else 0
                            print(f"Cost for area {location['name']}, group {group['name']}: {cost}")

                            location_costs.append({
                                'location_id': location['id'],
                                'group_id': group['id'],
                                'cost': cost
                            })
                        except Exception as e:
                            print(f"Error getting cost for area {location['name']}, group {group['name']}: {e}")
                            location_costs.append({
                                'location_id': location['id'],
                                'group_id': group['id'],
                                'cost': 0
                            })

                print(f"Generated {len(location_costs)} location costs for branch {branch_id}")

            # إذا كان النطاق هو منطقة محددة، نقوم بمقارنة العيادات
            elif scope_type == 'area' and area_id:
                location_type = "العيادة"

                # الحصول على العيادات في المنطقة
                locations = conn.execute('''
                    SELECT id, name
                    FROM clinics
                    WHERE area_id = ?
                    ORDER BY name
                ''', (area_id,)).fetchall()
                print(f"Found {len(locations)} clinics in area {area_id}")

                # الحصول على تكلفة كل مجموعة في كل عيادة
                location_costs = []
                for location in locations:
                    for group in groups:
                        try:
                            query = '''
                                SELECT COALESCE(SUM(cost), 0) as cost
                                FROM drug_groups
                                WHERE clinic_id = ? AND group_code_id = ? AND strftime('%Y-%m', dispense_month) = ?
                            '''
                            params = (location['id'], group['id'], month)
                            print(f"Location cost query: {query}")
                            print(f"Location cost params: {params}")

                            result = conn.execute(query, params).fetchone()
                            cost = result['cost'] if result and result['cost'] is not None else 0
                            print(f"Cost for clinic {location['name']}, group {group['name']}: {cost}")

                            location_costs.append({
                                'location_id': location['id'],
                                'group_id': group['id'],
                                'cost': cost
                            })
                        except Exception as e:
                            print(f"Error getting cost for clinic {location['name']}, group {group['name']}: {e}")
                            location_costs.append({
                                'location_id': location['id'],
                                'group_id': group['id'],
                                'cost': 0
                            })

                print(f"Generated {len(location_costs)} location costs for area {area_id}")
        except Exception as e:
            print(f"Error in location costs: {e}")
            import traceback
            print(traceback.format_exc())

        return render_template(
            'drug_groups_report.html',
            groups=groups,
            total_cost=total_cost,
            month=month,
            month_name=month_name,
            report_title=report_title,
            scope_type=scope_type,
            scope_desc=scope_desc,
            locations=locations,
            location_costs=location_costs,
            location_type=location_type
        )

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error in drug_groups_report: {e}")
        print(f"Error details: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

    finally:
        if conn:
            conn.close()

@app.route('/manage/drug_categories/new', methods=['GET', 'POST'])
def manage_drug_categories_new():
    if request.method == 'POST':
        name = request.form.get('name')
        if name:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO drug_categories (name) VALUES (?)', (name,))
                conn.commit()
                flash('تم إضافة التصنيف بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا التصنيف موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم التصنيف', 'danger')

    conn = get_db_connection()
    # استعلام للحصول على التصنيفات مع عدد الأدوية في كل تصنيف
    categories = conn.execute('''
        SELECT
            dc.id,
            dc.name,
            COUNT(d.id) as drug_count
        FROM
            drug_categories dc
        LEFT JOIN
            drugs d ON dc.id = d.category_id
        GROUP BY
            dc.id, dc.name
        ORDER BY
            dc.name
    ''').fetchall()
    conn.close()
    return render_template('manage_drug_categories_new.html', categories=categories)

@app.route('/manage/drug_categories', methods=['GET', 'POST'])
def manage_drug_categories():
    if request.method == 'POST':
        name = request.form.get('name')
        if name:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO drug_categories (name) VALUES (?)', (name,))
                conn.commit()
                flash('تم إضافة التصنيف بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا التصنيف موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم التصنيف', 'danger')

    conn = get_db_connection()
    # استعلام للحصول على التصنيفات مع عدد الأدوية في كل تصنيف
    categories = conn.execute('''
        SELECT
            dc.id,
            dc.name,
            COUNT(d.id) as drug_count
        FROM
            drug_categories dc
        LEFT JOIN
            drugs d ON dc.id = d.category_id
        GROUP BY
            dc.id, dc.name
        ORDER BY
            dc.name
    ''').fetchall()
    conn.close()
    return render_template('manage_drug_categories.html', categories=categories)

@app.route('/manage/drug_categories/<int:category_id>/delete', methods=['POST'])
def delete_drug_category(category_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM drug_categories WHERE id = ?', (category_id,))
    conn.commit()
    conn.close()
    flash('تم حذف التصنيف بنجاح', 'success')
    return redirect(url_for('manage_drug_categories'))

@app.route('/manage/drug_categories/update', methods=['POST'])
def update_drug_category():
    category_id = request.form.get('category_id')
    name = request.form.get('name')

    if category_id and name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE drug_categories SET name = ? WHERE id = ?', (name, category_id))
            conn.commit()
            flash('تم تحديث التصنيف بنجاح', 'success')
        except sqlite3.IntegrityError:
            flash('هذا التصنيف موجود بالفعل', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم التصنيف', 'danger')

    return redirect(url_for('manage_drug_categories'))

@app.route('/manage/drug_groups', methods=['GET', 'POST'])
def manage_drug_groups():
    if request.method == 'POST':
        name = request.form.get('name')
        cost = request.form.get('cost')
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        group_code_id = request.form.get('group_code_id')

        if name and cost and clinic_id and dispense_month:
            # تحويل التاريخ
            dispense_date = f"{dispense_month}-01"

            conn = get_db_connection()
            # الحصول على معرف المنطقة من العيادة
            area_id = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()['area_id']

            # إذا تم اختيار تكويد، استخدمه
            if group_code_id:
                conn.execute(
                    'INSERT INTO drug_groups (name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (?, ?, ?, ?, ?, ?)',
                    (name, float(cost), clinic_id, area_id, dispense_date, group_code_id)
                )
            else:
                conn.execute(
                    'INSERT INTO drug_groups (name, cost, clinic_id, area_id, dispense_month) VALUES (?, ?, ?, ?, ?)',
                    (name, float(cost), clinic_id, area_id, dispense_date)
                )

            conn.commit()
            conn.close()
            flash('تم إضافة المجموعة الدوائية بنجاح', 'success')
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    conn = get_db_connection()
    try:
        drug_groups = conn.execute('''
            SELECT
                dg.id, dg.name, dg.cost, dg.clinic_id, dg.area_id, dg.dispense_month, dg.group_code_id,
                c.name as clinic_name,
                a.name as area_name,
                dgc.code as code_value,
                dgc.name as code_name_value
            FROM drug_groups dg
            JOIN clinics c ON dg.clinic_id = c.id
            JOIN areas a ON dg.area_id = a.id
            LEFT JOIN drug_group_codes dgc ON dg.group_code_id = dgc.id
            ORDER BY dg.dispense_month DESC
        ''').fetchall()

        # تحويل النتائج إلى قائمة من القواميس مع إضافة حقل code_name
        result_groups = []
        for group in drug_groups:
            group_dict = dict(group)
            # إضافة كود المجموعة فقط بدون الاسم
            group_dict['code_name'] = group_dict['code_value'] if group_dict['code_value'] else None
            # إضافة اسم المجموعة المكودة كحقل منفصل
            group_dict['code_group_name'] = group_dict['code_name_value'] if group_dict['code_name_value'] else None
            result_groups.append(group_dict)

        drug_groups = result_groups
    except Exception as e:
        print(f"Error in drug_groups query: {e}")
        drug_groups = []

    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()

    drug_group_codes = conn.execute('SELECT * FROM drug_group_codes ORDER BY code').fetchall()

    conn.close()

    return render_template('manage_drug_groups.html', drug_groups=drug_groups, clinics=clinics, drug_group_codes=drug_group_codes)

@app.route('/manage/drug_groups/<int:group_id>/delete', methods=['POST'])
def delete_drug_group(group_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM drug_groups WHERE id = ?', (group_id,))
    conn.commit()
    conn.close()
    flash('تم حذف المجموعة الدوائية بنجاح', 'success')
    return redirect(url_for('manage_drug_groups'))

@app.route('/manage/drug_groups/update', methods=['POST'])
def update_drug_group():
    group_id = request.form.get('group_id')
    name = request.form.get('name')
    cost = request.form.get('cost')
    clinic_id = request.form.get('clinic_id')
    dispense_month = request.form.get('dispense_month')
    group_code_id = request.form.get('group_code_id') or None

    if group_id and name and cost and clinic_id and dispense_month:
        # تحويل التاريخ
        dispense_date = f"{dispense_month}-01"

        conn = get_db_connection()
        # الحصول على معرف المنطقة من العيادة
        area_id = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()['area_id']

        # تحديث المجموعة الدوائية
        conn.execute(
            '''UPDATE drug_groups
               SET name = ?, cost = ?, clinic_id = ?, area_id = ?, dispense_month = ?, group_code_id = ?
               WHERE id = ?''',
            (name, float(cost), clinic_id, area_id, dispense_date, group_code_id, group_id)
        )
        conn.commit()
        conn.close()
        flash('تم تحديث المجموعة الدوائية بنجاح', 'success')
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_drug_groups'))

@app.route('/manage/drug_group_codes', methods=['GET', 'POST'])
def manage_drug_group_codes():
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')

        if name:
            conn = get_db_connection()
            try:
                # الحصول على آخر كود موجود
                last_code = conn.execute('SELECT MAX(code) FROM drug_group_codes').fetchone()[0]

                # إذا لم يكن هناك أكواد سابقة، ابدأ من 1
                if last_code is None:
                    new_code = 1
                else:
                    # زيادة الكود بمقدار 1
                    new_code = last_code + 1

                conn.execute(
                    'INSERT INTO drug_group_codes (code, name, description) VALUES (?, ?, ?)',
                    (new_code, name, description)
                )
                conn.commit()
                flash('تم إضافة تكويد المجموعة الدوائية بنجاح', 'success')
            except Exception as e:
                print(f"Error adding drug group code: {e}")
                flash('حدث خطأ أثناء إضافة التكويد', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم المجموعة', 'danger')

    conn = get_db_connection()
    drug_group_codes = conn.execute('SELECT * FROM drug_group_codes ORDER BY code').fetchall()

    # الحصول على آخر كود موجود للعرض في الصفحة
    last_code = conn.execute('SELECT MAX(code) FROM drug_group_codes').fetchone()[0]
    if last_code is None:
        next_code = 1
    else:
        next_code = last_code + 1

    conn.close()

    return render_template('manage_drug_group_codes.html', drug_group_codes=drug_group_codes, next_code=next_code)

@app.route('/manage/drug_group_codes/<int:code_id>/delete', methods=['POST'])
def delete_drug_group_code(code_id):
    conn = get_db_connection()
    # تحقق من استخدام الكود في المجموعات الدوائية
    used_count = conn.execute('SELECT COUNT(*) FROM drug_groups WHERE group_code_id = ?', (code_id,)).fetchone()[0]

    if used_count > 0:
        flash('لا يمكن حذف هذا التكويد لأنه مستخدم في مجموعات دوائية', 'danger')
    else:
        conn.execute('DELETE FROM drug_group_codes WHERE id = ?', (code_id,))
        conn.commit()
        flash('تم حذف تكويد المجموعة الدوائية بنجاح', 'success')

    conn.close()
    return redirect(url_for('manage_drug_group_codes'))

@app.route('/manage/drug_group_codes/update', methods=['POST'])
def update_drug_group_code():
    code_id = request.form.get('code_id')
    name = request.form.get('name')
    description = request.form.get('description')

    if code_id and name:
        conn = get_db_connection()
        conn.execute(
            'UPDATE drug_group_codes SET name = ?, description = ? WHERE id = ?',
            (name, description, code_id)
        )
        conn.commit()
        conn.close()
        flash('تم تحديث المجموعة الدوائية بنجاح', 'success')
    else:
        flash('يرجى إدخال اسم المجموعة', 'danger')

    return redirect(url_for('manage_drug_group_codes'))

@app.route('/manage/insulin', methods=['GET'])
def manage_insulin():
    conn = get_db_connection()

    # جلب تكويدات الأنسولين
    insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()

    # جلب أنواع الأنسولين
    insulin_types = conn.execute('SELECT * FROM insulin_types ORDER BY name').fetchall()

    # جلب فئات الأنسولين
    insulin_categories = conn.execute('SELECT * FROM insulin_categories ORDER BY name').fetchall()

    # الحصول على آخر كود موجود للعرض في الصفحة
    last_code = conn.execute('SELECT MAX(code) FROM insulin_codes').fetchone()[0]
    if last_code is None:
        next_code = 1
    else:
        next_code = last_code + 1

    conn.close()

    return render_template('insulin_management.html',
                          insulin_codes=insulin_codes,
                          insulin_types=insulin_types,
                          insulin_categories=insulin_categories,
                          next_code=next_code)

@app.route('/manage/insulin_codes', methods=['GET', 'POST'])
def manage_insulin_codes():
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')

        if name:
            conn = get_db_connection()
            try:
                # الحصول على آخر كود موجود
                last_code = conn.execute('SELECT MAX(code) FROM insulin_codes').fetchone()[0]

                # إذا لم يكن هناك أكواد سابقة، ابدأ من 1
                if last_code is None:
                    new_code = 1
                else:
                    # زيادة الكود بمقدار 1
                    new_code = last_code + 1

                conn.execute(
                    'INSERT INTO insulin_codes (code, name, description) VALUES (?, ?, ?)',
                    (new_code, name, description)
                )
                conn.commit()
                flash('تم إضافة تكويد الأنسولين بنجاح', 'success')
            except Exception as e:
                flash(f'حدث خطأ أثناء إضافة التكويد: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم التكويد', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_codes/<int:code_id>/delete', methods=['POST'])
def delete_insulin_code(code_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM insulin_codes WHERE id = ?', (code_id,))
    conn.commit()
    conn.close()
    flash('تم حذف تكويد الأنسولين بنجاح', 'success')
    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_codes/update', methods=['POST'])
def update_insulin_code():
    code_id = request.form.get('code_id')
    name = request.form.get('name')
    description = request.form.get('description')

    if code_id and name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE insulin_codes SET name = ?, description = ? WHERE id = ?', (name, description, code_id))
            conn.commit()
            flash('تم تحديث تكويد الأنسولين بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث التكويد: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_types', methods=['POST'])
def manage_insulin_types():
    if request.method == 'POST':
        type_name = request.form.get('type_name')
        type_description = request.form.get('type_description')

        if type_name:
            conn = get_db_connection()
            try:
                conn.execute(
                    'INSERT INTO insulin_types (name, description) VALUES (?, ?)',
                    (type_name, type_description)
                )
                conn.commit()
                flash('تم إضافة نوع الأنسولين بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا النوع موجود بالفعل', 'danger')
            except Exception as e:
                flash(f'حدث خطأ أثناء إضافة نوع الأنسولين: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم نوع الأنسولين', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_types/<int:type_id>/delete', methods=['POST'])
def delete_insulin_type(type_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM insulin_types WHERE id = ?', (type_id,))
    conn.commit()
    conn.close()
    flash('تم حذف نوع الأنسولين بنجاح', 'success')
    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_types/update', methods=['POST'])
def update_insulin_type():
    type_id = request.form.get('type_id')
    type_name = request.form.get('type_name')
    type_description = request.form.get('type_description')

    if type_id and type_name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE insulin_types SET name = ?, description = ? WHERE id = ?',
                        (type_name, type_description, type_id))
            conn.commit()
            flash('تم تحديث نوع الأنسولين بنجاح', 'success')
        except sqlite3.IntegrityError:
            flash('هذا النوع موجود بالفعل', 'danger')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث نوع الأنسولين: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_categories', methods=['POST'])
def manage_insulin_categories():
    if request.method == 'POST':
        category_name = request.form.get('category_name')
        category_description = request.form.get('category_description')

        if category_name:
            conn = get_db_connection()
            try:
                conn.execute(
                    'INSERT INTO insulin_categories (name, description) VALUES (?, ?)',
                    (category_name, category_description)
                )
                conn.commit()
                flash('تم إضافة فئة الأنسولين بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذه الفئة موجودة بالفعل', 'danger')
            except Exception as e:
                flash(f'حدث خطأ أثناء إضافة فئة الأنسولين: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم فئة الأنسولين', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_categories/<int:category_id>/delete', methods=['POST'])
def delete_insulin_category(category_id):
    conn = get_db_connection()
    conn.execute('DELETE FROM insulin_categories WHERE id = ?', (category_id,))
    conn.commit()
    conn.close()
    flash('تم حذف فئة الأنسولين بنجاح', 'success')
    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_categories/update', methods=['POST'])
def update_insulin_category():
    category_id = request.form.get('category_id')
    category_name = request.form.get('category_name')
    category_description = request.form.get('category_description')

    if category_id and category_name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE insulin_categories SET name = ?, description = ? WHERE id = ?',
                        (category_name, category_description, category_id))
            conn.commit()
            flash('تم تحديث فئة الأنسولين بنجاح', 'success')
        except sqlite3.IntegrityError:
            flash('هذه الفئة موجودة بالفعل', 'danger')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث فئة الأنسولين: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_items', methods=['GET', 'POST'])
def manage_insulin_items():
    if request.method == 'POST':
        name = request.form.get('name')
        type = request.form.get('type')
        cases_count = request.form.get('cases_count')
        quantity = request.form.get('quantity')
        price = request.form.get('price')
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        insulin_code_id = request.form.get('insulin_code_id')

        if name and type and cases_count and quantity and price and clinic_id and dispense_month:
            # تحويل التاريخ
            dispense_date = f"{dispense_month}-01"

            conn = get_db_connection()
            # الحصول على معرف المنطقة من العيادة
            area_id = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()['area_id']

            # إذا تم اختيار تكويد، استخدمه
            if insulin_code_id:
                conn.execute(
                    'INSERT INTO insulin_dispensed (name, type, cases_count, quantity, price, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                    (name, type, int(cases_count), float(quantity), float(price), clinic_id, area_id, dispense_date, insulin_code_id)
                )
            else:
                conn.execute(
                    'INSERT INTO insulin_dispensed (name, type, cases_count, quantity, price, clinic_id, area_id, dispense_month) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                    (name, type, int(cases_count), float(quantity), float(price), clinic_id, area_id, dispense_date)
                )
            conn.commit()
            conn.close()
            flash('تم إضافة الأنسولين بنجاح', 'success')
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    conn = get_db_connection()
    insulin_items = conn.execute('''
        SELECT
            i.*,
            c.name as clinic_name,
            ic.name as code_name,
            ic.code as code_value
        FROM insulin_dispensed i
        JOIN clinics c ON i.clinic_id = c.id
        LEFT JOIN insulin_codes ic ON i.insulin_code_id = ic.id
        ORDER BY i.dispense_month DESC
    ''').fetchall()

    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()

    insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()

    conn.close()

    return render_template('manage_insulin.html', insulin_items=insulin_items, clinics=clinics, insulin_codes=insulin_codes)

@app.route('/manage/insulin_items/update', methods=['POST'])
def update_insulin_item():
    insulin_id = request.form.get('insulin_id')
    name = request.form.get('name')
    type = request.form.get('type')
    unit = request.form.get('unit')
    cases_count = request.form.get('cases_count')
    quantity = request.form.get('quantity')
    price = request.form.get('price')
    cost = request.form.get('cost')
    rate = request.form.get('rate')
    balance = request.form.get('balance')
    category = request.form.get('category')
    clinic_id = request.form.get('clinic_id')
    dispense_month = request.form.get('dispense_month')
    insulin_code_id = request.form.get('insulin_code_id')

    print(f"تم استلام طلب تحديث الأنسولين: {insulin_id}")
    print(f"البيانات المستلمة: name={name}, type={type}, unit={unit}, cases_count={cases_count}, quantity={quantity}, price={price}, cost={cost}, rate={rate}, balance={balance}, category={category}, clinic_id={clinic_id}, dispense_month={dispense_month}, insulin_code_id={insulin_code_id}")

    if insulin_id and name and type and unit and cases_count and quantity and price and clinic_id and dispense_month:
        # تحويل التاريخ
        dispense_date = f"{dispense_month}-01"

        # حساب التكلفة إذا لم يتم توفيرها
        if not cost:
            cost = float(quantity) * float(price)

        conn = get_db_connection()
        # الحصول على معرف المنطقة من العيادة
        area_id = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()['area_id']

        # إذا تم اختيار تكويد، استخدمه
        if insulin_code_id:
            conn.execute(
                '''UPDATE insulin_dispensed
                   SET name = ?, type = ?, unit = ?, cases_count = ?, quantity = ?, price = ?,
                       cost = ?, rate = ?, balance = ?, category = ?,
                       clinic_id = ?, area_id = ?, dispense_month = ?, insulin_code_id = ?
                   WHERE id = ?''',
                (name, type, unit, int(cases_count), float(quantity), float(price),
                 float(cost), float(rate) if rate else 0, float(balance) if balance else 0, category,
                 clinic_id, area_id, dispense_date, insulin_code_id, insulin_id)
            )
        else:
            conn.execute(
                '''UPDATE insulin_dispensed
                   SET name = ?, type = ?, unit = ?, cases_count = ?, quantity = ?, price = ?,
                       cost = ?, rate = ?, balance = ?, category = ?,
                       clinic_id = ?, area_id = ?, dispense_month = ?, insulin_code_id = NULL
                   WHERE id = ?''',
                (name, type, unit, int(cases_count), float(quantity), float(price),
                 float(cost), float(rate) if rate else 0, float(balance) if balance else 0, category,
                 clinic_id, area_id, dispense_date, insulin_id)
            )
        conn.commit()
        conn.close()
        flash('تم تحديث الأنسولين بنجاح', 'success')
        print("تم تحديث الأنسولين بنجاح")
    else:
        missing = []
        if not insulin_id: missing.append("معرف الأنسولين")
        if not name: missing.append("الاسم")
        if not type: missing.append("النوع")
        if not unit: missing.append("الوحدة")
        if not cases_count: missing.append("عدد الحالات")
        if not quantity: missing.append("الكمية")
        if not price: missing.append("السعر")
        if not clinic_id: missing.append("العيادة")
        if not dispense_month: missing.append("شهر الصرف")

        flash(f'يرجى إدخال جميع البيانات المطلوبة. البيانات المفقودة: {", ".join(missing)}', 'danger')
        print(f"فشل تحديث الأنسولين. البيانات المفقودة: {missing}")

    return redirect(url_for('insulin_dispense'))

@app.route('/manage/insulin_items/<int:insulin_id>/delete', methods=['POST'])
def delete_insulin_item(insulin_id):
    print(f"تم استلام طلب حذف الأنسولين: {insulin_id}")
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM insulin_dispensed WHERE id = ?', (insulin_id,))
        conn.commit()
        flash('تم حذف الأنسولين بنجاح', 'success')
        print(f"تم حذف الأنسولين بنجاح: {insulin_id}")
    except Exception as e:
        conn.rollback()
        flash(f'حدث خطأ أثناء حذف الأنسولين: {str(e)}', 'danger')
        print(f"فشل حذف الأنسولين: {insulin_id}. الخطأ: {str(e)}")
    finally:
        conn.close()
    return redirect(url_for('insulin_dispense'))

@app.route('/insulin/dispense', methods=['GET', 'POST'])
def insulin_dispense():
    if request.method == 'POST':
        print("تم استلام طلب POST لمنصرف الأنسولين")
        print("Headers:", request.headers)
        print("Form data:", request.form)

        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        items_data = request.form.get('items_data')
        total_cost = request.form.get('total_cost')

        print(f"clinic_id: {clinic_id}")
        print(f"dispense_month: {dispense_month}")
        print(f"items_data length: {len(items_data) if items_data else 'None'}")
        print(f"total_cost: {total_cost}")

        if clinic_id and dispense_month and items_data:
            try:
                # تحويل التاريخ
                dispense_date = f"{dispense_month}-01"
                print(f"dispense_date: {dispense_date}")

                # تحويل البيانات من JSON إلى قائمة
                items = json.loads(items_data)
                print(f"تم تحويل البيانات بنجاح، عدد العناصر: {len(items)}")

                # الحصول على معرف المنطقة من العيادة
                conn = get_db_connection()
                area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()

                if not area_result:
                    raise Exception(f"لم يتم العثور على العيادة برقم {clinic_id}")

                area_id = area_result['area_id']
                print(f"area_id: {area_id}")

                # إضافة كل عنصر إلى قاعدة البيانات
                for i, item in enumerate(items):
                    print(f"إضافة العنصر {i+1}: {item['name']}")
                    conn.execute(
                        '''INSERT INTO insulin_dispensed
                           (name, type, unit, cases_count, quantity, price, cost, rate, balance, category,
                            clinic_id, area_id, dispense_month, insulin_code_id)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                        (item['name'], item['type'], item['unit'], item['cases_count'], item['quantity'],
                         item['price'], item['cost'], item['rate'], item['balance'], item['category'],
                         clinic_id, area_id, dispense_date, item['insulin_code_id'] or None)
                    )

                conn.commit()
                print("تم الحفظ في قاعدة البيانات بنجاح")
                conn.close()

                # إذا كان الطلب من AJAX، أعد استجابة JSON
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    print("إرسال استجابة JSON للطلب من AJAX")
                    return jsonify({'success': True, 'message': f'تم إضافة {len(items)} من الأنسولين بنجاح'})

                # وإلا، أعد توجيه المستخدم إلى الصفحة مع رسالة نجاح
                print("إعادة توجيه المستخدم إلى صفحة منصرف الأنسولين")
                flash(f'تم إضافة {len(items)} من الأنسولين بنجاح', 'success')
                return redirect(url_for('insulin_dispense'))

            except Exception as e:
                import traceback
                print(f"Error adding insulin: {e}")
                traceback.print_exc()

                # إذا كان الطلب من AJAX، أعد استجابة خطأ JSON
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    print("إرسال استجابة خطأ JSON للطلب من AJAX")
                    return jsonify({'success': False, 'message': f'حدث خطأ أثناء إضافة الأنسولين: {str(e)}'}), 400

                # وإلا، أعد توجيه المستخدم إلى الصفحة مع رسالة خطأ
                print("إعادة توجيه المستخدم إلى صفحة منصرف الأنسولين مع رسالة خطأ")
                flash(f'حدث خطأ أثناء إضافة الأنسولين: {str(e)}', 'danger')
                return redirect(url_for('insulin_dispense'))
        else:
            missing = []
            if not clinic_id: missing.append("clinic_id")
            if not dispense_month: missing.append("dispense_month")
            if not items_data: missing.append("items_data")
            print(f"بيانات مفقودة: {', '.join(missing)}")

            # إذا كان الطلب من AJAX، أعد استجابة خطأ JSON
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                print("إرسال استجابة خطأ JSON للطلب من AJAX - بيانات مفقودة")
                return jsonify({'success': False, 'message': f'يرجى إدخال جميع البيانات المطلوبة. البيانات المفقودة: {", ".join(missing)}'}), 400

            # وإلا، أعد توجيه المستخدم إلى الصفحة مع رسالة خطأ
            print("إعادة توجيه المستخدم إلى صفحة منصرف الأنسولين مع رسالة خطأ - بيانات مفقودة")
            flash(f'يرجى إدخال جميع البيانات المطلوبة. البيانات المفقودة: {", ".join(missing)}', 'danger')
            return redirect(url_for('insulin_dispense'))

    # جلب البيانات اللازمة للصفحة
    conn = get_db_connection()

    # جلب الفروع
    branches = conn.execute('SELECT * FROM branches').fetchall()

    # جلب تكويدات الأنسولين
    insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()

    # جلب أنواع الأنسولين
    insulin_types = conn.execute('SELECT * FROM insulin_types ORDER BY name').fetchall()

    # جلب فئات الأنسولين
    insulin_categories = conn.execute('SELECT * FROM insulin_categories ORDER BY name').fetchall()

    # جلب منصرف الأنسولين المحفوظ
    saved_items = conn.execute('''
        SELECT
            i.*,
            c.name as clinic_name,
            a.name as area_name,
            b.name as branch_name,
            ic.name as code_name,
            ic.code as code_value,
            strftime('%Y-%m', i.dispense_month) as dispense_month_formatted
        FROM insulin_dispensed i
        JOIN clinics c ON i.clinic_id = c.id
        JOIN areas a ON i.area_id = a.id
        JOIN branches b ON a.branch_id = b.id
        LEFT JOIN insulin_codes ic ON i.insulin_code_id = ic.id
        ORDER BY i.dispense_month DESC, c.name
        LIMIT 50
    ''').fetchall()

    conn.close()

    return render_template('insulin_dispense.html',
                          branches=branches,
                          insulin_codes=insulin_codes,
                          insulin_types=insulin_types,
                          insulin_categories=insulin_categories,
                          saved_items=saved_items)

# إضافة متغيرات عامة للقوالب
@app.context_processor
def inject_now():
    return {'now': datetime.now()}

if __name__ == '__main__':
    print("بدء تشغيل تطبيق منصرف الأدوية...")
    app.run(debug=True, port=8080)