{% extends "base.html" %}

{% block title %}إدارة الأدوية - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 20px 20px 0 0 !important;
        border: none !important;
        padding: 20px 30px;
    }

    .section-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        overflow: hidden;
        height: 100%;
    }

    .section-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .section-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 15px 20px;
        margin: -1px -1px 20px -1px;
        border-radius: 15px 15px 0 0;
    }

    .links-section .section-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .drugs-list-section .section-header {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 12px 15px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .btn-danger {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
    }

    .btn-warning {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
        color: white;
        font-weight: 600;
    }

    .btn-warning:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .table {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
        text-align: center;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .table tbody td {
        padding: 15px;
        text-align: center;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .list-group-item {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 10px !important;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .list-group-item:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: translateX(10px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .alert-info {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border: none;
        border-radius: 15px;
        color: #333;
        padding: 20px;
        text-align: center;
    }

    .icon-input {
        position: relative;
    }

    .icon-input i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        z-index: 10;
    }

    .icon-input .form-control,
    .icon-input .form-select {
        padding-left: 45px;
    }

    .search-box {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        border: 2px solid #e3e6f0;
        padding: 8px 20px;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .section-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .section-card:nth-child(2) {
        animation-delay: 0.2s;
    }

    .section-card:nth-child(3) {
        animation-delay: 0.4s;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="main-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0 fw-bold text-white">
                            <i class="mdi mdi-pill me-3"></i>إدارة الأدوية
                        </h3>
                        <div>
                            <a href="{{ url_for('dispense_new') }}" class="btn btn-light me-2">
                                <i class="mdi mdi-cart me-1"></i>صرف الأدوية
                            </a>
                            <a href="{{ url_for('index') }}" class="btn btn-light">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4 mb-4">
                        <!-- نموذج إضافة دواء جديد -->
                        <div class="col-lg-6">
                            <div class="section-card">
                                <div class="section-header">
                                    <h6 class="mb-0 fw-bold"><i class="mdi mdi-plus-circle me-2"></i>إضافة دواء جديد</h6>
                                </div>
                                <div class="p-3">
                                    <form method="POST" action="/manage/drugs">
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-tag-multiple"></i>
                                            <select class="form-select" id="category_id" name="category_id" required>
                                                <option value="">-- اختر التصنيف --</option>
                                                {% for category in categories %}
                                                <option value="{{ category.id }}">{{ category.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-pill"></i>
                                            <input type="text" class="form-control" id="name" name="name" placeholder="اسم الدواء" required>
                                        </div>
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-flask"></i>
                                            <input type="text" class="form-control" id="scientific_name" name="scientific_name" placeholder="الاسم العلمي (اختياري)">
                                        </div>
                                        <div class="icon-input mb-4">
                                            <i class="mdi mdi-calendar"></i>
                                            <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                                        </div>
                                        <div class="text-center">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="mdi mdi-plus-circle me-2"></i>إضافة الدواء
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- روابط سريعة -->
                        <div class="col-lg-6">
                            <div class="section-card links-section">
                                <div class="section-header">
                                    <h6 class="mb-0 fw-bold"><i class="mdi mdi-link-variant me-2"></i>روابط سريعة</h6>
                                </div>
                                <div class="p-3">
                                    <div class="list-group">
                                        <a href="{{ url_for('manage_drug_categories_new') }}" class="list-group-item list-group-item-action">
                                            <i class="mdi mdi-tag-multiple me-2"></i>إدارة تصنيفات الأدوية
                                        </a>
                                        <a href="{{ url_for('dispense_new') }}" class="list-group-item list-group-item-action">
                                            <i class="mdi mdi-cart me-2"></i>صرف الأدوية
                                        </a>
                                        <a href="{{ url_for('insulin_dispense') }}" class="list-group-item list-group-item-action">
                                            <i class="mdi mdi-needle me-2"></i>منصرف الأنسولين
                                        </a>
                                        <a href="{{ url_for('reports') }}" class="list-group-item list-group-item-action">
                                            <i class="mdi mdi-chart-bar me-2"></i>التقارير
                                        </a>
                                        <a href="{{ url_for('index') }}" class="list-group-item list-group-item-action">
                                            <i class="mdi mdi-home me-2"></i>العودة للصفحة الرئيسية
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col">
                            <a href="{{ url_for('add_drugs_batch') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> إضافة مجموعة أدوية
                            </a>
                        </div>
                    </div>

                    <!-- قائمة الأدوية -->
                    <div class="section-card drugs-list-section">
                        <div class="section-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 fw-bold"><i class="mdi mdi-format-list-bulleted me-2"></i>قائمة الأدوية</h6>
                                <div class="d-flex gap-2 align-items-center">
                                    <!-- تصفية حسب التصنيف -->
                                    <div class="icon-input" style="position: relative; width: 200px;">
                                        <i class="mdi mdi-tag-multiple" style="color: white;"></i>
                                        <select id="categoryFilter" class="search-box" style="padding-left: 45px; width: 100%; appearance: none; background-color: rgba(255, 255, 255, 0.9);">
                                            <option value="">جميع التصنيفات</option>
                                            {% for category in categories %}
                                            <option value="{{ category.name }}">{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <!-- البحث النصي -->
                                    <div class="icon-input" style="position: relative; width: 250px;">
                                        <i class="mdi mdi-magnify" style="color: white;"></i>
                                        <input type="text" id="searchDrug" class="search-box" placeholder="بحث في الأدوية..." style="padding-left: 45px; width: 100%;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3">
                            {% if drugs %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="drugsTable">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>اسم الدواء</th>
                                            <th>الاسم العلمي</th>
                                            <th>التصنيف</th>
                                            <th>تاريخ انتهاء الصلاحية</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for drug in drugs %}
                                        <tr>
                                            <td><span class="badge bg-primary">{{ loop.index }}</span></td>
                                            <td><strong>{{ drug.name }}</strong></td>
                                            <td>{{ drug.scientific_name or '<span class="text-muted">-</span>' | safe }}</td>
                                            <td><span class="badge bg-info">{{ drug.category_name }}</span></td>
                                            <td>
                                                {% if drug.expiry_date %}
                                                    <span class="badge bg-warning text-dark">{{ drug.expiry_date }}</span>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-warning" title="تعديل"
                                                            data-id="{{ drug.id }}"
                                                            data-name="{{ drug.name }}"
                                                            data-scientific="{{ drug.scientific_name or '' }}"
                                                            data-category="{{ drug.category_id }}"
                                                            data-expiry="{{ drug.expiry_date or '' }}"
                                                            onclick="editDrug(this)">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </button>
                                                    <form method="POST" action="{{ url_for('delete_drug', drug_id=drug.id) }}" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الدواء؟');">
                                                        <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                            <i class="mdi mdi-delete"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-3" style="font-size: 2rem;"></i>
                                <strong>لا توجد أدوية مسجلة حالياً.</strong>
                                <br>ابدأ بإضافة دواء جديد من النموذج أعلاه.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    var isEditing = false;
    var editingDrugId = null;

    $(document).ready(function() {
        // دالة التصفية المشتركة
        function filterTable() {
            var searchValue = $("#searchDrug").val().toLowerCase();
            var categoryValue = $("#categoryFilter").val().toLowerCase();

            $("#drugsTable tbody tr").filter(function() {
                var row = $(this);
                var text = row.text().toLowerCase();
                var categoryText = row.find('td:nth-child(4)').text().toLowerCase(); // عمود التصنيف

                var matchesSearch = searchValue === "" || text.indexOf(searchValue) > -1;
                var matchesCategory = categoryValue === "" || categoryText.indexOf(categoryValue) > -1;

                row.toggle(matchesSearch && matchesCategory);
            });

            // عرض عدد النتائج
            updateResultsCount();
        }

        // دالة عرض عدد النتائج
        function updateResultsCount() {
            var visibleRows = $("#drugsTable tbody tr:visible").length;
            var totalRows = $("#drugsTable tbody tr").length;

            // إزالة العداد السابق إن وجد
            $("#resultsCount").remove();

            // إضافة العداد الجديد
            $("#drugsTable").before(
                '<div id="resultsCount" class="alert alert-info py-2 mb-3">' +
                '<i class="mdi mdi-information me-2"></i>' +
                'عرض <strong>' + visibleRows + '</strong> من أصل <strong>' + totalRows + '</strong> دواء' +
                '</div>'
            );

            // إخفاء العداد إذا كانت جميع النتائج ظاهرة
            if (visibleRows === totalRows) {
                $("#resultsCount").hide();
            }
        }

        // البحث في جدول الأدوية
        $("#searchDrug").on("keyup", filterTable);

        // التصفية حسب التصنيف
        $("#categoryFilter").on("change", filterTable);

        // إضافة زر مسح التصفية
        if ($("#clearFilters").length === 0) {
            $("#categoryFilter").parent().after(
                '<button type="button" id="clearFilters" class="btn btn-outline-light btn-sm" style="border-radius: 20px;">' +
                '<i class="mdi mdi-refresh me-1"></i>مسح التصفية' +
                '</button>'
            );
        }

        // مسح جميع التصفيات
        $("#clearFilters").on("click", function() {
            $("#searchDrug").val("");
            $("#categoryFilter").val("");
            filterTable();
        });
    });

    // دالة تعديل الدواء
    function editDrug(button) {
        // الحصول على البيانات من الزر
        var id = $(button).data('id');
        var name = $(button).data('name');
        var scientificName = $(button).data('scientific');
        var categoryId = $(button).data('category');
        var expiryDate = $(button).data('expiry');

        // ملء النموذج ببيانات الدواء
        $('#category_id').val(categoryId);
        $('#name').val(name);
        $('#scientific_name').val(scientificName);
        $('#expiry_date').val(expiryDate);

        // تغيير حالة النموذج للتعديل
        isEditing = true;
        editingDrugId = id;

        // تغيير نص الزر
        $('button[type="submit"]').html('<i class="mdi mdi-check me-2"></i>تحديث الدواء');

        // إضافة زر إلغاء
        if ($('#cancel-edit-btn').length === 0) {
            $('button[type="submit"]').after('<button type="button" id="cancel-edit-btn" class="btn btn-secondary ms-2" onclick="cancelEdit()"><i class="mdi mdi-close me-2"></i>إلغاء</button>');
        }

        // تغيير action النموذج
        $('form').attr('action', '/manage/drugs/' + id + '/edit');

        // التمرير للنموذج
        $('html, body').animate({
            scrollTop: $('form').offset().top - 100
        }, 500);
    }

    // دالة إلغاء التعديل
    function cancelEdit() {
        // إعادة تعيين النموذج
        $('form')[0].reset();

        // إعادة تعيين حالة التعديل
        isEditing = false;
        editingDrugId = null;

        // إعادة تعيين نص الزر
        $('button[type="submit"]').html('<i class="mdi mdi-plus-circle me-2"></i>إضافة الدواء');

        // إزالة زر الإلغاء
        $('#cancel-edit-btn').remove();

        // إعادة تعيين action النموذج
        $('form').attr('action', '/manage/drugs');
    }
</script>
{% endblock %}
