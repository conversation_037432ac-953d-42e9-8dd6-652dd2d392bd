{% extends "base.html" %}

{% block title %}تقرير الأنسولين - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    .report-header {
        background-color: #20c997;
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .report-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
    }

    .report-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .report-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .report-info-item {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .report-info-item i {
        margin-left: 8px;
        color: #20c997;
    }

    .report-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        border: none;
    }

    .report-card .card-header {
        background-color: #20c997;
        color: white;
        font-weight: bold;
    }

    .report-table th {
        background-color: #e9f7f3;
        color: #0b8a65;
    }

    .report-summary {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .summary-title {
        color: #0b8a65;
        font-size: 1.4rem;
        margin-bottom: 15px;
        border-bottom: 2px solid #20c997;
        padding-bottom: 10px;
    }

    .summary-item {
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 5px;
        background-color: #ffffff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .summary-item:hover {
        background-color: #e9f7f3;
    }

    .summary-item-name {
        font-weight: bold;
        color: #0b8a65;
    }

    .summary-item-value {
        font-weight: bold;
        color: #20c997;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 30px;
    }

    .action-buttons {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .action-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: none;
        font-size: 24px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .action-btn:hover {
        transform: scale(1.1);
    }

    .print-btn {
        background-color: #20c997;
        color: white;
    }

    .print-btn:hover {
        background-color: #0b8a65;
    }

    .back-btn {
        background-color: #6c757d;
        color: white;
    }

    .back-btn:hover {
        background-color: #5a6268;
    }

    .excel-btn {
        background-color: #1d6f42;
        color: white;
    }

    .excel-btn:hover {
        background-color: #185a36;
    }

    .pdf-btn {
        background-color: #e74c3c;
        color: white;
    }

    .pdf-btn:hover {
        background-color: #c0392b;
    }

    .pdf-btn .pdf-icon {
        font-family: Arial, sans-serif;
        font-weight: bold;
        font-size: 20px;
        color: white;
        text-align: center;
        display: block;
        line-height: 1;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        letter-spacing: 0.5px;
    }

    /* تنسيق اللوجو */
    .report-logo {
        max-width: 200px;
        height: auto;
        margin-bottom: 10px;
    }

    @media print {
        /* إخفاء أزرار الإجراءات عند الطباعة */
        .action-buttons {
            display: none;
        }

        /* إزالة الظلال وإضافة حدود للعناصر */
        .report-card, .report-header, .report-info, .report-summary {
            box-shadow: none;
            border: 1px solid #dee2e6;
        }

        /* ضبط هوامش الصفحة للطباعة - سيتم تحديد الاتجاه من خلال JavaScript */
        @page {
            margin: 0.8cm;
        }

        /* ضبط حجم الخط للطباعة */
        body {
            font-size: 12pt;
        }

        /* إضافة حقوق الملكية في نهاية صفحة الطباعة */
        body::after {
            content: 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}';
            display: block;
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }

        /* ضبط عرض الجدول للطباعة */
        .table {
            width: 100% !important;
            table-layout: auto; /* تغيير من fixed إلى auto للسماح بمرونة أكبر */
            font-size: 11pt;
            max-width: none;
            border-collapse: collapse;
        }

        /* تنسيق اللوجو عند الطباعة */
        .report-logo {
            display: block;
            max-width: 150px;
            height: auto;
            margin: 0 auto 15px;
        }

        /* ضبط عرض الأعمدة للتأكد من ظهور جميع البيانات */
        .report-table th, .report-table td {
            padding: 6px;
            overflow: visible;
            white-space: normal;
            word-wrap: break-word;
            max-width: none;
        }

        /* تعيين الحد الأدنى والأقصى لعرض الأعمدة بدلاً من عرض ثابت */
        .report-table th:nth-child(1), .report-table td:nth-child(1) { min-width: 30px; } /* # */
        .report-table th:nth-child(2), .report-table td:nth-child(2) { min-width: 120px; } /* الصنف */
        .report-table th:nth-child(3), .report-table td:nth-child(3) { min-width: 80px; } /* النوع */
        .report-table th:nth-child(4), .report-table td:nth-child(4) { min-width: 80px; } /* الفئة */
        .report-table th:nth-child(5), .report-table td:nth-child(5) { min-width: 60px; } /* الوحدة */
        .report-table th:nth-child(6), .report-table td:nth-child(6) { min-width: 60px; } /* السعر */
        .report-table th:nth-child(7), .report-table td:nth-child(7) { min-width: 60px; } /* الكمية */
        .report-table th:nth-child(8), .report-table td:nth-child(8) { min-width: 60px; } /* عدد الحالات */
        .report-table th:nth-child(9), .report-table td:nth-child(9) { min-width: 60px; } /* المعدل */
        .report-table th:nth-child(10), .report-table td:nth-child(10) { min-width: 60px; } /* الرصيد */
        .report-table th:nth-child(11), .report-table td:nth-child(11) { min-width: 80px; font-weight: bold; } /* التكلفة */
        .report-table th:nth-child(12), .report-table td:nth-child(12) { min-width: 120px; } /* الفرع */

        /* تصغير حجم الرسوم البيانية */
        .chart-container {
            height: 200px;
        }

        /* تحسين مظهر الشارات */
        .category-badge, .insulin-type-badge {
            padding: 2px 5px;
            font-size: 8pt;
        }
    }

    .category-badge {
        background-color: #e9f7f3;
        color: #0b8a65;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.85rem;
        margin-right: 5px;
    }

    .insulin-type-badge {
        background-color: #f0f8ff;
        color: #0d6efd;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .cost-cell {
        font-weight: bold;
        color: #0b8a65;
    }

    .total-row {
        background-color: #e9f7f3;
        font-weight: bold;
    }

    .total-row td {
        border-top: 2px solid #20c997;
    }

    .chart-title {
        color: #0b8a65;
        text-align: center;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }

    .group-item-even {
        background-color: rgba(32, 201, 151, 0.05);
    }

    .group-item-odd {
        background-color: rgba(13, 110, 253, 0.05);
    }

    .price-cell {
        font-weight: bold;
        color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-info" onclick="showPrintOptions('pdf')" title="حفظ كـ PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>حفظ PDF
                    </button>
                    <button class="btn btn-primary" onclick="showPrintOptions('print')" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- رأس التقرير -->
    <div class="report-header">
        <div class="text-center mb-3">
            <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" height="80" class="report-logo">
        </div>
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="report-title">{{ report_title }}</h1>
                <p class="report-subtitle mb-0">{{ period }}</p>
            </div>
            <div class="text-left">
                <h3>{{ total_cost|round(2) }} ج.م</h3>
                <p class="mb-0">إجمالي التكلفة</p>
            </div>
        </div>
    </div>

    <!-- معلومات التقرير -->
    <div class="report-info">
        <div class="row">
            <div class="col-md-6">
                <div class="report-info-item">
                    <i class="mdi mdi-calendar"></i>
                    <span>الفترة: {{ period }}</span>
                </div>
                {% if scope_name %}
                <div class="report-info-item">
                    <i class="mdi mdi-map-marker"></i>
                    <span>{{ scope_type_text }}: {{ scope_name }}</span>
                </div>
                {% endif %}
            </div>
            <div class="col-md-6">
                {% if category %}
                <div class="report-info-item">
                    <i class="mdi mdi-tag"></i>
                    <span>فئة الأنسولين: {{ category }}</span>
                </div>
                {% endif %}
                <div class="report-info-item">
                    <i class="mdi mdi-calendar-clock"></i>
                    <span>تاريخ إنشاء التقرير: {{ now.strftime('%d/%m/%Y %H:%M') }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card report-card">
                <div class="card-header">
                    <i class="mdi mdi-chart-pie me-2"></i>توزيع الأنسولين حسب الفئة
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card report-card">
                <div class="card-header">
                    <i class="mdi mdi-chart-bar me-2"></i>توزيع الأنسولين حسب النوع
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="typeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول البيانات -->
    <div class="card report-card">
        <div class="card-header">
            <i class="mdi mdi-table me-2"></i>تفاصيل منصرف الأنسولين
        </div>
        <div class="card-body">
            <div class="table-responsive">
                {% if group_by == 'category' %}
                <!-- جدول التقرير المجمع حسب الفئة -->
                <table class="table table-striped table-hover report-table">
                    <thead style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;">
                        <tr>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                <span>#</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 120px;">
                                <i class="mdi mdi-tag me-2" style="color: #2ecc71;"></i>
                                <span>الفئة</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                <i class="mdi mdi-needle me-2" style="color: #f39c12;"></i>
                                <span>الصنف</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 100px;">
                                <i class="mdi mdi-medical-bag me-2" style="color: #e74c3c;"></i>
                                <span>النوع</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-scale me-2" style="color: #2ecc71;"></i>
                                <span>الوحدة</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-currency-usd me-2" style="color: #f1c40f;"></i>
                                <span>السعر</span><br>
                                <small style="color: #ecf0f1; font-size: 11px;">(ج.م)</small>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-package-variant me-2" style="color: #2ecc71;"></i>
                                <span>الكمية</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 100px; text-align: center;">
                                <i class="mdi mdi-archive me-2" style="color: #e74c3c;"></i>
                                <span>عدد الحالات</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px; text-align: center;">
                                <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                <span>التكلفة</span><br>
                                <small style="color: #ecf0f1; font-size: 11px;">(ج.م)</small>
                            </th>
                            {% if show_location %}
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #8e44ad; min-width: 120px;">
                                <i class="mdi mdi-map-marker me-2" style="color: #9b59b6;"></i>
                                <span>{{ location_type }}</span>
                            </th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% set current_category = '' %}
                        {% set row_class = '' %}
                        {% for item in insulin_items %}
                            {% if current_category != item.category %}
                                {% set current_category = item.category %}
                                {% if row_class == 'group-item-even' %}
                                    {% set row_class = 'group-item-odd' %}
                                {% else %}
                                    {% set row_class = 'group-item-even' %}
                                {% endif %}
                            {% endif %}
                        <tr class="{{ row_class }}">
                            <td>{{ loop.index }}</td>
                            <td><span class="category-badge">{{ item.category }}</span></td>
                            <td>{{ item.name }}</td>
                            <td><span class="insulin-type-badge">{{ item.type }}</span></td>
                            <td>{{ item.unit }}</td>
                            <td class="price-cell">{{ item.price }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.cases_count }}</td>
                            <td class="cost-cell">{{ item.cost|round(2) }}</td>
                            {% if show_location %}
                            <td>{{ item.location_name }}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="6" class="text-left">الإجمالي</td>
                            <td>{{ insulin_items|sum(attribute='quantity')|round(2) }}</td>
                            <td>{{ insulin_items|sum(attribute='cases_count') }}</td>
                            <td class="cost-cell">{{ total_cost|round(2) }}</td>
                            {% if show_location %}
                            <td></td>
                            {% endif %}
                        </tr>
                    </tfoot>
                </table>

                {% elif group_by == 'type' %}
                <!-- جدول التقرير المجمع حسب النوع -->
                <table class="table table-striped table-hover report-table">
                    <thead style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;">
                        <tr>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                <span>#</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 100px;">
                                <i class="mdi mdi-medical-bag me-2" style="color: #e74c3c;"></i>
                                <span>النوع</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                <i class="mdi mdi-needle me-2" style="color: #f39c12;"></i>
                                <span>الصنف</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 120px;">
                                <i class="mdi mdi-tag me-2" style="color: #2ecc71;"></i>
                                <span>الفئة</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-scale me-2" style="color: #2ecc71;"></i>
                                <span>الوحدة</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-currency-usd me-2" style="color: #f1c40f;"></i>
                                <span>السعر</span><br>
                                <small style="color: #ecf0f1; font-size: 11px;">(ج.م)</small>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-package-variant me-2" style="color: #2ecc71;"></i>
                                <span>الكمية</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 100px; text-align: center;">
                                <i class="mdi mdi-archive me-2" style="color: #e74c3c;"></i>
                                <span>عدد الحالات</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px; text-align: center;">
                                <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                <span>التكلفة</span><br>
                                <small style="color: #ecf0f1; font-size: 11px;">(ج.م)</small>
                            </th>
                            {% if show_location %}
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #8e44ad; min-width: 120px;">
                                <i class="mdi mdi-map-marker me-2" style="color: #9b59b6;"></i>
                                <span>{{ location_type }}</span>
                            </th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% set current_type = '' %}
                        {% set row_class = '' %}
                        {% for item in insulin_items %}
                            {% if current_type != item.type %}
                                {% set current_type = item.type %}
                                {% if row_class == 'group-item-even' %}
                                    {% set row_class = 'group-item-odd' %}
                                {% else %}
                                    {% set row_class = 'group-item-even' %}
                                {% endif %}
                            {% endif %}
                        <tr class="{{ row_class }}">
                            <td>{{ loop.index }}</td>
                            <td><span class="insulin-type-badge">{{ item.type }}</span></td>
                            <td>{{ item.name }}</td>
                            <td><span class="category-badge">{{ item.category }}</span></td>
                            <td>{{ item.unit }}</td>
                            <td class="price-cell">{{ item.price }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.cases_count }}</td>
                            <td class="cost-cell">{{ item.cost|round(2) }}</td>
                            {% if show_location %}
                            <td>{{ item.location_name }}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="6" class="text-left">الإجمالي</td>
                            <td>{{ insulin_items|sum(attribute='quantity')|round(2) }}</td>
                            <td>{{ insulin_items|sum(attribute='cases_count') }}</td>
                            <td class="cost-cell">{{ total_cost|round(2) }}</td>
                            {% if show_location %}
                            <td></td>
                            {% endif %}
                        </tr>
                    </tfoot>
                </table>

                {% else %}
                <!-- جدول التقرير المجمع حسب الصنف والسعر (الافتراضي) -->
                <table class="table table-striped table-hover report-table">
                    <thead style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;">
                        <tr>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                <span>#</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                <i class="mdi mdi-needle me-2" style="color: #f39c12;"></i>
                                <span>الصنف</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 100px;">
                                <i class="mdi mdi-medical-bag me-2" style="color: #e74c3c;"></i>
                                <span>النوع</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 120px;">
                                <i class="mdi mdi-tag me-2" style="color: #2ecc71;"></i>
                                <span>الفئة</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-scale me-2" style="color: #2ecc71;"></i>
                                <span>الوحدة</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-currency-usd me-2" style="color: #f1c40f;"></i>
                                <span>السعر</span><br>
                                <small style="color: #ecf0f1; font-size: 11px;">(ج.م)</small>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-package-variant me-2" style="color: #2ecc71;"></i>
                                <span>الكمية</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 100px; text-align: center;">
                                <i class="mdi mdi-archive me-2" style="color: #e74c3c;"></i>
                                <span>عدد الحالات</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #8e44ad; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-chart-line me-2" style="color: #9b59b6;"></i>
                                <span>المعدل</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #16a085; min-width: 80px; text-align: center;">
                                <i class="mdi mdi-warehouse me-2" style="color: #1abc9c;"></i>
                                <span>الرصيد</span>
                            </th>
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px; text-align: center;">
                                <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                <span>التكلفة</span><br>
                                <small style="color: #ecf0f1; font-size: 11px;">(ج.م)</small>
                            </th>
                            {% if show_location %}
                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #8e44ad; min-width: 120px;">
                                <i class="mdi mdi-map-marker me-2" style="color: #9b59b6;"></i>
                                <span>{{ location_type }}</span>
                            </th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% set current_name = '' %}
                        {% set row_class = '' %}
                        {% for item in insulin_items %}
                            {% if current_name != item.name %}
                                {% set current_name = item.name %}
                                {% if row_class == 'group-item-even' %}
                                    {% set row_class = 'group-item-odd' %}
                                {% else %}
                                    {% set row_class = 'group-item-even' %}
                                {% endif %}
                            {% endif %}
                        <tr class="{{ row_class }}">
                            <td>{{ loop.index }}</td>
                            <td>{{ item.name }}</td>
                            <td><span class="insulin-type-badge">{{ item.type }}</span></td>
                            <td><span class="category-badge">{{ item.category }}</span></td>
                            <td>{{ item.unit }}</td>
                            <td class="price-cell">{{ item.price }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.cases_count }}</td>
                            <td>{{ item.rate|default(0)|round(2) }}</td>
                            <td>{{ item.balance|default(0) }}</td>
                            <td class="cost-cell">{{ item.cost|round(2) }}</td>
                            {% if show_location %}
                            <td>{{ item.location_name }}</td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="6" class="text-left">الإجمالي</td>
                            <td>{{ insulin_items|sum(attribute='quantity')|round(2) }}</td>
                            <td>{{ insulin_items|sum(attribute='cases_count') }}</td>
                            <td></td>
                            <td></td>
                            <td class="cost-cell">{{ total_cost|round(2) }}</td>
                            {% if show_location %}
                            <td></td>
                            {% endif %}
                        </tr>
                    </tfoot>
                </table>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- ملخص التقرير -->
    <div class="report-summary">
        <h3 class="summary-title">
            <i class="mdi mdi-information-outline me-2"></i>ملخص التقرير
        </h3>
        <div class="row">
            <div class="col-md-4">
                <div class="summary-item">
                    <div class="d-flex justify-content-between">
                        <span class="summary-item-name">إجمالي التكلفة</span>
                        <span class="summary-item-value">{{ total_cost|round(2) }} ج.م</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="summary-item">
                    <div class="d-flex justify-content-between">
                        <span class="summary-item-name">عدد الأصناف</span>
                        <span class="summary-item-value">{{ insulin_items|length }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="summary-item">
                    <div class="d-flex justify-content-between">
                        <span class="summary-item-name">إجمالي عدد الحالات</span>
                        <span class="summary-item-value">{{ total_cases }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- مربع حوار خيارات الطباعة -->
<div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">خيارات الطباعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">اتجاه الصفحة:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                        <label class="form-check-label" for="orientationLandscape">
                            أفقي (Landscape)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                        <label class="form-check-label" for="orientationPortrait">
                            رأسي (Portrait)
                        </label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">حجم الخط:</label>
                    <select class="form-select" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
            </div>
        </div>
    </div>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script>
    async function exportFormattedExcel() {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('تقرير الأنسولين');

            let currentRow = 1;

            // إضافة عنوان التقرير
            const titleCell = worksheet.getCell('A' + currentRow);
            titleCell.value = 'تقرير منصرف الأنسولين';
            titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
            titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF20C997' } };
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':H' + currentRow);
            currentRow += 2;

            // إضافة معلومات التقرير
            var reportInfo = document.querySelectorAll('.report-header p');
            reportInfo.forEach(function(info) {
                const infoCell = worksheet.getCell('A' + currentRow);
                infoCell.value = info.innerText;
                infoCell.font = { bold: true, size: 12 };
                infoCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FC' } };
                currentRow++;
            });
            currentRow++; // سطر فارغ

            // إضافة عناوين الأعمدة
            var headers = [];
            table.querySelectorAll('thead th').forEach(function(th) {
                headers.push(th.innerText);
            });

            headers.forEach((header, index) => {
                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                cell.value = header;
                cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF20C997' } };
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
            currentRow++;

            // إضافة بيانات الصفوف
            table.querySelectorAll('tbody tr').forEach(function(tr) {
                var row = [];
                tr.querySelectorAll('td').forEach(function(td, index) {
                    var text = td.innerText.trim();
                    // تحويل الأرقام إلى قيم رقمية
                    if (!isNaN(text) && text !== '' && index > 0) {
                        row.push(parseFloat(text));
                    } else {
                        row.push(text);
                    }
                });

                row.forEach((data, index) => {
                    const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    cell.value = data;
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.border = {
                        top: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                        left: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                        bottom: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                        right: { style: 'thin', color: { argb: 'FFDDDDDD' } }
                    };
                });
                currentRow++;
            });

            // إضافة صف الإجمالي
            var footerRow = [];
            table.querySelectorAll('tfoot td').forEach(function(td) {
                var text = td.innerText.trim();
                if (!isNaN(text) && text !== '') {
                    footerRow.push(parseFloat(text));
                } else {
                    footerRow.push(text);
                }
            });

            if (footerRow.length > 0) {
                footerRow.forEach((data, index) => {
                    const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    cell.value = data;
                    cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
                currentRow += 2;
            }

            // إضافة ملخص التقرير
            const summaryTitleCell = worksheet.getCell('A' + currentRow);
            summaryTitleCell.value = 'ملخص التقرير';
            summaryTitleCell.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
            summaryTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6F42C1' } };
            summaryTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':B' + currentRow);
            currentRow++;

            var summaryItems = document.querySelectorAll('.summary-item');
            summaryItems.forEach(function(item) {
                var name = item.querySelector('.summary-item-name').innerText;
                var value = item.querySelector('.summary-item-value').innerText;

                const nameCell = worksheet.getCell('A' + currentRow);
                nameCell.value = name;
                nameCell.font = { bold: true };
                nameCell.alignment = { horizontal: 'right', vertical: 'middle' };

                const valueCell = worksheet.getCell('B' + currentRow);
                valueCell.value = value;
                valueCell.font = { bold: true, color: { argb: 'FF28A745' } };
                valueCell.alignment = { horizontal: 'center', vertical: 'middle' };

                currentRow++;
            });

            // تحديد عرض الأعمدة
            worksheet.columns = [
                { width: 5 },   // #
                { width: 30 },  // اسم الصنف
                { width: 15 },  // الفئة
                { width: 15 },  // النوع
                { width: 12 },  // الكمية
                { width: 15 },  // عدد الحالات
                { width: 12 },  // السعر
                { width: 18 }   // إجمالي التكلفة
            ];

            // تصدير الملف
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const fileName = "تقرير_الأنسولين_منسق_" + new Date().toISOString().slice(0, 10) + ".xlsx";
            saveAs(blob, fileName);
        } catch (error) {
            console.error("Error exporting to Excel:", error);
            alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تحويل البيانات من جانب الخادم إلى متغيرات JavaScript
        var categoryData = JSON.parse('{{ category_data|tojson|safe }}');
        var typeData = JSON.parse('{{ type_data|tojson|safe }}');

        // تجهيز بيانات الفئات
        var categoryLabels = categoryData.map(function(item) { return item.name; });
        var categoryValues = categoryData.map(function(item) { return item.cost; });
        var categoryColors = [
            '#20c997', '#0d6efd', '#fd7e14', '#6f42c1', '#dc3545',
            '#0dcaf0', '#ffc107', '#198754', '#6610f2', '#d63384'
        ];

        // تجهيز بيانات الأنواع
        var typeLabels = typeData.map(function(item) { return item.name; });
        var typeValues = typeData.map(function(item) { return item.cost; });
        var typeColors = [
            '#0d6efd', '#6f42c1', '#dc3545', '#fd7e14', '#ffc107',
            '#198754', '#0dcaf0', '#20c997', '#6610f2', '#d63384'
        ];

        // إنشاء رسم بياني للفئات
        var categoryCtx = document.getElementById('categoryChart').getContext('2d');
        var categoryChart = new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: categoryLabels,
                datasets: [{
                    data: categoryValues,
                    backgroundColor: categoryColors.slice(0, categoryLabels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal, sans-serif',
                                size: 12
                            },
                            color: '#0b8a65'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.raw || 0;
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = Math.round((value / total) * 100);
                                return `${label}: ${value.toFixed(2)} ج.م (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // إنشاء رسم بياني للأنواع
        var typeCtx = document.getElementById('typeChart').getContext('2d');
        var typeChart = new Chart(typeCtx, {
            type: 'bar',
            data: {
                labels: typeLabels,
                datasets: [{
                    label: 'التكلفة (ج.م)',
                    data: typeValues,
                    backgroundColor: typeColors.slice(0, typeLabels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var value = context.raw || 0;
                                return `التكلفة: ${value.toFixed(2)} ج.م`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Tajawal, sans-serif'
                            },
                            color: '#0b8a65',
                            callback: function(value) {
                                return value.toFixed(2) + ' ج.م';
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Tajawal, sans-serif'
                            },
                            color: '#0b8a65'
                        }
                    }
                }
            }
        });

        // إضافة وظيفة تصدير Excel
        window.exportToExcel = function() {
            var table = document.querySelector('.report-table');
            if (!table) {
                alert('لم يتم العثور على جدول البيانات!');
                return;
            }
            exportFormattedExcel();
        };

        // إضافة وظيفة خيارات الطباعة
        window.showPrintOptions = function(action) {
            var modal = new bootstrap.Modal(document.getElementById('printOptionsModal'));
            modal.show();

            document.getElementById('confirmPrint').onclick = function() {
                var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
                var fontSize = document.getElementById('fontSize').value;
                
                document.body.classList.remove('print-portrait', 'print-landscape', 'font-small', 'font-medium', 'font-large');
                document.body.classList.add('print-' + orientation, 'font-' + fontSize);
                
                modal.hide();
                
                if (action === 'print') {
                    window.print();
                } else if (action === 'pdf') {
                    window.print(); // سيتم تحويله إلى PDF من خلال مربع حوار الطباعة
                }
            };
        };
    });
</script>
{% endblock %}
