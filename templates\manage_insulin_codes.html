{% extends "base.html" %}

{% block title %}إدارة تكويد الأنسولين - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="mdi mdi-needle me-2"></i>إدارة تكويد الأنسولين
                    </h4>
                    <a href="/manage/insulin" class="btn btn-light btn-add">
                        <i class="mdi mdi-needle me-1"></i>إدارة الأنسولين
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-8 mx-auto">
                        <form method="POST" action="/manage/insulin_codes">
                            <div class="card group-card shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0"><i class="mdi mdi-plus-box me-2"></i>بيانات تكويد الأنسولين</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="next_code" value="{{ next_code }}" readonly>
                                                <label for="next_code">الكود التالي</label>
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="name" name="name" required>
                                                <label for="name">اسم التكويد</label>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-floating">
                                                <textarea class="form-control" id="description" name="description" style="height: 100px;"></textarea>
                                                <label for="description">وصف التكويد (اختياري)</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center mt-3">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="mdi mdi-content-save me-1"></i>حفظ التكويد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card group-card shadow-sm mt-4">
                    <div class="card-header bg-light">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0"><i class="mdi mdi-format-list-bulleted me-2"></i>قائمة تكويدات الأنسولين</h5>
                            </div>
                            <div class="col-auto">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                                    <input type="text" id="searchInsulinCode" class="form-control" placeholder="بحث...">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if insulin_codes %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-custom" id="insulinCodesTable">
                                <thead>
                                    <tr class="text-center">
                                        <th>#</th>
                                        <th>كود الأنسولين</th>
                                        <th>اسم التكويد</th>
                                        <th>وصف التكويد</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for code in insulin_codes %}
                                    <tr>
                                        <td class="text-center">{{ loop.index }}</td>
                                        <td class="text-center fw-bold">{{ code.code }}</td>
                                        <td>{{ code.name }}</td>
                                        <td>{{ code.description or '-' }}</td>
                                        <td class="text-center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-info rounded-circle me-1 edit-insulin-code"
                                                        data-id="{{ code.id }}"
                                                        data-code="{{ code.code }}"
                                                        data-name="{{ code.name }}"
                                                        data-description="{{ code.description or '' }}">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>
                                                <form method="POST" action="/manage/insulin_codes/{{ code.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا التكويد؟');">
                                                    <button type="submit" class="btn btn-sm btn-danger rounded-circle">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="mdi mdi-information me-2"></i>لا توجد تكويدات أنسولين مسجلة حالياً.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل التكويد -->
<div class="modal fade" id="editInsulinCodeModal" tabindex="-1" aria-labelledby="editInsulinCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editInsulinCodeModalLabel">تعديل تكويد الأنسولين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/manage/insulin_codes/update">
                <div class="modal-body">
                    <input type="hidden" id="edit_code_id" name="code_id">
                    <div class="mb-3">
                        <label for="edit_code" class="form-label">الكود</label>
                        <input type="text" class="form-control" id="edit_code" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم التكويد</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">وصف التكويد</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تفعيل البحث في جدول التكويدات
        $("#searchInsulinCode").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $("#insulinCodesTable tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });

        // تفعيل نموذج تعديل التكويد
        $('.edit-insulin-code').on('click', function() {
            var id = $(this).data('id');
            var code = $(this).data('code');
            var name = $(this).data('name');
            var description = $(this).data('description');

            $('#edit_code_id').val(id);
            $('#edit_code').val(code);
            $('#edit_name').val(name);
            $('#edit_description').val(description);

            $('#editInsulinCodeModal').modal('show');
        });
    });
</script>
{% endblock %}
