#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

print("🚀 بدء تشغيل تطبيق منصرف الأدوية...")
print("=" * 50)

try:
    print("📦 تحميل المكتبات...")
    from flask import Flask
    print("✅ Flask تم تحميله")
    
    import sqlite3
    print("✅ SQLite تم تحميله")
    
    from datetime import datetime
    print("✅ DateTime تم تحميله")
    
    print("📁 تحميل التطبيق...")
    from app import app
    print("✅ تم تحميل التطبيق بنجاح")
    
    print("🌐 بدء تشغيل الخادم...")
    print("🔗 رابط التطبيق: http://localhost:8080")
    print("=" * 50)
    print("📝 ملاحظة: اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    # تشغيل التطبيق
    app.run(
        host='0.0.0.0',
        port=8080,
        debug=True,
        use_reloader=False
    )
    
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("💡 تأكد من تثبيت Flask: pip install flask")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    print("💡 تحقق من ملف app.py")
    
finally:
    print("\n👋 تم إيقاف التطبيق")
