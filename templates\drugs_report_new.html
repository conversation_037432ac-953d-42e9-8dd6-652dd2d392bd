{% extends 'base.html' %}

{% block title %}تقرير الأدوية: {{ category_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-info" onclick="showPrintOptions('pdf')" title="حفظ كـ PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>حفظ PDF
                    </button>
                    <button class="btn btn-primary" onclick="showPrintOptions('print')" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-pill me-2"></i>تقرير الأدوية: {{ category_name }}
                </h4>
                <div>
                    <span class="badge bg-light text-dark">
                        <i class="mdi mdi-calendar-range me-1"></i>{{ date_range_text }}
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
            </div>

            <!-- بطاقات الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        <div class="card-body text-white text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="mdi mdi-tag-multiple" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0">{{ categories_data|length }}</h3>
                                    <p class="mb-0">تصنيف</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                        <div class="card-body text-white text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="mdi mdi-account-group" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0">{{ total_cases }}</h3>
                                    <p class="mb-0">حالة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="mdi mdi-calendar-range text-primary" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 text-dark">{{ date_range_text }}</h6>
                                    <p class="mb-0 text-muted">الفترة الزمنية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="card-body text-white text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="mdi mdi-cash-multiple" style="font-size: 2.5rem;"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0">{{ total_cost }}</h4>
                                    <p class="mb-0">جنيه</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% if categories_data %}
                {% for category_name, category in categories_data.items() %}
                <div class="card mb-4 shadow-sm border-0">
                    <div class="card-header" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; border-radius: 15px 15px 0 0;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="mdi mdi-tag me-2"></i>{{ category_name }}</h5>
                            <div>
                                <span class="badge bg-light text-dark me-2">
                                    <i class="mdi mdi-account-group me-1"></i>{{ category.cases }} حالة
                                </span>
                                <span class="badge bg-warning text-dark">
                                    <i class="mdi mdi-cash me-1"></i>{{ category.total }} جنيه
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <tr>
                                        <th class="text-center" style="border: none;">#</th>
                                        <th style="border: none;"><i class="mdi mdi-pill me-1"></i>اسم الدواء</th>
                                        <th class="text-center" style="border: none;"><i class="mdi mdi-counter me-1"></i>الكمية</th>
                                        <th class="text-center" style="border: none;"><i class="mdi mdi-account-group me-1"></i>عدد الحالات</th>
                                        <th class="text-center" style="border: none;"><i class="mdi mdi-cash me-1"></i>التكلفة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for drug in category.drugs %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ drug.name }}</td>
                                        <td class="text-center">{{ drug.quantity }}</td>
                                        <td class="text-center">{{ drug.cases }}</td>
                                        <td class="text-center fw-bold">{{ drug.cost }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                    <tr>
                                        <th colspan="3" class="text-start" style="border: none; font-weight: bold;">
                                            <i class="mdi mdi-sigma me-1"></i>الإجمالي
                                        </th>
                                        <th class="text-center" style="border: none; font-weight: bold; color: #495057;">
                                            {{ category.cases }}
                                        </th>
                                        <th class="text-center" style="border: none; font-weight: bold; color: #28a745;">
                                            {{ category.total }} جنيه
                                        </th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
            <div class="alert alert-info">
                <i class="mdi mdi-information-outline me-2"></i>لا توجد بيانات منصرفة في الفترة المحددة
            </div>
            {% endif %}

            <!-- ملخص التكلفة النهائي -->
            <div class="card shadow-lg border-0 mt-4">
                <div class="card-header text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0;">
                    <h4 class="mb-0"><i class="mdi mdi-chart-pie me-2"></i>الملخص النهائي للتقرير</h4>
                </div>
                <div class="card-body" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                <div class="card-body text-white text-center">
                                    <i class="mdi mdi-account-group mb-3" style="font-size: 3rem;"></i>
                                    <h2 class="mb-2">{{ total_cases }}</h2>
                                    <h5 class="mb-0">إجمالي الحالات</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                <div class="card-body text-white text-center">
                                    <i class="mdi mdi-cash-multiple mb-3" style="font-size: 3rem;"></i>
                                    <h2 class="mb-2">{{ total_cost }}</h2>
                                    <h5 class="mb-0">إجمالي التكلفة (جنيه)</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- مربع حوار خيارات الطباعة -->
    <div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">خيارات الطباعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label class="form-label">اتجاه الصفحة:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                            <label class="form-check-label" for="orientationLandscape">
                                أفقي (Landscape)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                            <label class="form-check-label" for="orientationPortrait">
                                رأسي (Portrait)
                            </label>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">حجم الخط:</label>
                        <select class="form-select" id="fontSize">
                            <option value="small">صغير</option>
                            <option value="medium" selected>متوسط</option>
                            <option value="large">كبير</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    .card {
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .table {
        border-radius: 10px;
        overflow: hidden;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.1);
        transform: scale(1.01);
    }

    @media print {
        .btn, .navbar, footer, .no-print {
            display: none !important;
        }
        .card {
            border: 1px solid #ddd !important;
            break-inside: avoid;
        }
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }
        .container-fluid {
            padding: 0 !important;
        }

        /* إضافة اللوجو في بداية صفحة الطباعة */
        body::before {
            content: '';
            display: block;
            background-image: url("{{ url_for('static', filename='images/logo.png') }}");
            background-repeat: no-repeat;
            background-position: center top;
            background-size: 150px auto;
            height: 100px;
            margin-bottom: 20px;
        }

        /* إضافة حقوق الملكية في نهاية صفحة الطباعة */
        body::after {
            content: 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}';
            display: block;
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    // متغيرات عامة
    var printAction = 'print'; // 'print' أو 'pdf'
    var printModal;
    
    // التأكد من تحميل الصفحة بشكل كامل
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل الصفحة بنجاح');
        
        // التأكد من وجود زر PDF
        var pdfButton = document.getElementById('pdf-button');
        if (pdfButton) {
            console.log('تم العثور على زر PDF');
            
            // إضافة حدث النقر على زر PDF
            pdfButton.addEventListener('click', function() {
                console.log('تم النقر على زر PDF');
                showPrintOptions('pdf');
            });
        } else {
            console.error('لم يتم العثور على زر PDF');
        }
    });

    // وظيفة إظهار خيارات الطباعة
    function showPrintOptions(action) {
        printAction = action;

        // تهيئة مربع الحوار إذا لم يكن موجودًا
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));

            // إضافة حدث للزر "طباعة"
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }

        // عرض مربع الحوار
        printModal.show();
    }

    // وظيفة تنفيذ الطباعة بناءً على الخيارات المحددة
    function performPrint() {
        try {
            // الحصول على الخيارات المحددة
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            // تطبيق بعض التعديلات قبل الطباعة
            var originalOverflow = document.body.style.overflow;
            var originalWidth = document.body.style.width;

            // تعديل نمط الصفحة للطباعة
            document.body.style.overflow = 'visible';
            document.body.style.width = 'auto';

            // تطبيق اتجاه الصفحة
            var styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; }';

            // تطبيق حجم الخط
            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };
            styleElement.innerHTML += '.table { font-size: ' + fontSizeMap[fontSize] + '; }';

            // إضافة العنصر إلى الصفحة
            document.head.appendChild(styleElement);

            // طباعة الصفحة
            window.print();

            // إزالة العنصر بعد الطباعة
            setTimeout(function() {
                document.head.removeChild(styleElement);
                document.body.style.overflow = originalOverflow;
                document.body.style.width = originalWidth;
            }, 1000);
        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");
        }
    }

    // وظيفة تصدير البيانات إلى Excel
    function exportToExcel() {
        // تحديد الجدول المراد تصديره
        var table = document.querySelector('.table');
        if (!table) return;

        // إنشاء مصفوفة لتخزين البيانات
        var data = [];

        // إضافة عناوين الأعمدة
        var headers = [];
        table.querySelectorAll('thead th').forEach(function(th) {
            headers.push(th.innerText);
        });
        data.push(headers);

        // إضافة بيانات الصفوف
        table.querySelectorAll('tbody tr').forEach(function(tr) {
            var row = [];
            tr.querySelectorAll('td').forEach(function(td) {
                row.push(td.innerText);
            });
            data.push(row);
        });

        // إضافة صف الإجمالي
        var footerRow = [];
        table.querySelectorAll('tfoot tr').forEach(function(tr) {
            tr.querySelectorAll('th, td').forEach(function(cell) {
                footerRow.push(cell.innerText);
            });
        });
        if (footerRow.length > 0) {
            data.push(footerRow);
        }

        // إنشاء ورقة عمل Excel
        var ws = XLSX.utils.aoa_to_sheet(data);

        // إنشاء مصنف Excel
        var wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "تقرير الأدوية");

        // تحديد اسم الملف
        var fileName = "تقرير_الأدوية_" + new Date().toISOString().slice(0, 10) + ".xlsx";

        // تنزيل الملف
        XLSX.writeFile(wb, fileName);
    }
</script>
{% endblock %}
