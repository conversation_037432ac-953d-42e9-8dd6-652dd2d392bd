{% extends 'base.html' %}

{% block title %}تقرير الأدوية: {{ category_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="card shadow-sm mb-4">
        <div class="card-header drugs-report text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-pill me-2"></i>تقرير الأدوية: {{ category_name }}
                </h4>
                <div>
                    <span class="badge bg-light text-dark">
                        <i class="mdi mdi-calendar-range me-1"></i>{{ date_range_text }}
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title">معلومات التقرير</h5>
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr>
                                            <th width="30%">التصنيف:</th>
                                            <td>{{ category_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>الفترة الزمنية:</th>
                                            <td>{{ date_range_text }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title">ملخص التقرير</h5>
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr>
                                            <th width="30%">عدد التصنيفات:</th>
                                            <td>{{ categories_data|length }}</td>
                                        </tr>
                                        <tr>
                                            <th>إجمالي الحالات:</th>
                                            <td>{{ total_cases }}</td>
                                        </tr>
                                        <tr>
                                            <th>إجمالي التكلفة:</th>
                                            <td class="text-primary fw-bold">{{ total_cost }} جنيه</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% if categories_data %}
                {% for category_name, category in categories_data.items() %}
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ category_name }}</h5>
                            <div>
                                <span class="badge bg-info text-white me-2">
                                    <i class="mdi mdi-account-group me-1"></i>{{ category.cases }} حالة
                                </span>
                                <span class="badge drugs-report text-white">
                                    {{ category.total }} جنيه
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الدواء</th>
                                        <th class="text-center">الكمية</th>
                                        <th class="text-center">عدد الحالات</th>
                                        <th class="text-center">التكلفة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for drug in category.drugs %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ drug.name }}</td>
                                        <td class="text-center">{{ drug.quantity }}</td>
                                        <td class="text-center">{{ drug.cases }}</td>
                                        <td class="text-center fw-bold">{{ drug.cost }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-light">
                                        <th colspan="3" class="text-start">الإجمالي</th>
                                        <th class="text-center">{{ category.cases }}</th>
                                        <th class="text-center">{{ category.total }} جنيه</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
            <div class="alert alert-info">
                <i class="mdi mdi-information-outline me-2"></i>لا توجد بيانات منصرفة في الفترة المحددة
            </div>
            {% endif %}

            <div class="card shadow-sm">
                <div class="card-header drugs-report text-white">
                    <h5 class="mb-0">ملخص التكلفة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mx-auto">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <th class="bg-light">إجمالي الحالات</th>
                                            <td class="text-center fw-bold">{{ total_cases }} حالة</td>
                                        </tr>
                                        <tr>
                                            <th class="bg-light">إجمالي التكلفة</th>
                                            <td class="text-center fw-bold fs-4">{{ total_cost }} جنيه</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="action-buttons no-print">
        <button class="action-btn print-btn" onclick="showPrintOptions('print')" title="طباعة التقرير">
            <i class="mdi mdi-printer"></i>
        </button>
        <button class="action-btn excel-btn" onclick="exportToExcel()" title="تصدير إلى Excel">
            <i class="mdi mdi-file-excel"></i>
        </button>
        <div style="margin-bottom: 15px;">
            <button id="pdf-button" onclick="showPrintOptions('pdf')" title="طباعة كملف PDF" style="width: 60px; height: 60px; border-radius: 50%; border: none; background-color: #4e73df; color: white; font-size: 24px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); transition: all 0.3s; display: flex; align-items: center; justify-content: center;">
                <div style="font-family: Arial, sans-serif; font-weight: bold; font-size: 20px; color: white; text-align: center; display: block; line-height: 1; text-shadow: 1px 1px 2px rgba(0,0,0,0.5); letter-spacing: 0.5px;">PDF</div>
            </button>
        </div>
        <a href="{{ url_for('reports') }}" class="action-btn back-btn" title="العودة إلى التقارير">
            <i class="mdi mdi-arrow-left"></i>
        </a>
    </div>

    <!-- مربع حوار خيارات الطباعة -->
    <div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">خيارات الطباعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label class="form-label">اتجاه الصفحة:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                            <label class="form-check-label" for="orientationLandscape">
                                أفقي (Landscape)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                            <label class="form-check-label" for="orientationPortrait">
                                رأسي (Portrait)
                            </label>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">حجم الخط:</label>
                        <select class="form-select" id="fontSize">
                            <option value="small">صغير</option>
                            <option value="medium" selected>متوسط</option>
                            <option value="large">كبير</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block styles %}
<style>
    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    @media print {
        .btn, .navbar, footer, .no-print {
            display: none !important;
        }
        .card {
            border: 1px solid #ddd !important;
            break-inside: avoid;
        }
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }
        .container-fluid {
            padding: 0 !important;
        }

        /* إضافة اللوجو في بداية صفحة الطباعة */
        body::before {
            content: '';
            display: block;
            background-image: url("{{ url_for('static', filename='images/logo.png') }}");
            background-repeat: no-repeat;
            background-position: center top;
            background-size: 150px auto;
            height: 100px;
            margin-bottom: 20px;
        }

        /* إضافة حقوق الملكية في نهاية صفحة الطباعة */
        body::after {
            content: 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}';
            display: block;
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    // متغيرات عامة
    var printAction = 'print'; // 'print' أو 'pdf'
    var printModal;
    
    // التأكد من تحميل الصفحة بشكل كامل
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل الصفحة بنجاح');
        
        // التأكد من وجود زر PDF
        var pdfButton = document.getElementById('pdf-button');
        if (pdfButton) {
            console.log('تم العثور على زر PDF');
            
            // إضافة حدث النقر على زر PDF
            pdfButton.addEventListener('click', function() {
                console.log('تم النقر على زر PDF');
                showPrintOptions('pdf');
            });
        } else {
            console.error('لم يتم العثور على زر PDF');
        }
    });

    // وظيفة إظهار خيارات الطباعة
    function showPrintOptions(action) {
        printAction = action;

        // تهيئة مربع الحوار إذا لم يكن موجودًا
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));

            // إضافة حدث للزر "طباعة"
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }

        // عرض مربع الحوار
        printModal.show();
    }

    // وظيفة تنفيذ الطباعة بناءً على الخيارات المحددة
    function performPrint() {
        try {
            // الحصول على الخيارات المحددة
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            // تطبيق بعض التعديلات قبل الطباعة
            var originalOverflow = document.body.style.overflow;
            var originalWidth = document.body.style.width;

            // تعديل نمط الصفحة للطباعة
            document.body.style.overflow = 'visible';
            document.body.style.width = 'auto';

            // تطبيق اتجاه الصفحة
            var styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; }';

            // تطبيق حجم الخط
            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };
            styleElement.innerHTML += '.table { font-size: ' + fontSizeMap[fontSize] + '; }';

            // إضافة العنصر إلى الصفحة
            document.head.appendChild(styleElement);

            // طباعة الصفحة
            window.print();

            // إزالة العنصر بعد الطباعة
            setTimeout(function() {
                document.head.removeChild(styleElement);
                document.body.style.overflow = originalOverflow;
                document.body.style.width = originalWidth;
            }, 1000);
        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");
        }
    }

    // وظيفة تصدير البيانات إلى Excel
    function exportToExcel() {
        // تحديد الجدول المراد تصديره
        var table = document.querySelector('.table');
        if (!table) return;

        // إنشاء مصفوفة لتخزين البيانات
        var data = [];

        // إضافة عناوين الأعمدة
        var headers = [];
        table.querySelectorAll('thead th').forEach(function(th) {
            headers.push(th.innerText);
        });
        data.push(headers);

        // إضافة بيانات الصفوف
        table.querySelectorAll('tbody tr').forEach(function(tr) {
            var row = [];
            tr.querySelectorAll('td').forEach(function(td) {
                row.push(td.innerText);
            });
            data.push(row);
        });

        // إضافة صف الإجمالي
        var footerRow = [];
        table.querySelectorAll('tfoot tr').forEach(function(tr) {
            tr.querySelectorAll('th, td').forEach(function(cell) {
                footerRow.push(cell.innerText);
            });
        });
        if (footerRow.length > 0) {
            data.push(footerRow);
        }

        // إنشاء ورقة عمل Excel
        var ws = XLSX.utils.aoa_to_sheet(data);

        // إنشاء مصنف Excel
        var wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "تقرير الأدوية");

        // تحديد اسم الملف
        var fileName = "تقرير_الأدوية_" + new Date().toISOString().slice(0, 10) + ".xlsx";

        // تنزيل الملف
        XLSX.writeFile(wb, fileName);
    }
</script>
{% endblock %}
